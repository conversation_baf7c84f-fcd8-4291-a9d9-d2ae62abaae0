module.exports = {

"[project]/.next-internal/server/app/api/generate-revo-2.0/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/ai/revo-2.0-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 2.0 Service - Next-Generation AI Content Creation
 * Uses Gemini 2.5 Flash Image Preview for enhanced content generation
 */ __turbopack_context__.s({
    "generateWithRevo20": (()=>generateWithRevo20),
    "testRevo20Availability": (()=>testRevo20Availability)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
;
// Initialize AI clients
const ai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](process.env.GEMINI_API_KEY);
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
// Revo 2.0 uses Gemini 2.5 Flash Image Preview (following official docs)
const REVO_2_0_MODEL = 'gemini-2.5-flash-image-preview';
/**
 * Generate enhanced creative concept using GPT-4
 */ async function generateCreativeConcept(options) {
    const { businessType, platform, brandProfile, visualStyle = 'modern' } = options;
    const prompt = `You are a world-class creative director specializing in ${businessType} businesses. 
Create an authentic, locally-relevant creative concept for ${platform} that feels genuine and relatable.

Business Context:
- Type: ${businessType}
- Platform: ${platform}
- Style: ${visualStyle}
- Location: ${brandProfile.location || 'Global'}
- Brand: ${brandProfile.businessName || businessType}

Create a concept that:
1. Feels authentic and locally relevant
2. Uses relatable human experiences
3. Connects emotionally with the target audience
4. Incorporates cultural nuances naturally
5. Avoids generic corporate messaging

Return your response in this exact JSON format:
{
  "concept": "Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)",
  "catchwords": ["word1", "word2", "word3", "word4", "word5"],
  "visualDirection": "Authentic visual direction that feels real and community-focused (2-3 sentences)",
  "designElements": ["element1", "element2", "element3", "element4"],
  "colorSuggestions": ["#color1", "#color2", "#color3"],
  "moodKeywords": ["mood1", "mood2", "mood3", "mood4"],
  "targetEmotions": ["emotion1", "emotion2", "emotion3"]
}`;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        temperature: 0.8,
        max_tokens: 1000
    });
    try {
        return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
        console.error('Failed to parse creative concept:', error);
        return {
            concept: `Professional ${businessType} content for ${platform}`,
            catchwords: [
                'quality',
                'professional',
                'trusted',
                'local',
                'expert'
            ],
            visualDirection: 'Clean, professional design with modern aesthetics',
            designElements: [
                'clean typography',
                'professional imagery',
                'brand colors',
                'modern layout'
            ],
            colorSuggestions: [
                '#2563eb',
                '#1f2937',
                '#f8fafc'
            ],
            moodKeywords: [
                'professional',
                'trustworthy',
                'modern',
                'clean'
            ],
            targetEmotions: [
                'trust',
                'confidence',
                'reliability'
            ]
        };
    }
}
async function generateWithRevo20(options) {
    const startTime = Date.now();
    console.log('🚀 Starting Revo 2.0 generation...');
    console.log('📋 Options:', {
        businessType: options.businessType,
        platform: options.platform,
        visualStyle: options.visualStyle,
        aspectRatio: options.aspectRatio
    });
    try {
        // Step 1: Generate creative concept
        console.log('🎨 Generating creative concept...');
        const concept = await generateCreativeConcept(options);
        console.log('✅ Creative concept generated');
        // Step 2: Build enhanced prompt
        const enhancedPrompt = buildEnhancedPrompt(options, concept);
        console.log('📝 Enhanced prompt built');
        // Step 3: Generate image with Gemini 2.5 Flash Image Preview
        console.log('🖼️ Generating image with Revo 2.0...');
        const imageResult = await generateImageWithGemini(enhancedPrompt, options);
        console.log('✅ Image generated successfully');
        // Step 4: Generate caption and hashtags
        console.log('📱 Generating caption and hashtags...');
        const contentResult = await generateCaptionAndHashtags(options, concept);
        console.log('✅ Caption and hashtags generated');
        const processingTime = Date.now() - startTime;
        console.log(`🎯 Revo 2.0 generation completed in ${processingTime}ms`);
        return {
            imageUrl: imageResult.imageUrl,
            model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',
            qualityScore: 9.2,
            processingTime,
            enhancementsApplied: [
                'Creative concept generation',
                'Enhanced prompt engineering',
                'Brand consistency optimization',
                'Platform-specific formatting',
                'Cultural relevance integration'
            ],
            caption: contentResult.caption,
            hashtags: contentResult.hashtags
        };
    } catch (error) {
        console.error('❌ Revo 2.0 generation failed:', error);
        throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Build enhanced prompt for Revo 2.0
 */ function buildEnhancedPrompt(options, concept) {
    const { businessType, platform, brandProfile, aspectRatio = '1:1', visualStyle = 'modern' } = options;
    return `Create a high-quality, professional ${businessType} design for ${platform}.

CREATIVE CONCEPT: ${concept.concept}

VISUAL DIRECTION: ${concept.visualDirection}

DESIGN REQUIREMENTS:
- Style: ${visualStyle}, premium quality
- Aspect Ratio: ${aspectRatio}
- Platform: ${platform} optimized
- Business: ${brandProfile.businessName || businessType}
- Location: ${brandProfile.location || 'Professional setting'}

DESIGN ELEMENTS:
${concept.designElements.map((element)=>`- ${element}`).join('\n')}

MOOD & EMOTIONS:
- Target emotions: ${concept.targetEmotions.join(', ')}
- Mood keywords: ${concept.moodKeywords.join(', ')}

BRAND INTEGRATION:
- Colors: ${concept.colorSuggestions.join(', ')}
- Business name: ${brandProfile.businessName || businessType}
- Professional, trustworthy appearance

QUALITY STANDARDS:
- Ultra-high resolution and clarity
- Professional composition
- Perfect typography and text rendering
- Balanced color scheme
- Platform-optimized dimensions
- Brand consistency throughout

Create a stunning, professional design that captures the essence of this ${businessType} business.`;
}
/**
 * Generate image using Gemini 2.5 Flash Image Preview
 */ async function generateImageWithGemini(prompt, options) {
    const maxRetries = 3;
    let lastError;
    let response;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            console.log(`🔄 Attempt ${attempt}/${maxRetries} for Revo 2.0 generation...`);
            const model = ai.getGenerativeModel({
                model: REVO_2_0_MODEL
            });
            response = await model.generateContent(prompt);
            console.log('✅ Revo 2.0 generation successful!');
            break;
        } catch (error) {
            lastError = error;
            console.log(`❌ Attempt ${attempt} failed:`, error?.message || error);
            if (attempt === maxRetries) {
                break;
            }
            const waitTime = Math.pow(2, attempt) * 1000;
            console.log(`⏳ Waiting ${waitTime / 1000}s before retry...`);
            await new Promise((resolve)=>setTimeout(resolve, waitTime));
        }
    }
    if (!response) {
        throw new Error(`Revo 2.0 generation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);
    }
    // Extract image from response
    const parts = response.candidates?.[0]?.content?.parts || [];
    for (const part of parts){
        if (part.inlineData) {
            const imageData = part.inlineData.data;
            const mimeType = part.inlineData.mimeType;
            return {
                imageUrl: `data:${mimeType};base64,${imageData}`
            };
        }
    }
    throw new Error('No image data found in Revo 2.0 response');
}
/**
 * Generate caption and hashtags
 */ async function generateCaptionAndHashtags(options, concept) {
    const { businessType, platform, brandProfile } = options;
    const prompt = `Create engaging ${platform} content for a ${businessType} business.

Business: ${brandProfile.businessName || businessType}
Location: ${brandProfile.location || 'Local area'}
Concept: ${concept.concept}
Catchwords: ${concept.catchwords.join(', ')}

Create:
1. A catchy, engaging caption (2-3 sentences max)
2. Relevant hashtags (8-12 hashtags)

Make it authentic, locally relevant, and engaging for ${platform}.

Format as JSON:
{
  "caption": "Your engaging caption here",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3"]
}`;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        temperature: 0.7,
        max_tokens: 500
    });
    try {
        const result = JSON.parse(response.choices[0].message.content || '{}');
        return {
            caption: result.caption || `Professional ${businessType} services in ${brandProfile.location || 'your area'}`,
            hashtags: result.hashtags || [
                `#${businessType.replace(/\s+/g, '')}`,
                '#professional',
                '#local',
                '#quality'
            ]
        };
    } catch (error) {
        console.error('Failed to parse caption/hashtags:', error);
        return {
            caption: `Professional ${businessType} services in ${brandProfile.location || 'your area'}`,
            hashtags: [
                `#${businessType.replace(/\s+/g, '')}`,
                '#professional',
                '#local',
                '#quality'
            ]
        };
    }
}
async function testRevo20Availability() {
    try {
        console.log('🧪 Testing Revo 2.0 (Gemini 2.5 Flash Image Preview) availability...');
        const model = ai.getGenerativeModel({
            model: REVO_2_0_MODEL
        });
        const response = await model.generateContent('Create a simple test image with the text "Revo 2.0 Test" on a modern gradient background');
        const parts = response.candidates?.[0]?.content?.parts || [];
        let hasImage = false;
        for (const part of parts){
            if (part.inlineData) {
                console.log('🖼️ Image data found:', part.inlineData.mimeType);
                hasImage = true;
            }
        }
        if (hasImage) {
            console.log('✅ Revo 2.0 is available and working perfectly!');
            return true;
        } else {
            console.log('⚠️ Revo 2.0 responded but no image found');
            return false;
        }
    } catch (error) {
        console.error('❌ Revo 2.0 test failed:', error);
        return false;
    }
}
}}),
"[project]/src/app/api/generate-revo-2.0/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 2.0 Generation API Route
 * Uses Gemini 2.5 Flash Image Preview for next-generation content creation
 */ __turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/revo-2.0-service.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        console.log('🚀 API: Starting Revo 2.0 generation...');
        const body = await request.json();
        const { businessType, platform, brandProfile, visualStyle, imageText, aspectRatio, includePeopleInDesigns, useLocalLanguage } = body;
        // Validate required fields
        if (!businessType || !platform || !brandProfile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Missing required fields: businessType, platform, brandProfile'
            }, {
                status: 400
            });
        }
        console.log('📋 API: Generation parameters:', {
            businessType,
            platform,
            visualStyle: visualStyle || 'modern',
            aspectRatio: aspectRatio || '1:1'
        });
        // Generate content with Revo 2.0
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateWithRevo20"])({
            businessType,
            platform,
            visualStyle: visualStyle || 'modern',
            imageText: imageText || `${brandProfile.businessName || businessType} - Premium Content`,
            brandProfile,
            aspectRatio,
            includePeopleInDesigns,
            useLocalLanguage
        });
        console.log('✅ API: Revo 2.0 generation completed successfully');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            imageUrl: result.imageUrl,
            model: result.model,
            qualityScore: result.qualityScore,
            processingTime: result.processingTime,
            enhancementsApplied: result.enhancementsApplied,
            caption: result.caption,
            hashtags: result.hashtags,
            message: 'Revo 2.0 content generated successfully'
        });
    } catch (error) {
        console.error('❌ API: Revo 2.0 generation error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Revo 2.0 generation failed'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        message: 'Revo 2.0 Generation API',
        description: 'Use POST method to generate content with Revo 2.0',
        requiredFields: [
            'businessType',
            'platform',
            'brandProfile'
        ],
        optionalFields: [
            'visualStyle',
            'imageText',
            'aspectRatio'
        ],
        model: 'Gemini 2.5 Flash Image Preview',
        version: '2.0.0'
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__96176cc8._.js.map