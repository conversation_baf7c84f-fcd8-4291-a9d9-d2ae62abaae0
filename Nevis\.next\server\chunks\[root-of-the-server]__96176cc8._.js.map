{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-2.0-service.ts"], "sourcesContent": ["/**\n * Revo 2.0 Service - Next-Generation AI Content Creation\n * Uses Gemini 2.5 Flash Image Preview for enhanced content generation\n */\n\nimport { GoogleGenerativeAI } from '@google/generative-ai';\nimport OpenAI from 'openai';\nimport type { BrandProfile, Platform } from '@/lib/types';\n\n// Initialize AI clients\nconst ai = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);\nconst openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });\n\n// Revo 2.0 uses Gemini 2.5 Flash Image Preview (following official docs)\nconst REVO_2_0_MODEL = 'gemini-2.5-flash-image-preview';\n\nexport interface Revo20GenerationOptions {\n  businessType: string;\n  platform: Platform;\n  visualStyle?: 'modern' | 'minimalist' | 'bold' | 'elegant' | 'playful' | 'professional';\n  imageText?: string;\n  brandProfile: BrandProfile;\n  aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\n  includePeopleInDesigns?: boolean;\n  useLocalLanguage?: boolean;\n}\n\nexport interface Revo20GenerationResult {\n  imageUrl: string;\n  model: string;\n  qualityScore: number;\n  processingTime: number;\n  enhancementsApplied: string[];\n  caption: string;\n  hashtags: string[];\n}\n\n/**\n * Generate enhanced creative concept using GPT-4\n */\nasync function generateCreativeConcept(options: Revo20GenerationOptions): Promise<any> {\n  const { businessType, platform, brandProfile, visualStyle = 'modern' } = options;\n  \n  const prompt = `You are a world-class creative director specializing in ${businessType} businesses. \nCreate an authentic, locally-relevant creative concept for ${platform} that feels genuine and relatable.\n\nBusiness Context:\n- Type: ${businessType}\n- Platform: ${platform}\n- Style: ${visualStyle}\n- Location: ${brandProfile.location || 'Global'}\n- Brand: ${brandProfile.businessName || businessType}\n\nCreate a concept that:\n1. Feels authentic and locally relevant\n2. Uses relatable human experiences\n3. Connects emotionally with the target audience\n4. Incorporates cultural nuances naturally\n5. Avoids generic corporate messaging\n\nReturn your response in this exact JSON format:\n{\n  \"concept\": \"Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)\",\n  \"catchwords\": [\"word1\", \"word2\", \"word3\", \"word4\", \"word5\"],\n  \"visualDirection\": \"Authentic visual direction that feels real and community-focused (2-3 sentences)\",\n  \"designElements\": [\"element1\", \"element2\", \"element3\", \"element4\"],\n  \"colorSuggestions\": [\"#color1\", \"#color2\", \"#color3\"],\n  \"moodKeywords\": [\"mood1\", \"mood2\", \"mood3\", \"mood4\"],\n  \"targetEmotions\": [\"emotion1\", \"emotion2\", \"emotion3\"]\n}`;\n\n  const response = await openai.chat.completions.create({\n    model: 'gpt-4o',\n    messages: [{ role: 'user', content: prompt }],\n    temperature: 0.8,\n    max_tokens: 1000\n  });\n\n  try {\n    return JSON.parse(response.choices[0].message.content || '{}');\n  } catch (error) {\n    console.error('Failed to parse creative concept:', error);\n    return {\n      concept: `Professional ${businessType} content for ${platform}`,\n      catchwords: ['quality', 'professional', 'trusted', 'local', 'expert'],\n      visualDirection: 'Clean, professional design with modern aesthetics',\n      designElements: ['clean typography', 'professional imagery', 'brand colors', 'modern layout'],\n      colorSuggestions: ['#2563eb', '#1f2937', '#f8fafc'],\n      moodKeywords: ['professional', 'trustworthy', 'modern', 'clean'],\n      targetEmotions: ['trust', 'confidence', 'reliability']\n    };\n  }\n}\n\n/**\n * Generate content with Revo 2.0 (Gemini 2.5 Flash Image Preview)\n */\nexport async function generateWithRevo20(options: Revo20GenerationOptions): Promise<Revo20GenerationResult> {\n  const startTime = Date.now();\n  console.log('🚀 Starting Revo 2.0 generation...');\n  console.log('📋 Options:', {\n    businessType: options.businessType,\n    platform: options.platform,\n    visualStyle: options.visualStyle,\n    aspectRatio: options.aspectRatio\n  });\n\n  try {\n    // Step 1: Generate creative concept\n    console.log('🎨 Generating creative concept...');\n    const concept = await generateCreativeConcept(options);\n    console.log('✅ Creative concept generated');\n\n    // Step 2: Build enhanced prompt\n    const enhancedPrompt = buildEnhancedPrompt(options, concept);\n    console.log('📝 Enhanced prompt built');\n\n    // Step 3: Generate image with Gemini 2.5 Flash Image Preview\n    console.log('🖼️ Generating image with Revo 2.0...');\n    const imageResult = await generateImageWithGemini(enhancedPrompt, options);\n    console.log('✅ Image generated successfully');\n\n    // Step 4: Generate caption and hashtags\n    console.log('📱 Generating caption and hashtags...');\n    const contentResult = await generateCaptionAndHashtags(options, concept);\n    console.log('✅ Caption and hashtags generated');\n\n    const processingTime = Date.now() - startTime;\n    console.log(`🎯 Revo 2.0 generation completed in ${processingTime}ms`);\n\n    return {\n      imageUrl: imageResult.imageUrl,\n      model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',\n      qualityScore: 9.2,\n      processingTime,\n      enhancementsApplied: [\n        'Creative concept generation',\n        'Enhanced prompt engineering',\n        'Brand consistency optimization',\n        'Platform-specific formatting',\n        'Cultural relevance integration'\n      ],\n      caption: contentResult.caption,\n      hashtags: contentResult.hashtags\n    };\n\n  } catch (error) {\n    console.error('❌ Revo 2.0 generation failed:', error);\n    throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Build enhanced prompt for Revo 2.0\n */\nfunction buildEnhancedPrompt(options: Revo20GenerationOptions, concept: any): string {\n  const { businessType, platform, brandProfile, aspectRatio = '1:1', visualStyle = 'modern' } = options;\n  \n  return `Create a high-quality, professional ${businessType} design for ${platform}.\n\nCREATIVE CONCEPT: ${concept.concept}\n\nVISUAL DIRECTION: ${concept.visualDirection}\n\nDESIGN REQUIREMENTS:\n- Style: ${visualStyle}, premium quality\n- Aspect Ratio: ${aspectRatio}\n- Platform: ${platform} optimized\n- Business: ${brandProfile.businessName || businessType}\n- Location: ${brandProfile.location || 'Professional setting'}\n\nDESIGN ELEMENTS:\n${concept.designElements.map((element: string) => `- ${element}`).join('\\n')}\n\nMOOD & EMOTIONS:\n- Target emotions: ${concept.targetEmotions.join(', ')}\n- Mood keywords: ${concept.moodKeywords.join(', ')}\n\nBRAND INTEGRATION:\n- Colors: ${concept.colorSuggestions.join(', ')}\n- Business name: ${brandProfile.businessName || businessType}\n- Professional, trustworthy appearance\n\nQUALITY STANDARDS:\n- Ultra-high resolution and clarity\n- Professional composition\n- Perfect typography and text rendering\n- Balanced color scheme\n- Platform-optimized dimensions\n- Brand consistency throughout\n\nCreate a stunning, professional design that captures the essence of this ${businessType} business.`;\n}\n\n/**\n * Generate image using Gemini 2.5 Flash Image Preview\n */\nasync function generateImageWithGemini(prompt: string, options: Revo20GenerationOptions): Promise<{ imageUrl: string }> {\n  const maxRetries = 3;\n  let lastError: any;\n  let response: any;\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      console.log(`🔄 Attempt ${attempt}/${maxRetries} for Revo 2.0 generation...`);\n      const model = ai.getGenerativeModel({ model: REVO_2_0_MODEL });\n      response = await model.generateContent(prompt);\n      console.log('✅ Revo 2.0 generation successful!');\n      break;\n    } catch (error: any) {\n      lastError = error;\n      console.log(`❌ Attempt ${attempt} failed:`, error?.message || error);\n\n      if (attempt === maxRetries) {\n        break;\n      }\n\n      const waitTime = Math.pow(2, attempt) * 1000;\n      console.log(`⏳ Waiting ${waitTime / 1000}s before retry...`);\n      await new Promise(resolve => setTimeout(resolve, waitTime));\n    }\n  }\n\n  if (!response) {\n    throw new Error(`Revo 2.0 generation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);\n  }\n\n  // Extract image from response\n  const parts = response.candidates?.[0]?.content?.parts || [];\n  for (const part of parts) {\n    if (part.inlineData) {\n      const imageData = part.inlineData.data;\n      const mimeType = part.inlineData.mimeType;\n      return {\n        imageUrl: `data:${mimeType};base64,${imageData}`\n      };\n    }\n  }\n\n  throw new Error('No image data found in Revo 2.0 response');\n}\n\n/**\n * Generate caption and hashtags\n */\nasync function generateCaptionAndHashtags(options: Revo20GenerationOptions, concept: any): Promise<{ caption: string; hashtags: string[] }> {\n  const { businessType, platform, brandProfile } = options;\n  \n  const prompt = `Create engaging ${platform} content for a ${businessType} business.\n\nBusiness: ${brandProfile.businessName || businessType}\nLocation: ${brandProfile.location || 'Local area'}\nConcept: ${concept.concept}\nCatchwords: ${concept.catchwords.join(', ')}\n\nCreate:\n1. A catchy, engaging caption (2-3 sentences max)\n2. Relevant hashtags (8-12 hashtags)\n\nMake it authentic, locally relevant, and engaging for ${platform}.\n\nFormat as JSON:\n{\n  \"caption\": \"Your engaging caption here\",\n  \"hashtags\": [\"hashtag1\", \"hashtag2\", \"hashtag3\"]\n}`;\n\n  const response = await openai.chat.completions.create({\n    model: 'gpt-4o',\n    messages: [{ role: 'user', content: prompt }],\n    temperature: 0.7,\n    max_tokens: 500\n  });\n\n  try {\n    const result = JSON.parse(response.choices[0].message.content || '{}');\n    return {\n      caption: result.caption || `Professional ${businessType} services in ${brandProfile.location || 'your area'}`,\n      hashtags: result.hashtags || [`#${businessType.replace(/\\s+/g, '')}`, '#professional', '#local', '#quality']\n    };\n  } catch (error) {\n    console.error('Failed to parse caption/hashtags:', error);\n    return {\n      caption: `Professional ${businessType} services in ${brandProfile.location || 'your area'}`,\n      hashtags: [`#${businessType.replace(/\\s+/g, '')}`, '#professional', '#local', '#quality']\n    };\n  }\n}\n\n/**\n * Test Revo 2.0 availability\n */\nexport async function testRevo20Availability(): Promise<boolean> {\n  try {\n    console.log('🧪 Testing Revo 2.0 (Gemini 2.5 Flash Image Preview) availability...');\n    \n    const model = ai.getGenerativeModel({ model: REVO_2_0_MODEL });\n    const response = await model.generateContent('Create a simple test image with the text \"Revo 2.0 Test\" on a modern gradient background');\n\n    const parts = response.candidates?.[0]?.content?.parts || [];\n    let hasImage = false;\n\n    for (const part of parts) {\n      if (part.inlineData) {\n        console.log('🖼️ Image data found:', part.inlineData.mimeType);\n        hasImage = true;\n      }\n    }\n\n    if (hasImage) {\n      console.log('✅ Revo 2.0 is available and working perfectly!');\n      return true;\n    } else {\n      console.log('⚠️ Revo 2.0 responded but no image found');\n      return false;\n    }\n\n  } catch (error) {\n    console.error('❌ Revo 2.0 test failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;;;AAGA,wBAAwB;AACxB,MAAM,KAAK,IAAI,gKAAA,CAAA,qBAAkB,CAAC,QAAQ,GAAG,CAAC,cAAc;AAC5D,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAAE,QAAQ,QAAQ,GAAG,CAAC,cAAc;AAAE;AAEhE,yEAAyE;AACzE,MAAM,iBAAiB;AAuBvB;;CAEC,GACD,eAAe,wBAAwB,OAAgC;IACrE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,QAAQ,EAAE,GAAG;IAEzE,MAAM,SAAS,CAAC,wDAAwD,EAAE,aAAa;2DAC9B,EAAE,SAAS;;;QAG9D,EAAE,aAAa;YACX,EAAE,SAAS;SACd,EAAE,YAAY;YACX,EAAE,aAAa,QAAQ,IAAI,SAAS;SACvC,EAAE,aAAa,YAAY,IAAI,aAAa;;;;;;;;;;;;;;;;;;CAkBpD,CAAC;IAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAO;SAAE;QAC7C,aAAa;QACb,YAAY;IACd;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YACL,SAAS,CAAC,aAAa,EAAE,aAAa,aAAa,EAAE,UAAU;YAC/D,YAAY;gBAAC;gBAAW;gBAAgB;gBAAW;gBAAS;aAAS;YACrE,iBAAiB;YACjB,gBAAgB;gBAAC;gBAAoB;gBAAwB;gBAAgB;aAAgB;YAC7F,kBAAkB;gBAAC;gBAAW;gBAAW;aAAU;YACnD,cAAc;gBAAC;gBAAgB;gBAAe;gBAAU;aAAQ;YAChE,gBAAgB;gBAAC;gBAAS;gBAAc;aAAc;QACxD;IACF;AACF;AAKO,eAAe,mBAAmB,OAAgC;IACvE,MAAM,YAAY,KAAK,GAAG;IAC1B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe;QACzB,cAAc,QAAQ,YAAY;QAClC,UAAU,QAAQ,QAAQ;QAC1B,aAAa,QAAQ,WAAW;QAChC,aAAa,QAAQ,WAAW;IAClC;IAEA,IAAI;QACF,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,MAAM,UAAU,MAAM,wBAAwB;QAC9C,QAAQ,GAAG,CAAC;QAEZ,gCAAgC;QAChC,MAAM,iBAAiB,oBAAoB,SAAS;QACpD,QAAQ,GAAG,CAAC;QAEZ,6DAA6D;QAC7D,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,wBAAwB,gBAAgB;QAClE,QAAQ,GAAG,CAAC;QAEZ,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,2BAA2B,SAAS;QAChE,QAAQ,GAAG,CAAC;QAEZ,MAAM,iBAAiB,KAAK,GAAG,KAAK;QACpC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe,EAAE,CAAC;QAErE,OAAO;YACL,UAAU,YAAY,QAAQ;YAC9B,OAAO;YACP,cAAc;YACd;YACA,qBAAqB;gBACnB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS,cAAc,OAAO;YAC9B,UAAU,cAAc,QAAQ;QAClC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC3G;AACF;AAEA;;CAEC,GACD,SAAS,oBAAoB,OAAgC,EAAE,OAAY;IACzE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,KAAK,EAAE,cAAc,QAAQ,EAAE,GAAG;IAE9F,OAAO,CAAC,oCAAoC,EAAE,aAAa,YAAY,EAAE,SAAS;;kBAElE,EAAE,QAAQ,OAAO,CAAC;;kBAElB,EAAE,QAAQ,eAAe,CAAC;;;SAGnC,EAAE,YAAY;gBACP,EAAE,YAAY;YAClB,EAAE,SAAS;YACX,EAAE,aAAa,YAAY,IAAI,aAAa;YAC5C,EAAE,aAAa,QAAQ,IAAI,uBAAuB;;;AAG9D,EAAE,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,UAAoB,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM;;;mBAG1D,EAAE,QAAQ,cAAc,CAAC,IAAI,CAAC,MAAM;iBACtC,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;;;UAGzC,EAAE,QAAQ,gBAAgB,CAAC,IAAI,CAAC,MAAM;iBAC/B,EAAE,aAAa,YAAY,IAAI,aAAa;;;;;;;;;;;yEAWY,EAAE,aAAa,UAAU,CAAC;AACnG;AAEA;;CAEC,GACD,eAAe,wBAAwB,MAAc,EAAE,OAAgC;IACrF,MAAM,aAAa;IACnB,IAAI;IACJ,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,WAAW,2BAA2B,CAAC;YAC5E,MAAM,QAAQ,GAAG,kBAAkB,CAAC;gBAAE,OAAO;YAAe;YAC5D,WAAW,MAAM,MAAM,eAAe,CAAC;YACvC,QAAQ,GAAG,CAAC;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,YAAY;YACZ,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,QAAQ,CAAC,EAAE,OAAO,WAAW;YAE9D,IAAI,YAAY,YAAY;gBAC1B;YACF;YAEA,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,WAAW;YACxC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,KAAK,iBAAiB,CAAC;YAC3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,WAAW,WAAW,EAAE,WAAW,WAAW,iBAAiB;IACrH;IAEA,8BAA8B;IAC9B,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;IAC5D,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,UAAU,EAAE;YACnB,MAAM,YAAY,KAAK,UAAU,CAAC,IAAI;YACtC,MAAM,WAAW,KAAK,UAAU,CAAC,QAAQ;YACzC,OAAO;gBACL,UAAU,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,WAAW;YAClD;QACF;IACF;IAEA,MAAM,IAAI,MAAM;AAClB;AAEA;;CAEC,GACD,eAAe,2BAA2B,OAAgC,EAAE,OAAY;IACtF,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG;IAEjD,MAAM,SAAS,CAAC,gBAAgB,EAAE,SAAS,eAAe,EAAE,aAAa;;UAEjE,EAAE,aAAa,YAAY,IAAI,aAAa;UAC5C,EAAE,aAAa,QAAQ,IAAI,aAAa;SACzC,EAAE,QAAQ,OAAO,CAAC;YACf,EAAE,QAAQ,UAAU,CAAC,IAAI,CAAC,MAAM;;;;;;sDAMU,EAAE,SAAS;;;;;;CAMhE,CAAC;IAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAO;SAAE;QAC7C,aAAa;QACb,YAAY;IACd;IAEA,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI;QACjE,OAAO;YACL,SAAS,OAAO,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa,aAAa,EAAE,aAAa,QAAQ,IAAI,aAAa;YAC7G,UAAU,OAAO,QAAQ,IAAI;gBAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;gBAAE;gBAAiB;gBAAU;aAAW;QAC9G;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YACL,SAAS,CAAC,aAAa,EAAE,aAAa,aAAa,EAAE,aAAa,QAAQ,IAAI,aAAa;YAC3F,UAAU;gBAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;gBAAE;gBAAiB;gBAAU;aAAW;QAC3F;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAAE,OAAO;QAAe;QAC5D,MAAM,WAAW,MAAM,MAAM,eAAe,CAAC;QAE7C,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,EAAE;gBACnB,QAAQ,GAAG,CAAC,yBAAyB,KAAK,UAAU,CAAC,QAAQ;gBAC7D,WAAW;YACb;QACF;QAEA,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/generate-revo-2.0/route.ts"], "sourcesContent": ["/**\n * Revo 2.0 Generation API Route\n * Uses Gemini 2.5 Flash Image Preview for next-generation content creation\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { generateWithRevo20 } from '@/ai/revo-2.0-service';\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🚀 API: Starting Revo 2.0 generation...');\n    \n    const body = await request.json();\n    const { \n      businessType, \n      platform, \n      brandProfile, \n      visualStyle, \n      imageText, \n      aspectRatio,\n      includePeopleInDesigns,\n      useLocalLanguage \n    } = body;\n\n    // Validate required fields\n    if (!businessType || !platform || !brandProfile) {\n      return NextResponse.json({\n        success: false,\n        error: 'Missing required fields: businessType, platform, brandProfile'\n      }, { status: 400 });\n    }\n\n    console.log('📋 API: Generation parameters:', {\n      businessType,\n      platform,\n      visualStyle: visualStyle || 'modern',\n      aspectRatio: aspectRatio || '1:1'\n    });\n\n    // Generate content with Revo 2.0\n    const result = await generateWithRevo20({\n      businessType,\n      platform,\n      visualStyle: visualStyle || 'modern',\n      imageText: imageText || `${brandProfile.businessName || businessType} - Premium Content`,\n      brandProfile,\n      aspectRatio,\n      includePeopleInDesigns,\n      useLocalLanguage\n    });\n\n    console.log('✅ API: Revo 2.0 generation completed successfully');\n\n    return NextResponse.json({\n      success: true,\n      imageUrl: result.imageUrl,\n      model: result.model,\n      qualityScore: result.qualityScore,\n      processingTime: result.processingTime,\n      enhancementsApplied: result.enhancementsApplied,\n      caption: result.caption,\n      hashtags: result.hashtags,\n      message: 'Revo 2.0 content generated successfully'\n    });\n\n  } catch (error) {\n    console.error('❌ API: Revo 2.0 generation error:', error);\n\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      message: 'Revo 2.0 generation failed'\n    }, { status: 500 });\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({\n    message: 'Revo 2.0 Generation API',\n    description: 'Use POST method to generate content with Revo 2.0',\n    requiredFields: ['businessType', 'platform', 'brandProfile'],\n    optionalFields: ['visualStyle', 'imageText', 'aspectRatio'],\n    model: 'Gemini 2.5 Flash Image Preview',\n    version: '2.0.0'\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,SAAS,EACT,WAAW,EACX,sBAAsB,EACtB,gBAAgB,EACjB,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC5C;YACA;YACA,aAAa,eAAe;YAC5B,aAAa,eAAe;QAC9B;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;YACtC;YACA;YACA,aAAa,eAAe;YAC5B,WAAW,aAAa,GAAG,aAAa,YAAY,IAAI,aAAa,kBAAkB,CAAC;YACxF;YACA;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,OAAO,QAAQ;YACzB,OAAO,OAAO,KAAK;YACnB,cAAc,OAAO,YAAY;YACjC,gBAAgB,OAAO,cAAc;YACrC,qBAAqB,OAAO,mBAAmB;YAC/C,SAAS,OAAO,OAAO;YACvB,UAAU,OAAO,QAAQ;YACzB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,aAAa;QACb,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,gBAAgB;YAAC;YAAe;YAAa;SAAc;QAC3D,OAAO;QACP,SAAS;IACX;AACF", "debugId": null}}]}