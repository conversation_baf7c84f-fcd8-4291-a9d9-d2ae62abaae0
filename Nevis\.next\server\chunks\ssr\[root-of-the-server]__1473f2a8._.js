module.exports = {

"[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Capabilities Configuration
 * Defines what each model version can do
 */ __turbopack_context__.s({
    "capabilityMatrix": (()=>capabilityMatrix),
    "featureAvailability": (()=>featureAvailability),
    "getCapabilityLevel": (()=>getCapabilityLevel),
    "getMaxQualityForPlatform": (()=>getMaxQualityForPlatform),
    "getModelsByFeature": (()=>getModelsByFeature),
    "getPlatformCapabilities": (()=>getPlatformCapabilities),
    "getSupportedAspectRatios": (()=>getSupportedAspectRatios),
    "hasCapability": (()=>hasCapability),
    "hasFeature": (()=>hasFeature),
    "modelCapabilities": (()=>modelCapabilities),
    "platformCapabilities": (()=>platformCapabilities)
});
const modelCapabilities = {
    'revo-1.0': {
        // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: false,
        aspectRatios: [
            '1:1'
        ],
        maxQuality: 9,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        perfectTextRendering: true,
        highResolution: true // NEW: 2048x2048 support
    },
    'revo-1.5': {
        // Enhanced model with advanced features
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16'
        ],
        maxQuality: 8,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true // Real-time context and trends
    },
    'revo-2.0': {
        // Premium Next-Gen AI model
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16',
            '4:3',
            '3:4'
        ],
        maxQuality: 10,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        characterConsistency: true,
        intelligentEditing: true,
        multimodalReasoning: true // NEW: Advanced visual context understanding
    }
};
const capabilityMatrix = {
    contentGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    designGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    videoGeneration: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'none'
    },
    artifactSupport: {
        'revo-1.0': 'none',
        'revo-1.5': 'full',
        'revo-2.0': 'premium'
    },
    brandConsistency: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'advanced',
        'revo-2.0': 'perfect'
    },
    characterConsistency: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    },
    intelligentEditing: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    }
};
const featureAvailability = {
    // Content features
    hashtagGeneration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    catchyWords: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    subheadlines: [
        'revo-1.5',
        'revo-2.0'
    ],
    callToAction: [
        'revo-1.5',
        'revo-2.0'
    ],
    contentVariants: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Design features
    logoIntegration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    brandColors: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    designExamples: [
        'revo-1.5',
        'revo-2.0'
    ],
    textOverlay: [
        'revo-1.5',
        'revo-2.0'
    ],
    multipleAspectRatios: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Advanced features
    realTimeContext: [
        'revo-1.5',
        'revo-2.0'
    ],
    trendingTopics: [
        'revo-1.5',
        'revo-2.0'
    ],
    marketIntelligence: [
        'revo-1.5',
        'revo-2.0'
    ],
    competitorAnalysis: [
        'revo-2.0'
    ],
    // Revo 2.0 exclusive features
    characterConsistency: [
        'revo-2.0'
    ],
    intelligentEditing: [
        'revo-2.0'
    ],
    inpainting: [
        'revo-2.0'
    ],
    outpainting: [
        'revo-2.0'
    ],
    multimodalReasoning: [
        'revo-2.0'
    ],
    // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)
    perfectTextRendering: [
        'revo-1.0',
        'revo-2.0'
    ],
    highResolution: [
        'revo-1.0',
        'revo-2.0'
    ],
    // Artifact features
    artifactReference: [
        'revo-1.5'
    ],
    exactUseArtifacts: [
        'revo-1.5'
    ],
    textOverlayArtifacts: [
        'revo-1.5'
    ]
};
const platformCapabilities = {
    Instagram: {
        'revo-1.0': {
            aspectRatios: [
                '1:1'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'hashtags'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '1:1',
                '9:16'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'hashtags',
                'stories',
                'reels-ready'
            ]
        }
    },
    Facebook: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'page-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'page-posts',
                'stories'
            ]
        }
    },
    Twitter: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'tweets'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'tweets',
                'threads'
            ]
        }
    },
    LinkedIn: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'professional-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'professional-posts',
                'articles'
            ]
        }
    }
};
function hasCapability(modelId, capability) {
    return modelCapabilities[modelId][capability];
}
function getCapabilityLevel(modelId, capability) {
    return capabilityMatrix[capability][modelId];
}
function hasFeature(modelId, feature) {
    return featureAvailability[feature].includes(modelId);
}
function getModelsByFeature(feature) {
    return [
        ...featureAvailability[feature]
    ];
}
function getPlatformCapabilities(modelId, platform) {
    return platformCapabilities[platform]?.[modelId] || null;
}
function getMaxQualityForPlatform(modelId, platform) {
    const platformCaps = getPlatformCapabilities(modelId, platform);
    return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;
}
function getSupportedAspectRatios(modelId, platform) {
    if (platform) {
        const platformCaps = getPlatformCapabilities(modelId, platform);
        return platformCaps?.aspectRatios ? [
            ...platformCaps.aspectRatios
        ] : [
            ...modelCapabilities[modelId].aspectRatios
        ];
    }
    return [
        ...modelCapabilities[modelId].aspectRatios
    ];
}
}}),
"[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Pricing Configuration
 * Defines credit costs and pricing tiers for each model
 */ __turbopack_context__.s({
    "creditPackages": (()=>creditPackages),
    "getAllPricing": (()=>getAllPricing),
    "getCheapestModel": (()=>getCheapestModel),
    "getModelPricing": (()=>getModelPricing),
    "getModelsByTier": (()=>getModelsByTier),
    "getMostExpensiveModel": (()=>getMostExpensiveModel),
    "modelPricing": (()=>modelPricing),
    "pricingDisplay": (()=>pricingDisplay),
    "pricingTiers": (()=>pricingTiers),
    "usageCalculations": (()=>usageCalculations)
});
const modelPricing = {
    'revo-1.0': {
        creditsPerGeneration: 1.5,
        creditsPerDesign: 1.5,
        creditsPerVideo: 0,
        tier: 'enhanced' // Upgraded from basic
    },
    'revo-1.5': {
        creditsPerGeneration: 2,
        creditsPerDesign: 2,
        creditsPerVideo: 0,
        tier: 'premium'
    },
    'revo-2.0': {
        creditsPerGeneration: 5,
        creditsPerDesign: 5,
        creditsPerVideo: 0,
        tier: 'premium'
    }
};
const pricingTiers = {
    basic: {
        name: 'Basic',
        description: 'Reliable and cost-effective',
        maxCreditsPerGeneration: 2,
        features: [
            'Standard quality generation',
            'Basic brand consistency',
            'Core platform support',
            'Standard processing speed'
        ],
        recommendedFor: [
            'Small businesses',
            'Personal brands',
            'Budget-conscious users',
            'Basic content needs'
        ]
    },
    premium: {
        name: 'Premium',
        description: 'Enhanced features and quality',
        maxCreditsPerGeneration: 10,
        features: [
            'Enhanced quality generation',
            'Advanced brand consistency',
            'Full platform support',
            'Artifact integration',
            'Real-time context',
            'Trending topics',
            'Multiple aspect ratios'
        ],
        recommendedFor: [
            'Growing businesses',
            'Marketing agencies',
            'Content creators',
            'Professional brands'
        ]
    },
    enterprise: {
        name: 'Enterprise',
        description: 'Maximum quality and features',
        maxCreditsPerGeneration: 20,
        features: [
            'Premium quality generation',
            '4K resolution support',
            'Perfect text rendering',
            'Advanced style controls',
            'Priority processing',
            'Dedicated support',
            'Custom integrations'
        ],
        recommendedFor: [
            'Large enterprises',
            'Premium brands',
            'High-volume users',
            'Quality-focused campaigns'
        ]
    }
};
const creditPackages = {
    starter: {
        name: 'Starter Pack',
        credits: 50,
        price: 9.99,
        pricePerCredit: 0.20,
        bestFor: 'revo-1.0',
        estimatedGenerations: {
            'revo-1.0': 50,
            'revo-1.5': 25,
            'imagen-4': 5
        }
    },
    professional: {
        name: 'Professional Pack',
        credits: 200,
        price: 29.99,
        pricePerCredit: 0.15,
        bestFor: 'revo-1.5',
        estimatedGenerations: {
            'revo-1.0': 200,
            'revo-1.5': 100,
            'imagen-4': 20
        }
    },
    business: {
        name: 'Business Pack',
        credits: 500,
        price: 59.99,
        pricePerCredit: 0.12,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 500,
            'revo-1.5': 250,
            'imagen-4': 50
        }
    },
    enterprise: {
        name: 'Enterprise Pack',
        credits: 1000,
        price: 99.99,
        pricePerCredit: 0.10,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 1000,
            'revo-1.5': 500,
            'revo-2.0': 200,
            'imagen-4': 100
        }
    }
};
const usageCalculations = {
    // Calculate cost for a specific generation request
    calculateGenerationCost (modelId, type = 'content') {
        const pricing = modelPricing[modelId];
        switch(type){
            case 'content':
                return pricing.creditsPerGeneration;
            case 'design':
                return pricing.creditsPerDesign;
            case 'video':
                return pricing.creditsPerVideo || 0;
            default:
                return pricing.creditsPerGeneration;
        }
    },
    // Calculate total cost for multiple generations
    calculateBatchCost (requests) {
        return requests.reduce((total, request)=>{
            return total + this.calculateGenerationCost(request.modelId, request.type);
        }, 0);
    },
    // Estimate monthly cost based on usage patterns
    estimateMonthlyCost (usage) {
        const pricing = modelPricing[usage.modelId];
        const dailyCost = usage.generationsPerDay * pricing.creditsPerGeneration + usage.designsPerDay * pricing.creditsPerDesign + (usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0);
        const monthlyCost = dailyCost * 30;
        // Recommend package based on monthly cost
        let recommendedPackage = 'starter';
        if (monthlyCost > 400) recommendedPackage = 'enterprise';
        else if (monthlyCost > 150) recommendedPackage = 'business';
        else if (monthlyCost > 50) recommendedPackage = 'professional';
        return {
            dailyCost,
            monthlyCost,
            recommendedPackage
        };
    },
    // Check if user has enough credits for a request
    canAfford (userCredits, modelId, type = 'content') {
        const cost = this.calculateGenerationCost(modelId, type);
        return userCredits >= cost;
    },
    // Get the best model within budget
    getBestModelForBudget (availableCredits, type = 'content') {
        const affordableModels = [];
        for (const [modelId, pricing] of Object.entries(modelPricing)){
            const cost = type === 'content' ? pricing.creditsPerGeneration : type === 'design' ? pricing.creditsPerDesign : pricing.creditsPerVideo || 0;
            if (cost <= availableCredits && cost > 0) {
                affordableModels.push(modelId);
            }
        }
        // Sort by quality (higher credit cost usually means higher quality)
        return affordableModels.sort((a, b)=>{
            const costA = this.calculateGenerationCost(a, type);
            const costB = this.calculateGenerationCost(b, type);
            return costB - costA; // Descending order (highest quality first)
        });
    }
};
const pricingDisplay = {
    // Format credits for display
    formatCredits (credits) {
        if (credits >= 1000) {
            return `${(credits / 1000).toFixed(1)}K`;
        }
        return credits.toString();
    },
    // Format price for display
    formatPrice (price) {
        return `$${price.toFixed(2)}`;
    },
    // Get pricing tier info
    getTierInfo (modelId) {
        const pricing = modelPricing[modelId];
        return pricingTiers[pricing.tier];
    },
    // Get cost comparison between models
    compareCosts (modelA, modelB) {
        const costA = modelPricing[modelA].creditsPerGeneration;
        const costB = modelPricing[modelB].creditsPerGeneration;
        const difference = Math.abs(costA - costB);
        const percentDifference = (difference / Math.min(costA, costB) * 100).toFixed(0);
        return {
            cheaper: costA < costB ? modelA : modelB,
            moreExpensive: costA > costB ? modelA : modelB,
            difference,
            percentDifference: `${percentDifference}%`,
            ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`
        };
    },
    // Get value proposition for each model
    getValueProposition (modelId) {
        const pricing = modelPricing[modelId];
        const tierInfo = pricingTiers[pricing.tier];
        return {
            model: modelId,
            tier: pricing.tier,
            creditsPerGeneration: pricing.creditsPerGeneration,
            valueScore: tierInfo.features.length / pricing.creditsPerGeneration,
            description: tierInfo.description,
            bestFor: tierInfo.recommendedFor
        };
    }
};
function getModelPricing(modelId) {
    return modelPricing[modelId];
}
function getAllPricing() {
    return modelPricing;
}
function getModelsByTier(tier) {
    return Object.entries(modelPricing).filter(([_, pricing])=>pricing.tier === tier).map(([modelId])=>modelId);
}
function getCheapestModel() {
    return Object.entries(modelPricing).reduce((cheapest, [modelId, pricing])=>{
        const currentCheapest = modelPricing[cheapest];
        return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ? modelId : cheapest;
    }, 'revo-1.0');
}
function getMostExpensiveModel() {
    return Object.entries(modelPricing).reduce((mostExpensive, [modelId, pricing])=>{
        const currentMostExpensive = modelPricing[mostExpensive];
        return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ? modelId : mostExpensive;
    }, 'revo-1.0');
}
}}),
"[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Configurations
 * Centralized configuration for all Revo model versions
 */ __turbopack_context__.s({
    "compareModels": (()=>compareModels),
    "getAllModelConfigs": (()=>getAllModelConfigs),
    "getLatestModels": (()=>getLatestModels),
    "getModelConfig": (()=>getModelConfig),
    "getModelForBudget": (()=>getModelForBudget),
    "getModelsByStatus": (()=>getModelsByStatus),
    "getModelsByTier": (()=>getModelsByTier),
    "getRecommendedModel": (()=>getRecommendedModel),
    "modelConfigs": (()=>modelConfigs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)");
;
;
// Base configurations for different AI services
const baseConfigs = {
    'gemini-2.0': {
        aiService: 'gemini-2.0',
        fallbackServices: [
            'gemini-2.5',
            'openai'
        ],
        maxRetries: 3,
        timeout: 30000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 85,
            enhancementLevel: 5
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 2048,
            topP: 0.9,
            topK: 40
        }
    },
    'gemini-2.5': {
        aiService: 'gemini-2.5',
        fallbackServices: [
            'gemini-2.0',
            'openai'
        ],
        maxRetries: 2,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 90,
            enhancementLevel: 7
        },
        promptSettings: {
            temperature: 0.8,
            maxTokens: 4096,
            topP: 0.95,
            topK: 50
        }
    },
    'openai': {
        aiService: 'openai',
        fallbackServices: [
            'gemini-2.5',
            'gemini-2.0'
        ],
        maxRetries: 3,
        timeout: 35000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 88,
            enhancementLevel: 6
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 3000,
            topP: 0.9
        }
    },
    'gemini-2.5-flash-image': {
        aiService: 'gemini-2.5-flash-image',
        fallbackServices: [
            'imagen-4',
            'gemini-2.5'
        ],
        maxRetries: 3,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '2048x2048',
            compressionLevel: 92,
            enhancementLevel: 9
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 2048,
            topP: 0.9,
            topK: 40
        }
    }
};
const modelConfigs = {
    'revo-1.0': {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        version: '1.0.0',
        description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',
        longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',
        icon: 'Zap',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.0'],
        features: [
            'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',
            '1:1 Images with High Resolution',
            'Core Features',
            'Proven Performance',
            'Multi-platform Support',
            'Enhanced Brand Consistency',
            'Perfect Text Rendering',
            'High-Resolution Output (2048x2048)'
        ],
        releaseDate: '2024-01-15',
        lastUpdated: '2025-01-27'
    },
    'revo-1.5': {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        version: '1.5.0',
        description: 'Enhanced Model - Advanced Features',
        longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',
        icon: 'Sparkles',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.5'],
        config: {
            ...baseConfigs['gemini-2.5'],
            qualitySettings: {
                ...baseConfigs['gemini-2.5'].qualitySettings,
                enhancementLevel: 8
            }
        },
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.5'],
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations',
            'Professional Templates',
            'Advanced Brand Integration',
            'Real-time Context',
            'Trending Topics Integration'
        ],
        releaseDate: '2024-06-20',
        lastUpdated: '2024-12-15'
    },
    'revo-2.0': {
        id: 'revo-2.0',
        name: 'Revo 2.0',
        version: '2.0.0',
        description: 'Next-Gen Model - Advanced AI with native image generation',
        longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',
        icon: 'Rocket',
        badge: 'Next-Gen',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-2.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-2.0'],
        features: [
            'Next-Gen AI Engine',
            'Native Image Generation',
            'Character Consistency',
            'Intelligent Editing',
            'Inpainting & Outpainting',
            'Multimodal Reasoning',
            'All Aspect Ratios',
            'Perfect Brand Consistency'
        ],
        releaseDate: '2025-01-27',
        lastUpdated: '2025-01-27'
    }
};
function getModelConfig(modelId) {
    const config = modelConfigs[modelId];
    if (!config) {
        throw new Error(`Model configuration not found for: ${modelId}`);
    }
    return config;
}
function getAllModelConfigs() {
    return Object.values(modelConfigs);
}
function getModelsByStatus(status) {
    return getAllModelConfigs().filter((model)=>model.status === status);
}
function getModelsByTier(tier) {
    return getAllModelConfigs().filter((model)=>model.pricing.tier === tier);
}
function getLatestModels() {
    return getAllModelConfigs().sort((a, b)=>new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()).slice(0, 3);
}
function getRecommendedModel() {
    // Return Revo 1.5 as the recommended balanced option
    return modelConfigs['revo-1.5'];
}
function getModelForBudget(maxCredits) {
    return getAllModelConfigs().filter((model)=>model.pricing.creditsPerGeneration <= maxCredits).sort((a, b)=>a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);
}
function compareModels(modelA, modelB) {
    const configA = getModelConfig(modelA);
    const configB = getModelConfig(modelB);
    return {
        quality: {
            a: configA.capabilities.maxQuality,
            b: configB.capabilities.maxQuality,
            winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB
        },
        cost: {
            a: configA.pricing.creditsPerGeneration,
            b: configB.pricing.creditsPerGeneration,
            winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB
        },
        features: {
            a: configA.features.length,
            b: configB.features.length,
            winner: configA.features.length > configB.features.length ? modelA : modelB
        },
        status: {
            a: configA.status,
            b: configB.status,
            recommendation: configA.status === 'stable' || configB.status === 'stable' ? configA.status === 'stable' ? modelA : modelB : modelA
        }
    };
}
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:https [external] (node:https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:https", () => require("node:https"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:process [external] (node:process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:process", () => require("node:process"));

module.exports = mod;
}}),
"[externals]/node:stream/web [external] (node:stream/web, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream/web", () => require("node:stream/web"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:path [external] (node:path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:path", () => require("node:path"));

module.exports = mod;
}}),
"[externals]/worker_threads [external] (worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("worker_threads", () => require("worker_threads"));

module.exports = mod;
}}),
"[project]/src/services/weather.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/services/weather.ts
__turbopack_context__.s({
    "getWeather": (()=>getWeather)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript) <locals>");
;
const API_KEY = process.env.OPENWEATHERMAP_API_KEY;
const BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
async function getWeather(location) {
    if (!API_KEY || API_KEY === 'YOUR_OPENWEATHERMAP_API_KEY' || API_KEY.length < 20) {
        console.log('OpenWeatherMap API key is not configured or appears invalid.');
        return null;
    }
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(`${BASE_URL}?q=${location}&appid=${API_KEY}&units=imperial`);
        if (!response.ok) {
            console.error('Weather API Error:', `Status: ${response.status}`);
            // Return null to allow the flow to continue without weather data
            return `Could not retrieve weather information due to an API error (Status: ${response.status})`;
        }
        const data = await response.json();
        if (data && data.weather && data.main) {
            const description = data.weather[0].description;
            const temp = Math.round(data.main.temp);
            return `${description} with a temperature of ${temp}°F`;
        }
        return null;
    } catch (error) {
        console.error('Error fetching weather data:', error);
        return null;
    }
}
}}),
"[project]/src/services/events.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/services/events.ts
__turbopack_context__.s({
    "getEvents": (()=>getEvents)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$add$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/add.mjs [app-rsc] (ecmascript)");
;
;
const API_KEY = process.env.EVENTBRITE_PRIVATE_TOKEN;
const BASE_URL = 'https://www.eventbriteapi.com/v3/events/search/';
async function getEvents(location, date) {
    if (!API_KEY || API_KEY === 'YOUR_EVENTBRITE_PRIVATE_TOKEN' || API_KEY.length < 10) {
        console.log('Eventbrite API key is not configured or appears invalid.');
        return null;
    }
    // Eventbrite is more flexible with location strings.
    const city = location.split(',')[0].trim();
    // Search for events starting from today up to one week from now to get more results
    const startDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "yyyy-MM-dd'T'HH:mm:ss'Z'");
    const endDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$add$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(date, {
        days: 7
    }), "yyyy-MM-dd'T'HH:mm:ss'Z'");
    try {
        const url = `${BASE_URL}?location.address=${city}&start_date.range_start=${startDate}&start_date.range_end=${endDate}&sort_by=date`;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(url, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Accept': 'application/json'
            }
        });
        if (!response.ok) {
            const errorBody = await response.text();
            console.error('Eventbrite API Error:', `Status: ${response.status}`, errorBody);
            // Return null to allow the flow to continue without event data
            return `Could not retrieve local event information due to an API error (Status: ${response.status}).`;
        }
        const data = await response.json();
        if (data.events && data.events.length > 0) {
            const eventNames = data.events.slice(0, 5).map((event)=>event.name.text);
            return `local events happening soon include: ${eventNames.join(', ')}`;
        } else {
            return 'no major local events found on Eventbrite for the upcoming week';
        }
    } catch (error) {
        console.error('Error fetching event data:', error);
        return null;
    }
}
}}),
"[project]/src/ai/tools/local-data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7fdcb0b92919a9bca2d6479f40ca161a39be8544fc":"getEventsTool","7fddcb107dd9ce5a9f55f16c661ee35dbf2ef139c8":"getWeatherTool"},"",""] */ __turbopack_context__.s({
    "getEventsTool": (()=>getEventsTool),
    "getWeatherTool": (()=>getWeatherTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview Defines Genkit tools for fetching local weather and event data.
 * This allows the AI to dynamically decide when to pull in local information
 * to make social media posts more relevant and timely.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$weather$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/weather.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$events$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/events.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
const getWeatherTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getWeather',
    description: 'Gets the current weather for a specific location. Use this to make posts more relevant to the current conditions.',
    inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The city and state, e.g., "San Francisco, CA"')
    }),
    outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}, async (input)=>{
    const weather = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$weather$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getWeather"])(input.location);
    return weather || 'Could not retrieve weather information.';
});
const getEventsTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEvents',
    description: 'Finds local events happening on or after the current date for a specific location. Use this to create timely posts about local happenings.',
    inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The city and state, e.g., "San Francisco, CA"')
    }),
    outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}, async (input)=>{
    // Tools will always be called with the current date
    const events = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$events$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEvents"])(input.location, new Date());
    return events || 'Could not retrieve local event information.';
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getWeatherTool,
    getEventsTool
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getWeatherTool, "7fddcb107dd9ce5a9f55f16c661ee35dbf2ef139c8", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getEventsTool, "7fdcb0b92919a9bca2d6479f40ca161a39be8544fc", null);
}}),
"[project]/src/ai/tools/enhanced-local-data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced Local Data Tools - Events and Weather Integration
 * 
 * This module provides real-time local events and weather data
 * for contextually aware content generation.
 */ __turbopack_context__.s({
    "getEnhancedEventsTool": (()=>getEnhancedEventsTool),
    "getEnhancedWeatherTool": (()=>getEnhancedWeatherTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const getEnhancedEventsTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEnhancedEvents',
    description: 'Fetch local events from Eventbrite API that are relevant to the business type and location',
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Location for events (city, country or coordinates)'),
        businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Business type to filter relevant events'),
        radius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().default('25km').describe('Search radius for events'),
        timeframe: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().default('this_week').describe('Time period: today, this_week, this_month')
    }),
    output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        start_date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        venue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        is_free: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean(),
        relevance_score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number()
    }))
}, async (input)=>{
    try {
        if (!process.env.EVENTBRITE_API_KEY) {
            console.log('Eventbrite API key not configured, using fallback events');
            return getEventsFallback(input.location, input.businessType);
        }
        console.log(`🎪 Fetching events from Eventbrite for ${input.location}...`);
        // Convert location to coordinates if needed
        const locationQuery = await geocodeLocation(input.location);
        // Build Eventbrite API request
        const params = new URLSearchParams({
            'location.address': input.location,
            'location.within': input.radius,
            'start_date.range_start': getDateRange(input.timeframe).start,
            'start_date.range_end': getDateRange(input.timeframe).end,
            'sort_by': 'relevance',
            'page_size': '20',
            'expand': 'venue,category'
        });
        const response = await fetch(`https://www.eventbriteapi.com/v3/events/search/?${params}`, {
            headers: {
                'Authorization': `Bearer ${process.env.EVENTBRITE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            console.log(`Eventbrite API error: ${response.status} ${response.statusText}`);
            throw new Error(`Eventbrite API error: ${response.status}`);
        }
        const data = await response.json();
        console.log(`✅ Eventbrite returned ${data.events?.length || 0} events`);
        // Process and filter events by business relevance
        const relevantEvents = processEventbriteEvents(data.events || [], input.businessType);
        return relevantEvents.slice(0, 10);
    } catch (error) {
        console.error('Error fetching Eventbrite events:', error);
        return getEventsFallback(input.location, input.businessType);
    }
});
const getEnhancedWeatherTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEnhancedWeather',
    description: 'Fetch current weather and forecast with business context and content opportunities',
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Location for weather (city, country)'),
        businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Business type to provide relevant weather context'),
        includeForecast: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional().default(false).describe('Include 5-day forecast')
    }),
    output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        humidity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        feels_like: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        content_opportunities: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        business_impact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        forecast: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            business_opportunity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).optional()
    })
}, async (input)=>{
    try {
        if (!process.env.OPENWEATHER_API_KEY) {
            console.log('OpenWeather API key not configured, using fallback weather');
            return getWeatherFallback(input.location, input.businessType);
        }
        console.log(`🌤️ Fetching weather from OpenWeather for ${input.location}...`);
        // Current weather
        const currentParams = new URLSearchParams({
            q: input.location,
            appid: process.env.OPENWEATHER_API_KEY,
            units: 'metric'
        });
        const currentResponse = await fetch(`https://api.openweathermap.org/data/2.5/weather?${currentParams}`);
        if (!currentResponse.ok) {
            console.log(`OpenWeather API error: ${currentResponse.status} ${currentResponse.statusText}`);
            throw new Error(`OpenWeather API error: ${currentResponse.status}`);
        }
        const currentData = await currentResponse.json();
        console.log(`✅ OpenWeather returned current weather for ${currentData.name}`);
        // Process weather data with business context
        const weatherContext = processWeatherData(currentData, input.businessType);
        // Get forecast if requested
        if (input.includeForecast) {
            const forecastParams = new URLSearchParams({
                q: input.location,
                appid: process.env.OPENWEATHER_API_KEY,
                units: 'metric'
            });
            const forecastResponse = await fetch(`https://api.openweathermap.org/data/2.5/forecast?${forecastParams}`);
            if (forecastResponse.ok) {
                const forecastData = await forecastResponse.json();
                weatherContext.forecast = processForecastData(forecastData, input.businessType);
            }
        }
        return weatherContext;
    } catch (error) {
        console.error('Error fetching weather:', error);
        return getWeatherFallback(input.location, input.businessType);
    }
});
/**
 * Helper functions
 */ async function geocodeLocation(location) {
    try {
        if (!process.env.OPENWEATHER_API_KEY) return null;
        const params = new URLSearchParams({
            q: location,
            limit: '1',
            appid: process.env.OPENWEATHER_API_KEY
        });
        const response = await fetch(`https://api.openweathermap.org/geo/1.0/direct?${params}`);
        if (response.ok) {
            const data = await response.json();
            if (data.length > 0) {
                return {
                    lat: data[0].lat,
                    lon: data[0].lon
                };
            }
        }
    } catch (error) {
        console.error('Error geocoding location:', error);
    }
    return null;
}
function getDateRange(timeframe) {
    const now = new Date();
    const start = new Date(now);
    let end = new Date(now);
    switch(timeframe){
        case 'today':
            end.setDate(end.getDate() + 1);
            break;
        case 'this_week':
            end.setDate(end.getDate() + 7);
            break;
        case 'this_month':
            end.setMonth(end.getMonth() + 1);
            break;
        default:
            end.setDate(end.getDate() + 7);
    }
    return {
        start: start.toISOString(),
        end: end.toISOString()
    };
}
function processEventbriteEvents(events, businessType) {
    return events.map((event)=>{
        const relevanceScore = calculateEventRelevance(event, businessType);
        return {
            name: event.name?.text || 'Unnamed Event',
            description: event.description?.text?.substring(0, 200) || '',
            start_date: event.start?.local || event.start?.utc || '',
            end_date: event.end?.local || event.end?.utc,
            venue: event.venue?.name || 'Online Event',
            category: event.category?.name || 'General',
            url: event.url,
            is_free: event.is_free || false,
            relevance_score: relevanceScore
        };
    }).filter((event)=>event.relevance_score >= 5).sort((a, b)=>b.relevance_score - a.relevance_score);
}
function calculateEventRelevance(event, businessType) {
    let score = 5; // Base score
    const eventName = (event.name?.text || '').toLowerCase();
    const eventDescription = (event.description?.text || '').toLowerCase();
    const eventCategory = (event.category?.name || '').toLowerCase();
    // Business type relevance
    const businessKeywords = getBusinessKeywords(businessType);
    for (const keyword of businessKeywords){
        if (eventName.includes(keyword) || eventDescription.includes(keyword) || eventCategory.includes(keyword)) {
            score += 2;
        }
    }
    // Event category bonus
    if (eventCategory.includes('business') || eventCategory.includes('networking')) {
        score += 1;
    }
    // Free events get slight bonus for broader appeal
    if (event.is_free) {
        score += 1;
    }
    return Math.min(10, score);
}
function getBusinessKeywords(businessType) {
    const keywordMap = {
        'financial technology software': [
            'fintech',
            'finance',
            'banking',
            'payment',
            'blockchain',
            'cryptocurrency',
            'startup',
            'tech'
        ],
        'restaurant': [
            'food',
            'culinary',
            'cooking',
            'dining',
            'chef',
            'restaurant',
            'hospitality'
        ],
        'fitness': [
            'fitness',
            'health',
            'wellness',
            'gym',
            'workout',
            'nutrition',
            'sports'
        ],
        'technology': [
            'tech',
            'software',
            'programming',
            'ai',
            'digital',
            'innovation',
            'startup'
        ],
        'beauty': [
            'beauty',
            'cosmetics',
            'skincare',
            'wellness',
            'spa',
            'fashion'
        ],
        'retail': [
            'retail',
            'shopping',
            'ecommerce',
            'business',
            'sales',
            'marketing'
        ]
    };
    return keywordMap[businessType.toLowerCase()] || [
        'business',
        'networking',
        'professional'
    ];
}
function processWeatherData(weatherData, businessType) {
    const temperature = Math.round(weatherData.main.temp);
    const condition = weatherData.weather[0].main;
    const description = weatherData.weather[0].description;
    return {
        temperature,
        condition,
        description,
        humidity: weatherData.main.humidity,
        feels_like: Math.round(weatherData.main.feels_like),
        location: weatherData.name,
        content_opportunities: generateWeatherContentOpportunities(condition, temperature, businessType),
        business_impact: generateBusinessWeatherImpact(condition, temperature, businessType)
    };
}
function processForecastData(forecastData, businessType) {
    const dailyForecasts = forecastData.list.filter((_, index)=>index % 8 === 0).slice(0, 5);
    return dailyForecasts.map((forecast)=>({
            date: new Date(forecast.dt * 1000).toLocaleDateString(),
            temperature: Math.round(forecast.main.temp),
            condition: forecast.weather[0].main,
            business_opportunity: generateBusinessWeatherImpact(forecast.weather[0].main, forecast.main.temp, businessType)
        }));
}
function generateWeatherContentOpportunities(condition, temperature, businessType) {
    const opportunities = [];
    // Temperature-based opportunities
    if (temperature > 25) {
        opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');
    } else if (temperature < 10) {
        opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');
    }
    // Condition-based opportunities
    switch(condition.toLowerCase()){
        case 'rain':
            opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');
            break;
        case 'sunny':
        case 'clear':
            opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');
            break;
        case 'clouds':
            opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');
            break;
    }
    // Business-specific weather opportunities
    const businessOpportunities = getBusinessWeatherOpportunities(businessType, condition, temperature);
    opportunities.push(...businessOpportunities);
    return opportunities;
}
function generateBusinessWeatherImpact(condition, temperature, businessType) {
    const businessImpacts = {
        'restaurant': {
            'sunny': 'Perfect weather for outdoor dining and patio service',
            'rain': 'Great opportunity to promote cozy indoor dining experience',
            'hot': 'Ideal time to highlight refreshing drinks and cool dishes',
            'cold': 'Perfect weather for warm comfort food and hot beverages'
        },
        'fitness': {
            'sunny': 'Excellent conditions for outdoor workouts and activities',
            'rain': 'Great time to promote indoor fitness programs',
            'hot': 'Important to emphasize hydration and cooling strategies',
            'cold': 'Perfect for promoting warm-up routines and indoor training'
        },
        'retail': {
            'sunny': 'Great shopping weather, people are out and about',
            'rain': 'Perfect time for online shopping promotions',
            'hot': 'Opportunity to promote summer collections and cooling products',
            'cold': 'Ideal for promoting warm clothing and comfort items'
        }
    };
    const businessKey = businessType.toLowerCase();
    const impacts = businessImpacts[businessKey] || businessImpacts['retail'];
    if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';
    if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';
    return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';
}
function getBusinessWeatherOpportunities(businessType, condition, temperature) {
    // Business-specific weather content opportunities
    const opportunities = [];
    if (businessType.toLowerCase().includes('restaurant')) {
        if (condition === 'sunny') opportunities.push('Outdoor dining promotion', 'Fresh seasonal menu highlight');
        if (condition === 'rain') opportunities.push('Cozy indoor atmosphere', 'Comfort food specials');
    }
    if (businessType.toLowerCase().includes('fitness')) {
        if (condition === 'sunny') opportunities.push('Outdoor workout motivation', 'Vitamin D benefits');
        if (temperature > 25) opportunities.push('Hydration importance', 'Summer fitness tips');
    }
    return opportunities;
}
// Fallback functions
function getEventsFallback(location, businessType) {
    return [
        {
            name: `${businessType} networking event in ${location}`,
            description: `Local networking opportunity for ${businessType} professionals`,
            start_date: new Date(Date.now() + 86400000 * 3).toISOString(),
            venue: `${location} Business Center`,
            category: 'Business & Professional',
            is_free: true,
            relevance_score: 8
        }
    ];
}
function getWeatherFallback(location, businessType) {
    return {
        temperature: 22,
        condition: 'Clear',
        description: 'clear sky',
        humidity: 60,
        feels_like: 24,
        location: location,
        content_opportunities: [
            'Pleasant weather content opportunity',
            'Comfortable conditions messaging'
        ],
        business_impact: 'Current weather conditions are favorable for business activities'
    };
}
}}),
"[project]/src/ai/prompts/advanced-ai-prompt.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced AI Content Generation Prompt
 * 
 * This prompt integrates trending topics, competitor analysis, cultural optimization,
 * human-like content generation, and traffic-driving strategies.
 */ __turbopack_context__.s({
    "ADVANCED_AI_PROMPT": (()=>ADVANCED_AI_PROMPT)
});
const ADVANCED_AI_PROMPT = `You are an elite social media strategist, cultural anthropologist, and viral content creator with deep expertise in the {{{businessType}}} industry.

Your mission is to create content that:
🎯 Captures trending conversations and cultural moments
🚀 Drives maximum traffic and business results
🤝 Feels authentically human and culturally sensitive
💡 Differentiates from competitors strategically
📈 Optimizes for platform-specific viral potential
🌤️ Integrates current weather and local events naturally
🎪 Leverages local happenings for timely relevance
🌍 Uses ENGLISH ONLY for all content generation

BUSINESS INTELLIGENCE:
- Industry: {{{businessType}}}
- Location: {{{location}}}
- Brand Voice: {{{writingTone}}}
- Content Themes: {{{contentThemes}}}
- Day: {{{dayOfWeek}}}
- Date: {{{currentDate}}}
{{#if platform}}- Primary Platform: {{{platform}}}{{/if}}
{{#if services}}- Services/Products: {{{services}}}{{/if}}
{{#if targetAudience}}- Target Audience: {{{targetAudience}}}{{/if}}
{{#if keyFeatures}}- Key Features: {{{keyFeatures}}}{{/if}}
{{#if competitiveAdvantages}}- Competitive Edge: {{{competitiveAdvantages}}}{{/if}}
{{#if contentVariation}}- Content Approach: {{{contentVariation}}} (MANDATORY: Use this specific approach for content generation){{/if}}

TRENDING TOPICS INTEGRATION:
Research and incorporate current trending topics relevant to:
- {{{businessType}}} industry developments
- {{{location}}} local events and cultural moments
- Platform-specific trending hashtags and conversations
- Seasonal relevance and timely opportunities
- News events that connect to your business value

COMPETITOR DIFFERENTIATION STRATEGY:
Analyze and differentiate from typical competitor content by:
- Avoiding generic industry messaging
- Finding unique angles on common topics
- Highlighting authentic personal/business stories
- Focusing on underserved audience needs
- Creating content gaps competitors miss
- Using authentic local cultural connections

CONTENT DIVERSITY ENFORCEMENT:
CRITICAL: Each post must be completely different from previous generations:
- Use different opening hooks (question, statement, story, statistic, quote)
- Vary content structure (problem-solution, story-lesson, tip-benefit, behind-scenes)
- Alternate between different emotional tones (inspiring, educational, entertaining, personal)
- Change content length and paragraph structure significantly
- Use different call-to-action styles (direct, subtle, question-based, action-oriented)
- Vary hashtag themes and combinations
- Never repeat the same content pattern or messaging approach

{{#if contentVariation}}
MANDATORY CONTENT VARIATION APPROACH - {{{contentVariation}}}:

Use the following approach based on the content variation specified:
- For "trending_hook": Start with a trending topic or viral conversation, connect the trend to your business naturally, use current social media language and references, include trending hashtags and phrases
- For "story_driven": Begin with a compelling personal or customer story, use narrative structure with beginning, middle, end, include emotional elements and relatable characters, end with a meaningful lesson or takeaway
- For "educational_tip": Lead with valuable, actionable advice, use numbered lists or step-by-step format, position your business as the expert solution, include "did you know" or "pro tip" elements
- For "behind_scenes": Show the human side of your business, include process, preparation, or team moments, use authentic, unpolished language, create connection through transparency
- For "question_engagement": Start with a thought-provoking question, encourage audience participation and responses, use polls, "this or that," or opinion requests, build community through conversation
- For "statistic_driven": Lead with surprising or compelling statistics, use data to support your business value, include industry insights and research, position your business as data-informed
- For "personal_insight": Share personal experiences or observations, use first-person perspective and authentic voice, include lessons learned or mistakes made, connect personal growth to business value
- For "industry_contrarian": Challenge common industry assumptions, present alternative viewpoints respectfully, use "unpopular opinion" or "hot take" framing, support contrarian views with evidence
- For "local_cultural": Reference local events, landmarks, or culture, use location-specific language and references, connect to community values and traditions, show deep local understanding
- For "seasonal_relevance": Connect to current season, weather, or holidays, use timely references and seasonal language, align business offerings with seasonal needs
- For "problem_solution": Identify a specific customer pain point, agitate the problem to create urgency, present your business as the clear solution, use before/after or transformation language
- For "inspiration_motivation": Use uplifting, motivational language, include inspirational quotes or mantras, focus on transformation and possibility, connect inspiration to business outcomes

Apply the specific approach for the {{{contentVariation}}} variation throughout your content generation.
{{/if}}

CULTURAL & LOCATION OPTIMIZATION:
For {{{location}}}, incorporate:
- Local cultural nuances and values
- Regional language preferences and expressions
- Community customs and social norms
- Seasonal and cultural calendar awareness
- Local landmarks, events, and references
- Respectful acknowledgment of cultural diversity

INTELLIGENT CONTEXT USAGE:
{{#if contextInstructions}}
CONTEXT INSTRUCTIONS FOR THIS SPECIFIC POST:
{{{contextInstructions}}}

Follow these instructions precisely - they are based on expert analysis of what information is relevant for this specific business type and location.
{{/if}}

WEATHER & EVENTS INTEGRATION:
{{#if selectedWeather}}
- Current weather: {{{selectedWeather.temperature}}}°C, {{{selectedWeather.condition}}}
- Business impact: {{{selectedWeather.business_impact}}}
- Content opportunities: {{{selectedWeather.content_opportunities}}}
{{/if}}

{{#if selectedEvents}}
- Relevant local events:
{{#each selectedEvents}}
  * {{{this.name}}} ({{{this.category}}}) - {{{this.start_date}}}
{{/each}}
{{/if}}

Use this information ONLY if the context instructions indicate it's relevant for this business type.

HUMAN-LIKE AUTHENTICITY MARKERS:
Make content feel genuinely human by:
- Using conversational, imperfect language
- Including personal experiences and observations
- Showing vulnerability and learning moments
- Using specific details over generic statements
- Adding natural speech patterns and contractions
- Including time-specific references (today, this morning)
- Expressing genuine emotions and reactions

TRAFFIC-DRIVING OPTIMIZATION:
Maximize engagement and traffic through:
- Curiosity gaps that demand attention
- Shareability factors that encourage spreading
- Conversion triggers that drive action
- Social proof elements that build trust
- Interactive elements that boost engagement
- Viral hooks that capture trending conversations

ADVANCED COPYWRITING FRAMEWORKS:
1. **AIDA Framework**: Attention → Interest → Desire → Action
2. **PAS Framework**: Problem → Agitation → Solution  
3. **Storytelling Arc**: Setup → Conflict → Resolution → Lesson
4. **Social Proof Stack**: Testimonial → Statistics → Authority → Community
5. **Curiosity Loop**: Hook → Tension → Payoff → Next Hook

PSYCHOLOGICAL TRIGGERS FOR MAXIMUM ENGAGEMENT:
✅ **Urgency & Scarcity**: Time-sensitive opportunities
✅ **Social Proof**: Community validation and testimonials
✅ **FOMO**: Exclusive access and insider information
✅ **Curiosity Gaps**: Intriguing questions and reveals
✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy
✅ **Authority**: Expert insights and industry knowledge
✅ **Reciprocity**: Valuable tips and free insights
✅ **Tribal Identity**: Community belonging and shared values

CONTENT GENERATION REQUIREMENTS:

Generate a comprehensive social media post with these components:

1. **CAPTION (content)**:
   - Start with a trending topic hook or cultural moment
   - Use authentic, conversational human language
   - Include competitor differentiation naturally
   - Apply psychological triggers strategically
   - Incorporate local cultural references appropriately
   - End with traffic-driving call-to-action
   - Length optimized for platform and engagement
   - Feel like it was written by a real person, not AI

2. **CATCHY WORDS (catchyWords)**:
   - Create relevant, business-focused catchy words (max 5 words)
   - MUST be directly related to the specific business services/products
   - Use clear, professional language that matches the business type
   - Focus on the business value proposition or key service
   - Avoid generic phrases like "Banking Made Easy" or random financial terms
   - Examples: For a restaurant: "Fresh Daily Specials", For a gym: "Transform Your Body", For a salon: "Expert Hair Care"
   - Required for ALL posts - this is the main visual text
   - Optimize for visual impact and business relevance

3. **SUBHEADLINE (subheadline)** - OPTIONAL:
   - Add only when it would make the post more effective
   - Maximum 14 words
   - Use your marketing expertise to decide when needed
   - Should complement the catchy words and enhance the message
   - Examples: When explaining a complex service, highlighting a special offer, or providing context
   - Skip if the catchy words and caption are sufficient

4. **CALL TO ACTION (callToAction)** - OPTIONAL:
   - Add only when it would drive better engagement or conversions
   - Use your marketing expertise to decide when needed
   - Should be specific and actionable
   - Examples: "Book Now", "Call Today", "Visit Us", "Learn More", "Get Started"
   - Skip if the post is more about awareness or engagement rather than direct action

5. **HASHTAGS**:
   - Mix trending hashtags with niche industry tags
   - Include location-specific and cultural hashtags
   - Balance high-competition and low-competition tags
   - Ensure cultural sensitivity and appropriateness
   - Optimize quantity for platform (Instagram: 20-30, LinkedIn: 3-5, etc.)

6. **CONTENT VARIANTS (contentVariants)**:
   Generate 2-3 alternative approaches:

   **Variant 1 - Trending Topic Angle**:
   - Hook into current trending conversation
   - Connect trend to business value naturally
   - Use viral content patterns
   - Include shareability factors

   **Variant 2 - Cultural Connection Angle**:
   - Start with local cultural reference
   - Show deep community understanding
   - Use location-specific language naturally
   - Build authentic local connections

   **Variant 3 - Competitor Differentiation Angle**:
   - Address common industry pain points differently
   - Highlight unique business approach
   - Use contrarian but respectful positioning
   - Show authentic expertise and experience

   For each variant, provide:
   - The alternative caption content
   - The strategic approach used
   - Why this variant will drive traffic and engagement
   - Cultural sensitivity considerations

QUALITY STANDARDS:
- Every word serves engagement or conversion purpose
- Content feels authentically human, never robotic
- Cultural references are respectful and accurate
- Trending topics are naturally integrated, not forced
- Competitor differentiation is subtle but clear
- Traffic-driving elements are seamlessly woven in
- Platform optimization is invisible but effective
- Local cultural nuances are appropriately honored

TRAFFIC & CONVERSION OPTIMIZATION:
- Include clear value proposition for audience
- Create multiple engagement touchpoints
- Use psychological triggers ethically
- Provide shareable insights or entertainment
- Include conversion pathway (comment, DM, visit, etc.)
- Optimize for algorithm preferences
- Encourage community building and return visits

WEBSITE REFERENCE GUIDELINES:
{{#if websiteUrl}}
- Website available for CTAs: {{{websiteUrl}}} (use clean format without https:// or www.)
- Only include website when CTA specifically calls for it (e.g., "check us out online", "visit our site")
- Don't force website into every post - use contextually when it makes sense
- Examples: "Visit us online", "Check our website", "Learn more at [clean-url]"
{{else}}
- No website URL provided - focus on other CTAs (DM, call, visit location)
{{/if}}

LANGUAGE REQUIREMENTS:
🌍 TEXT CLARITY: Generate clear, readable text
{{#if useLocalLanguage}}
- You may use local language text when 100% certain of spelling, meaning, and cultural appropriateness
- Mix local language with English naturally (1-2 local words maximum per text element)
- Only use commonly known local words that add cultural connection to {{{location}}}
- When uncertain about local language accuracy, use English instead
- Better to use clear English than incorrect or garbled local language
{{else}}
- USE ONLY ENGLISH for all text content (captions, hashtags, call-to-actions)
- Do not use any local language words or phrases
- Keep all text elements in clear, professional English
- Focus on universal messaging that works across all markets
{{/if}}
- Do NOT use corrupted, gibberish, or unreadable character sequences
- Do NOT use random symbols or malformed text
- Ensure all text is properly formatted and legible
- Avoid character encoding issues or text corruption
- All text must be clear and professional
- Prevent any garbled or nonsensical character combinations

Your response MUST be a valid JSON object that conforms to the output schema.
Focus on creating content that real humans will love, share, and act upon.`;
}}),
"[project]/src/ai/utils/hashtag-strategy.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Hashtag Strategy Utilities
 * 
 * This module provides sophisticated hashtag generation and optimization
 * strategies for different platforms and business types.
 */ __turbopack_context__.s({
    "analyzeHashtags": (()=>analyzeHashtags),
    "generateHashtagStrategy": (()=>generateHashtagStrategy)
});
function generateHashtagStrategy(businessType, location, platform, services, targetAudience, postTopic, designStyle) {
    // Generate all possible hashtags
    const allHashtags = {
        trending: generateTrendingHashtags(businessType, platform),
        niche: generateNicheHashtags(businessType, services),
        branded: generateBrandedHashtags(businessType),
        location: generateLocationHashtags(location),
        community: generateCommunityHashtags(businessType, targetAudience),
        topicSpecific: generateTopicSpecificHashtags(postTopic, designStyle),
        designBased: generateDesignBasedHashtags(designStyle, businessType)
    };
    // Select exactly 10 most relevant hashtags
    return selectTop10Hashtags(allHashtags, platform, businessType, postTopic);
}
/**
 * Optimizes hashtag counts based on platform best practices
 */ function optimizeHashtagsForPlatform(strategy, platform) {
    const platformLimits = {
        'instagram': {
            trending: 5,
            niche: 8,
            location: 4,
            community: 5,
            branded: 3
        },
        'linkedin': {
            trending: 2,
            niche: 2,
            location: 1,
            community: 1,
            branded: 1
        },
        'twitter': {
            trending: 2,
            niche: 1,
            location: 0,
            community: 0,
            branded: 0
        },
        'facebook': {
            trending: 2,
            niche: 3,
            location: 2,
            community: 2,
            branded: 1
        }
    };
    const limits = platformLimits[platform.toLowerCase()] || {
        trending: 3,
        niche: 5,
        location: 3,
        community: 3,
        branded: 2
    };
    return {
        trending: strategy.trending.slice(0, limits.trending),
        niche: strategy.niche.slice(0, limits.niche),
        location: strategy.location.slice(0, limits.location),
        community: strategy.community.slice(0, limits.community),
        branded: strategy.branded.slice(0, limits.branded),
        topicSpecific: strategy.topicSpecific || [],
        designBased: strategy.designBased || []
    };
}
/**
 * Generates trending hashtags based on business type and platform
 */ function generateTrendingHashtags(businessType, platform) {
    const businessTypeMap = {
        'restaurant': [
            '#foodie',
            '#delicious',
            '#foodstagram',
            '#yummy',
            '#tasty'
        ],
        'fitness': [
            '#fitness',
            '#workout',
            '#health',
            '#motivation',
            '#fitlife'
        ],
        'beauty': [
            '#beauty',
            '#skincare',
            '#makeup',
            '#selfcare',
            '#glowup'
        ],
        'retail': [
            '#shopping',
            '#style',
            '#fashion',
            '#deals',
            '#newcollection'
        ],
        'technology': [
            '#tech',
            '#innovation',
            '#digital',
            '#future',
            '#startup'
        ],
        'healthcare': [
            '#health',
            '#wellness',
            '#care',
            '#medical',
            '#healthy'
        ],
        'education': [
            '#education',
            '#learning',
            '#knowledge',
            '#skills',
            '#growth'
        ],
        'real_estate': [
            '#realestate',
            '#home',
            '#property',
            '#investment',
            '#dreamhome'
        ],
        'automotive': [
            '#cars',
            '#automotive',
            '#driving',
            '#vehicle',
            '#auto'
        ],
        'travel': [
            '#travel',
            '#adventure',
            '#explore',
            '#wanderlust',
            '#vacation'
        ]
    };
    const platformTrending = {
        'instagram': [
            '#instagood',
            '#photooftheday',
            '#love',
            '#beautiful',
            '#happy',
            '#instadaily',
            '#follow',
            '#like4like'
        ],
        'linkedin': [
            '#professional',
            '#business',
            '#career',
            '#networking',
            '#success',
            '#leadership',
            '#innovation',
            '#growth'
        ],
        'twitter': [
            '#trending',
            '#viral',
            '#breaking',
            '#news',
            '#update',
            '#thread',
            '#twitterchat',
            '#follow'
        ],
        'facebook': [
            '#community',
            '#local',
            '#family',
            '#friends',
            '#share',
            '#like',
            '#comment',
            '#engage'
        ]
    };
    const businessHashtags = businessTypeMap[businessType.toLowerCase()] || [
        '#business',
        '#service',
        '#quality',
        '#professional',
        '#local'
    ];
    const platformHashtags = platformTrending[platform.toLowerCase()] || [
        '#social',
        '#content',
        '#engagement'
    ];
    // Shuffle to ensure variety in each generation
    const shuffledBusiness = businessHashtags.sort(()=>0.5 - Math.random());
    const shuffledPlatform = platformHashtags.sort(()=>0.5 - Math.random());
    return [
        ...shuffledBusiness.slice(0, 3),
        ...shuffledPlatform.slice(0, 2)
    ];
}
/**
 * Generates niche-specific hashtags
 */ function generateNicheHashtags(businessType, services) {
    const nicheMap = {
        'food production': [
            '#foodproduction',
            '#nutrition',
            '#healthyfood',
            '#manufacturing',
            '#qualitycontrol',
            '#foodsafety',
            '#sustainable',
            '#organic',
            '#processing',
            '#ingredients',
            '#nutritious',
            '#wholesome'
        ],
        'restaurant': [
            '#localfood',
            '#chefspecial',
            '#freshingredients',
            '#culinaryart',
            '#foodculture',
            '#diningexperience',
            '#artisanfood',
            '#farmtotable'
        ],
        'fitness': [
            '#personaltrainer',
            '#strengthtraining',
            '#cardio',
            '#nutrition',
            '#bodybuilding',
            '#crossfit',
            '#yoga',
            '#pilates'
        ],
        'beauty': [
            '#beautytips',
            '#skincareroutine',
            '#makeuptutorial',
            '#beautyproducts',
            '#antiaging',
            '#naturalbeauty',
            '#beautysalon',
            '#spa'
        ],
        'retail': [
            '#boutique',
            '#handmade',
            '#unique',
            '#quality',
            '#craftsmanship',
            '#designer',
            '#exclusive',
            '#limited'
        ],
        'technology': [
            '#software',
            '#AI',
            '#machinelearning',
            '#cybersecurity',
            '#cloudcomputing',
            '#blockchain',
            '#IoT',
            '#automation'
        ],
        'healthcare': [
            '#preventivecare',
            '#patientcare',
            '#medicaladvice',
            '#healthtips',
            '#wellness',
            '#mentalhealth',
            '#nutrition',
            '#exercise'
        ],
        'education': [
            '#onlinelearning',
            '#skillbuilding',
            '#certification',
            '#training',
            '#development',
            '#mentorship',
            '#coaching',
            '#academy'
        ],
        'real_estate': [
            '#propertyinvestment',
            '#homebuying',
            '#realtorlife',
            '#propertymanagement',
            '#commercialrealestate',
            '#luxury',
            '#firsttimehomebuyer'
        ],
        'automotive': [
            '#carcare',
            '#automotive',
            '#mechanic',
            '#carrepair',
            '#maintenance',
            '#performance',
            '#luxury',
            '#electric'
        ],
        'travel': [
            '#localtourism',
            '#hiddengems',
            '#culturalexperience',
            '#adventure',
            '#ecotourism',
            '#luxurytravel',
            '#backpacking',
            '#roadtrip'
        ]
    };
    const baseNiche = nicheMap[businessType.toLowerCase()] || [
        '#specialized',
        '#expert',
        '#professional',
        '#quality',
        '#service',
        '#local',
        '#trusted',
        '#experienced'
    ];
    // Shuffle the base niche hashtags to ensure variety
    const shuffledNiche = baseNiche.sort(()=>0.5 - Math.random());
    // Add service-specific hashtags if services are provided
    if (services) {
        const serviceWords = services.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
        const serviceHashtags = serviceWords.slice(0, 3).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
        return [
            ...shuffledNiche.slice(0, 5),
            ...serviceHashtags
        ];
    }
    return shuffledNiche.slice(0, 8);
}
/**
 * Generates branded hashtags
 */ function generateBrandedHashtags(businessType) {
    const brandedSuffixes = [
        'experience',
        'quality',
        'service',
        'difference',
        'way',
        'style',
        'approach'
    ];
    const businessPrefix = businessType.toLowerCase().replace(/[^a-z]/g, '');
    return [
        `#${businessPrefix}${brandedSuffixes[0]}`,
        `#${businessPrefix}${brandedSuffixes[1]}`,
        `#choose${businessPrefix}`
    ];
}
/**
 * Generates location-based hashtags
 */ function generateLocationHashtags(location) {
    const locationParts = location.split(',').map((part)=>part.trim());
    const hashtags = [];
    locationParts.forEach((part)=>{
        const cleanLocation = part.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '').toLowerCase();
        if (cleanLocation.length > 2) {
            hashtags.push(`#${cleanLocation}`);
            hashtags.push(`#local${cleanLocation}`);
            hashtags.push(`#${cleanLocation}business`);
        }
    });
    // Add generic location hashtags
    hashtags.push('#local', '#community', '#neighborhood');
    return hashtags.slice(0, 5);
}
/**
 * Generates community and engagement hashtags
 */ function generateCommunityHashtags(businessType, targetAudience) {
    const communityMap = {
        'restaurant': [
            '#foodlovers',
            '#foodies',
            '#localfoodie',
            '#foodcommunity'
        ],
        'fitness': [
            '#fitnesscommunity',
            '#healthylifestyle',
            '#fitnessjourney',
            '#workoutbuddy'
        ],
        'beauty': [
            '#beautycommunity',
            '#selfcare',
            '#beautylovers',
            '#skincarecommunity'
        ],
        'retail': [
            '#shoplocal',
            '#supportlocal',
            '#shoppingcommunity',
            '#stylelovers'
        ],
        'technology': [
            '#techcommunity',
            '#developers',
            '#innovation',
            '#digitaltransformation'
        ],
        'healthcare': [
            '#healthcommunity',
            '#wellness',
            '#patientcare',
            '#healthylife'
        ],
        'education': [
            '#learningcommunity',
            '#students',
            '#educators',
            '#knowledge'
        ],
        'real_estate': [
            '#homeowners',
            '#investors',
            '#realestatecommunity',
            '#propertylovers'
        ],
        'automotive': [
            '#carenthusiasts',
            '#automotive',
            '#carlovers',
            '#drivingcommunity'
        ],
        'travel': [
            '#travelers',
            '#explorers',
            '#adventurers',
            '#wanderers'
        ]
    };
    const baseCommunity = communityMap[businessType.toLowerCase()] || [
        '#community',
        '#customers',
        '#supporters',
        '#family'
    ];
    // Add audience-specific hashtags if provided
    if (targetAudience) {
        const audienceWords = targetAudience.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
        const audienceHashtags = audienceWords.slice(0, 2).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
        return [
            ...baseCommunity.slice(0, 3),
            ...audienceHashtags
        ];
    }
    return baseCommunity.slice(0, 4);
}
function analyzeHashtags(hashtags) {
    return hashtags.map((hashtag)=>({
            hashtag,
            category: categorizeHashtag(hashtag),
            competitionLevel: estimateCompetition(hashtag),
            estimatedReach: estimateReach(hashtag),
            relevanceScore: Math.floor(Math.random() * 3) + 8 // Simplified scoring
        }));
}
function categorizeHashtag(hashtag) {
    const trending = [
        '#instagood',
        '#photooftheday',
        '#love',
        '#beautiful',
        '#happy',
        '#fitness',
        '#food'
    ];
    const location = hashtag.includes('local') || hashtag.includes('community');
    const branded = hashtag.includes('experience') || hashtag.includes('quality');
    if (trending.some((t)=>hashtag.includes(t.slice(1)))) return 'trending';
    if (location) return 'location';
    if (branded) return 'branded';
    return 'niche';
}
function estimateCompetition(hashtag) {
    const highCompetition = [
        '#love',
        '#instagood',
        '#photooftheday',
        '#beautiful',
        '#happy'
    ];
    const lowCompetition = hashtag.length > 15 || hashtag.includes('local');
    if (highCompetition.includes(hashtag)) return 'high';
    if (lowCompetition) return 'low';
    return 'medium';
}
function estimateReach(hashtag) {
    const highReach = [
        '#love',
        '#instagood',
        '#photooftheday',
        '#beautiful',
        '#happy'
    ];
    const lowReach = hashtag.length > 15 || hashtag.includes('local');
    if (highReach.includes(hashtag)) return 'high';
    if (lowReach) return 'low';
    return 'medium';
}
/**
 * Generates hashtags based on specific post topic and content
 */ function generateTopicSpecificHashtags(postTopic, designStyle) {
    if (!postTopic) return [];
    const postContent = postTopic.toLowerCase();
    const hashtags = [];
    // Content-based hashtag detection
    const contentKeywords = {
        // Food & Nutrition
        'food': [
            '#food',
            '#nutrition',
            '#healthy',
            '#delicious',
            '#fresh'
        ],
        'cookie': [
            '#cookies',
            '#snacks',
            '#treats',
            '#baked',
            '#homemade'
        ],
        'nutritious': [
            '#nutritious',
            '#healthy',
            '#wellness',
            '#goodforyou',
            '#natural'
        ],
        'malnutrition': [
            '#nutrition',
            '#health',
            '#wellness',
            '#community',
            '#impact'
        ],
        // Business actions
        'sale': [
            '#sale',
            '#discount',
            '#offer',
            '#deal',
            '#savings'
        ],
        'new': [
            '#new',
            '#launch',
            '#fresh',
            '#latest',
            '#innovation'
        ],
        'quality': [
            '#quality',
            '#premium',
            '#excellence',
            '#trusted',
            '#reliable'
        ],
        'service': [
            '#service',
            '#professional',
            '#expert',
            '#specialized',
            '#care'
        ],
        // Emotions & Values
        'future': [
            '#future',
            '#tomorrow',
            '#progress',
            '#growth',
            '#vision'
        ],
        'fighting': [
            '#fighting',
            '#mission',
            '#purpose',
            '#impact',
            '#change'
        ],
        'learn': [
            '#learn',
            '#education',
            '#knowledge',
            '#discover',
            '#grow'
        ],
        'experience': [
            '#experience',
            '#expertise',
            '#skilled',
            '#proven',
            '#established'
        ],
        // Industry specific
        'production': [
            '#production',
            '#manufacturing',
            '#quality',
            '#process',
            '#industry'
        ],
        'technology': [
            '#technology',
            '#innovation',
            '#digital',
            '#modern',
            '#advanced'
        ],
        'consulting': [
            '#consulting',
            '#advisory',
            '#expertise',
            '#solutions',
            '#strategy'
        ],
        'retail': [
            '#retail',
            '#shopping',
            '#products',
            '#customer',
            '#store'
        ]
    };
    // Extract relevant hashtags based on content
    Object.entries(contentKeywords).forEach(([keyword, tags])=>{
        if (postContent.includes(keyword)) {
            hashtags.push(...tags.slice(0, 2)); // Take first 2 from each matching category
        }
    });
    // Add random variation to avoid repetition
    const randomVariations = [
        '#amazing',
        '#incredible',
        '#outstanding',
        '#exceptional',
        '#remarkable',
        '#innovative',
        '#creative',
        '#unique',
        '#special',
        '#exclusive',
        '#authentic',
        '#genuine',
        '#original',
        '#custom',
        '#personalized',
        '#sustainable',
        '#eco',
        '#green',
        '#responsible',
        '#ethical'
    ];
    // Add 1-2 random variations for uniqueness
    const shuffled = randomVariations.sort(()=>0.5 - Math.random());
    hashtags.push(...shuffled.slice(0, 2));
    return [
        ...new Set(hashtags)
    ]; // Remove duplicates
}
/**
 * Generates hashtags based on design style and visual elements
 */ function generateDesignBasedHashtags(designStyle, businessType) {
    if (!designStyle) return [];
    const designMap = {
        'modern': [
            '#modern',
            '#clean',
            '#minimal',
            '#sleek',
            '#contemporary'
        ],
        'vintage': [
            '#vintage',
            '#retro',
            '#classic',
            '#timeless',
            '#nostalgic'
        ],
        'bold': [
            '#bold',
            '#vibrant',
            '#striking',
            '#powerful',
            '#dynamic'
        ],
        'elegant': [
            '#elegant',
            '#sophisticated',
            '#luxury',
            '#premium',
            '#refined'
        ],
        'playful': [
            '#playful',
            '#fun',
            '#creative',
            '#colorful',
            '#energetic'
        ],
        'minimalist': [
            '#minimalist',
            '#simple',
            '#clean',
            '#pure',
            '#essential'
        ],
        'professional': [
            '#professional',
            '#corporate',
            '#business',
            '#formal',
            '#polished'
        ],
        'artistic': [
            '#artistic',
            '#creative',
            '#design',
            '#art',
            '#visual'
        ],
        'trendy': [
            '#trendy',
            '#stylish',
            '#fashionable',
            '#current',
            '#hip'
        ],
        'warm': [
            '#warm',
            '#cozy',
            '#inviting',
            '#friendly',
            '#welcoming'
        ]
    };
    // Find matching design style
    const style = Object.keys(designMap).find((key)=>designStyle.toLowerCase().includes(key));
    return style ? designMap[style] : [];
}
/**
 * Selects exactly 10 most relevant hashtags from all categories
 */ function selectTop10Hashtags(allHashtags, platform, businessType, postTopic) {
    // Use timestamp for randomization seed to ensure different results each time
    const randomSeed = Date.now() % 1000;
    // Priority weights for different categories
    const categoryWeights = {
        topicSpecific: 3.0,
        designBased: 2.5,
        niche: 2.0,
        branded: 1.8,
        trending: 1.5,
        location: 1.3,
        community: 1.0 // Lower priority - general engagement
    };
    // Collect all hashtags with scores and add randomization
    const scoredHashtags = [];
    Object.entries(allHashtags).forEach(([category, hashtags])=>{
        const weight = categoryWeights[category] || 1.0;
        hashtags.forEach((hashtag, index)=>{
            // Avoid duplicates
            if (!scoredHashtags.some((item)=>item.hashtag === hashtag)) {
                // Add slight randomization to score to ensure variety
                const randomBonus = (randomSeed + index) % 100 / 1000;
                scoredHashtags.push({
                    hashtag,
                    score: weight + randomBonus,
                    category
                });
            }
        });
    });
    // Sort by score (highest first) and take top 10
    const top10 = scoredHashtags.sort((a, b)=>b.score - a.score).slice(0, 10).map((item)=>item.hashtag);
    // Return in the expected format (distribute across categories for compatibility)
    return {
        trending: top10.slice(0, 3),
        niche: top10.slice(3, 6),
        branded: top10.slice(6, 8),
        location: top10.slice(8, 9),
        community: top10.slice(9, 10),
        topicSpecific: [],
        designBased: []
    };
}
}}),
"[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Real-Time Trends Integration System
 * 
 * This module integrates multiple real-time trending topic sources
 * and provides a unified interface for getting current trends.
 */ __turbopack_context__.s({
    "TRENDING_CONFIG": (()=>TRENDING_CONFIG),
    "fetchCurrentNews": (()=>fetchCurrentNews),
    "fetchGoogleTrends": (()=>fetchGoogleTrends),
    "fetchLocalContext": (()=>fetchLocalContext),
    "fetchRedditTrends": (()=>fetchRedditTrends),
    "fetchTwitterTrends": (()=>fetchTwitterTrends)
});
const TRENDING_CONFIG = {
    sources: {
        googleTrends: {
            name: 'Google Trends RSS',
            enabled: process.env.GOOGLE_TRENDS_RSS_ENABLED === 'true',
            apiKey: undefined,
            baseUrl: 'https://trends.google.com/trends/trendingsearches/daily/rss',
            rateLimitPerHour: 1000 // RSS has higher limits
        },
        twitterApi: {
            name: 'Twitter API v1.1',
            enabled: false,
            apiKey: process.env.TWITTER_BEARER_TOKEN,
            baseUrl: 'https://api.twitter.com/1.1',
            rateLimitPerHour: 300
        },
        newsApi: {
            name: 'News API',
            enabled: false,
            apiKey: process.env.NEWS_API_KEY,
            baseUrl: 'https://newsapi.org/v2',
            rateLimitPerHour: 1000
        },
        redditApi: {
            name: 'Reddit RSS',
            enabled: process.env.REDDIT_RSS_ENABLED === 'true',
            apiKey: undefined,
            baseUrl: 'https://www.reddit.com',
            rateLimitPerHour: 1000 // RSS has higher limits
        },
        youtubeApi: {
            name: 'YouTube Data API',
            enabled: !!process.env.YOUTUBE_API_KEY,
            apiKey: process.env.YOUTUBE_API_KEY,
            baseUrl: 'https://www.googleapis.com/youtube/v3',
            rateLimitPerHour: 10000
        },
        eventbriteApi: {
            name: 'Eventbrite API',
            enabled: !!process.env.EVENTBRITE_API_KEY,
            apiKey: process.env.EVENTBRITE_API_KEY,
            baseUrl: 'https://www.eventbriteapi.com/v3',
            rateLimitPerHour: 1000
        },
        openWeatherApi: {
            name: 'OpenWeather API',
            enabled: !!process.env.OPENWEATHER_API_KEY,
            apiKey: process.env.OPENWEATHER_API_KEY,
            baseUrl: 'https://api.openweathermap.org/data/2.5',
            rateLimitPerHour: 1000
        }
    },
    fallbackToStatic: true,
    cacheTimeMinutes: 30
};
async function fetchGoogleTrends(location, category) {
    if (!TRENDING_CONFIG.sources.googleTrends.enabled) {
        console.log('Google Trends RSS not enabled, using fallback');
        return getGoogleTrendsFallback(location, category);
    }
    try {
        // Import RSS integration
        const { fetchGoogleTrendsRSS } = await __turbopack_context__.r("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use RSS feeds for Google Trends
        const geoCode = getGoogleTrendsGeoCode(location);
        const trends = await fetchGoogleTrendsRSS(geoCode, category);
        // Convert to expected format
        return trends.map((trend)=>({
                topic: trend.topic,
                relevanceScore: trend.relevanceScore,
                category: trend.category,
                timeframe: trend.timeframe,
                engagement_potential: trend.engagement_potential,
                source: 'google_trends_rss'
            }));
    } catch (error) {
        console.error('Error fetching Google Trends RSS:', error);
        return getGoogleTrendsFallback(location, category);
    }
}
async function fetchTwitterTrends(location, businessType) {
    if (!TRENDING_CONFIG.sources.twitterApi.enabled) {
        console.log('Twitter API not configured, using fallback');
        return getTwitterTrendsFallback(location, businessType);
    }
    try {
        const woeid = getTwitterWOEID(location);
        // Use Twitter API v2 trending topics endpoint
        const response = await fetch(`${TRENDING_CONFIG.sources.twitterApi.baseUrl}/trends/place.json?id=${woeid}`, {
            headers: {
                'Authorization': `Bearer ${TRENDING_CONFIG.sources.twitterApi.apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'TrendingTopicsBot/2.0'
            }
        });
        if (!response.ok) {
            console.log(`Twitter API response: ${response.status} ${response.statusText}`);
            throw new Error(`Twitter API error: ${response.status}`);
        }
        const data = await response.json();
        console.log(`✅ Twitter API returned ${data.length || 0} trend locations`);
        // Process Twitter trends data
        return processTwitterTrendsData(data, businessType);
    } catch (error) {
        console.error('Error fetching Twitter trends:', error);
        return getTwitterTrendsFallback(location, businessType);
    }
}
async function fetchCurrentNews(location, businessType, category) {
    if (!TRENDING_CONFIG.sources.newsApi.enabled) {
        console.log('News API not configured, using fallback');
        return getNewsFallback(location, businessType, category);
    }
    try {
        const params = new URLSearchParams({
            country: getNewsApiCountryCode(location),
            category: category || 'business',
            pageSize: '10',
            apiKey: TRENDING_CONFIG.sources.newsApi.apiKey
        });
        console.log(`🔍 Fetching news from News API for ${location}...`);
        const response = await fetch(`${TRENDING_CONFIG.sources.newsApi.baseUrl}/top-headlines?${params}`);
        if (!response.ok) {
            console.log(`News API response: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.log('News API error details:', errorText);
            throw new Error(`News API error: ${response.status}`);
        }
        const data = await response.json();
        console.log(`✅ News API returned ${data.articles?.length || 0} articles`);
        // Process news data
        return processNewsData(data, businessType);
    } catch (error) {
        console.error('Error fetching news:', error);
        return getNewsFallback(location, businessType, category);
    }
}
async function fetchRedditTrends(businessType, platform) {
    if (!TRENDING_CONFIG.sources.redditApi.enabled) {
        console.log('Reddit RSS not enabled, using fallback');
        return getRedditTrendsFallback(businessType, platform);
    }
    try {
        // Import RSS integration
        const { fetchRedditRSS } = await __turbopack_context__.r("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use RSS feeds for Reddit trends
        const trends = await fetchRedditRSS(businessType);
        // Convert to expected format
        return trends.map((trend)=>({
                topic: trend.topic,
                relevanceScore: trend.relevanceScore,
                category: trend.category,
                timeframe: trend.timeframe,
                engagement_potential: trend.engagement_potential,
                source: 'reddit_rss'
            }));
    } catch (error) {
        console.error('Error fetching Reddit RSS:', error);
        return getRedditTrendsFallback(businessType, platform);
    }
}
/**
 * Helper functions for processing API data
 */ function processGoogleTrendsData(data, location, category) {
    // Process Google Trends API response
    return [
        {
            topic: `Trending in ${location}`,
            source: 'google_trends',
            relevanceScore: 9,
            category: category || 'general',
            timeframe: 'now',
            engagement_potential: 'high'
        }
    ];
}
function processTwitterTrendsData(data, businessType) {
    // Process Twitter API response
    if (data && data[0] && data[0].trends) {
        return data[0].trends.slice(0, 10).map((trend)=>({
                topic: trend.name,
                source: 'twitter',
                relevanceScore: trend.tweet_volume ? Math.min(10, Math.log10(trend.tweet_volume)) : 5,
                category: 'social',
                timeframe: 'now',
                engagement_potential: trend.tweet_volume > 10000 ? 'high' : 'medium'
            }));
    }
    return [];
}
function processNewsData(data, businessType) {
    // Process News API response
    if (data && data.articles) {
        return data.articles.slice(0, 8).map((article)=>({
                topic: article.title,
                source: 'news',
                relevanceScore: 8,
                category: 'news',
                timeframe: 'today',
                engagement_potential: 'high',
                business_angle: `How this relates to ${businessType} industry`
            }));
    }
    return [];
}
function processRedditData(data, subreddit) {
    // Process Reddit API response
    if (data && data.data && data.data.children) {
        return data.data.children.slice(0, 5).map((post)=>({
                topic: post.data.title,
                source: 'reddit',
                relevanceScore: Math.min(10, post.data.score / 100),
                category: 'community',
                timeframe: 'today',
                engagement_potential: post.data.score > 1000 ? 'high' : 'medium',
                subreddit: subreddit
            }));
    }
    return [];
}
/**
 * Helper functions for API parameters
 */ function getGoogleTrendsGeoCode(location) {
    const geoMap = {
        'kenya': 'KE',
        'united states': 'US',
        'nairobi': 'KE',
        'new york': 'US-NY',
        'london': 'GB-ENG'
    };
    return geoMap[location.toLowerCase()] || 'US';
}
function getTwitterWOEID(location) {
    const woeidMap = {
        'kenya': '23424863',
        'united states': '23424977',
        'nairobi': '1528488',
        'new york': '2459115',
        'london': '44418'
    };
    return woeidMap[location.toLowerCase()] || '1'; // Worldwide
}
function getNewsApiCountryCode(location) {
    const countryMap = {
        'kenya': 'ke',
        'united states': 'us',
        'nairobi': 'ke',
        'new york': 'us',
        'london': 'gb'
    };
    return countryMap[location.toLowerCase()] || 'us';
}
function getRelevantSubreddits(businessType) {
    const subredditMap = {
        'financial technology software': [
            'fintech',
            'personalfinance',
            'investing',
            'entrepreneur'
        ],
        'restaurant': [
            'food',
            'recipes',
            'restaurantowners',
            'smallbusiness'
        ],
        'fitness': [
            'fitness',
            'bodybuilding',
            'nutrition',
            'personaltrainer'
        ],
        'technology': [
            'technology',
            'programming',
            'startups',
            'artificial'
        ]
    };
    return subredditMap[businessType.toLowerCase()] || [
        'business',
        'entrepreneur'
    ];
}
/**
 * Fallback functions when APIs are not available
 */ function getGoogleTrendsFallback(location, category) {
    return [
        {
            topic: `Local business trends in ${location}`,
            source: 'fallback',
            relevanceScore: 7,
            category: category || 'business',
            timeframe: 'week',
            engagement_potential: 'medium'
        }
    ];
}
function getTwitterTrendsFallback(location, businessType) {
    return [
        {
            topic: '#MondayMotivation',
            source: 'fallback',
            relevanceScore: 6,
            category: 'social',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
function getNewsFallback(location, businessType, category) {
    return [
        {
            topic: `${businessType} industry updates`,
            source: 'fallback',
            relevanceScore: 6,
            category: 'news',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
function getRedditTrendsFallback(businessType, platform) {
    return [
        {
            topic: `${businessType} community discussions`,
            source: 'fallback',
            relevanceScore: 5,
            category: 'community',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
async function fetchLocalContext(location, businessType) {
    const context = {};
    try {
        // Fetch weather context
        if (TRENDING_CONFIG.sources.openWeatherApi.enabled) {
            console.log(`🌤️ Fetching weather context for ${location}...`);
            const params = new URLSearchParams({
                q: location,
                appid: TRENDING_CONFIG.sources.openWeatherApi.apiKey,
                units: 'metric'
            });
            const response = await fetch(`${TRENDING_CONFIG.sources.openWeatherApi.baseUrl}/weather?${params}`);
            if (response.ok) {
                const weatherData = await response.json();
                context.weather = {
                    temperature: Math.round(weatherData.main.temp),
                    condition: weatherData.weather[0].main,
                    business_impact: generateBusinessWeatherImpact(weatherData.weather[0].main, weatherData.main.temp, businessType),
                    content_opportunities: generateWeatherContentOpportunities(weatherData.weather[0].main, weatherData.main.temp, businessType)
                };
                console.log(`✅ Weather: ${context.weather.temperature}°C, ${context.weather.condition}`);
            }
        }
        // Fetch events context
        if (TRENDING_CONFIG.sources.eventbriteApi.enabled) {
            console.log(`🎪 Fetching events context for ${location}...`);
            const params = new URLSearchParams({
                'location.address': location,
                'location.within': '25km',
                'start_date.range_start': new Date().toISOString(),
                'start_date.range_end': new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                'sort_by': 'relevance',
                'page_size': '10'
            });
            const response = await fetch(`${TRENDING_CONFIG.sources.eventbriteApi.baseUrl}/events/search/?${params}`, {
                headers: {
                    'Authorization': `Bearer ${TRENDING_CONFIG.sources.eventbriteApi.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.ok) {
                const eventsData = await response.json();
                context.events = (eventsData.events || []).slice(0, 5).map((event)=>({
                        name: event.name?.text || 'Event',
                        category: event.category?.name || 'General',
                        relevance_score: calculateEventRelevance(event, businessType),
                        start_date: event.start?.local || event.start?.utc
                    }));
                console.log(`✅ Found ${context.events.length} relevant events`);
            }
        }
    } catch (error) {
        console.error('Error fetching local context:', error);
    }
    return context;
}
// Helper functions for weather and events
function generateBusinessWeatherImpact(condition, temperature, businessType) {
    const businessImpacts = {
        'restaurant': {
            'sunny': 'Perfect weather for outdoor dining and patio service',
            'rain': 'Great opportunity to promote cozy indoor dining experience',
            'hot': 'Ideal time to highlight refreshing drinks and cool dishes',
            'cold': 'Perfect weather for warm comfort food and hot beverages'
        },
        'fitness': {
            'sunny': 'Excellent conditions for outdoor workouts and activities',
            'rain': 'Great time to promote indoor fitness programs',
            'hot': 'Important to emphasize hydration and cooling strategies',
            'cold': 'Perfect for promoting warm-up routines and indoor training'
        },
        'financial technology software': {
            'sunny': 'Great weather for outdoor meetings and client visits',
            'rain': 'Perfect time for indoor productivity and digital solutions',
            'hot': 'Ideal for promoting mobile solutions and remote services',
            'cold': 'Good time for cozy indoor planning and financial reviews'
        }
    };
    const businessKey = businessType.toLowerCase();
    const impacts = businessImpacts[businessKey] || businessImpacts['restaurant'];
    if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';
    if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';
    return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';
}
function generateWeatherContentOpportunities(condition, temperature, businessType) {
    const opportunities = [];
    // Temperature-based opportunities
    if (temperature > 25) {
        opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');
    } else if (temperature < 10) {
        opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');
    }
    // Condition-based opportunities
    switch(condition.toLowerCase()){
        case 'rain':
            opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');
            break;
        case 'sunny':
        case 'clear':
            opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');
            break;
        case 'clouds':
            opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');
            break;
    }
    return opportunities;
}
function calculateEventRelevance(event, businessType) {
    let score = 5; // Base score
    const eventName = (event.name?.text || '').toLowerCase();
    const eventCategory = (event.category?.name || '').toLowerCase();
    // Business type relevance
    const businessKeywords = getBusinessKeywords(businessType);
    for (const keyword of businessKeywords){
        if (eventName.includes(keyword) || eventCategory.includes(keyword)) {
            score += 2;
        }
    }
    // Event category bonus
    if (eventCategory.includes('business') || eventCategory.includes('networking')) {
        score += 1;
    }
    return Math.min(10, score);
}
function getBusinessKeywords(businessType) {
    const keywordMap = {
        'financial technology software': [
            'fintech',
            'finance',
            'banking',
            'payment',
            'blockchain',
            'startup',
            'tech'
        ],
        'restaurant': [
            'food',
            'culinary',
            'cooking',
            'dining',
            'chef',
            'restaurant'
        ],
        'fitness': [
            'fitness',
            'health',
            'wellness',
            'gym',
            'workout',
            'nutrition'
        ],
        'technology': [
            'tech',
            'software',
            'programming',
            'ai',
            'digital',
            'innovation'
        ]
    };
    return keywordMap[businessType.toLowerCase()] || [
        'business',
        'networking',
        'professional'
    ];
}
}}),
"[project]/src/ai/utils/trending-topics.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Topics and Market Intelligence System
 *
 * This module provides real-time trending topics, competitor analysis,
 * and market intelligence for content optimization.
 */ __turbopack_context__.s({
    "generateCompetitorInsights": (()=>generateCompetitorInsights),
    "generateCulturalContext": (()=>generateCulturalContext),
    "generateMarketIntelligence": (()=>generateMarketIntelligence),
    "generateRealTimeTrendingTopics": (()=>generateRealTimeTrendingTopics),
    "generateStaticTrendingTopics": (()=>generateStaticTrendingTopics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)");
;
async function generateRealTimeTrendingTopics(businessType, location, platform = 'general') {
    try {
        console.log(`🔍 Fetching real-time trends for ${businessType} in ${location}...`);
        console.log(`📱 Platform: ${platform}`);
        // Fetch from working real-time sources (temporarily disable failing APIs)
        console.log('🌐 Starting RSS feeds fetch...');
        const [googleTrends, redditTrends] = await Promise.allSettled([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchGoogleTrends"])(location, businessType),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchRedditTrends"])(businessType, platform)
        ]);
        console.log(`📊 Google Trends status: ${googleTrends.status}`);
        console.log(`📊 Reddit Trends status: ${redditTrends.status}`);
        // Temporarily disable failing APIs until we fix them
        const twitterTrends = {
            status: 'rejected',
            reason: 'Temporarily disabled'
        };
        const currentNews = {
            status: 'rejected',
            reason: 'Temporarily disabled'
        };
        const allTrends = [];
        // Process Google Trends
        if (googleTrends.status === 'fulfilled') {
            console.log(`✅ Google Trends: ${googleTrends.value.length} trends received`);
            allTrends.push(...googleTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // Process Twitter Trends
        if (twitterTrends.status === 'fulfilled') {
            allTrends.push(...twitterTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // Process News
        if (currentNews.status === 'fulfilled') {
            allTrends.push(...currentNews.value.map((news)=>({
                    topic: news.topic,
                    relevanceScore: news.relevanceScore,
                    platform: platform,
                    category: news.category,
                    timeframe: news.timeframe,
                    engagement_potential: news.engagement_potential
                })));
        } else {
            console.warn(`⚠️ Google Trends failed:`, googleTrends.reason);
        }
        // Process Reddit Trends
        if (redditTrends.status === 'fulfilled') {
            console.log(`✅ Reddit Trends: ${redditTrends.value.length} trends received`);
            allTrends.push(...redditTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        } else {
            console.warn(`⚠️ Reddit Trends failed:`, redditTrends.reason);
        }
        // If we have real-time trends, use them
        if (allTrends.length > 0) {
            console.log(`✅ Found ${allTrends.length} real-time trends`);
            return allTrends.sort((a, b)=>b.relevanceScore - a.relevanceScore).slice(0, 10);
        }
        // Fallback to static trends
        console.log('⚠️ No real-time trends available, using static fallback');
        return generateStaticTrendingTopics(businessType, location, platform);
    } catch (error) {
        console.error('Error fetching real-time trends:', error);
        return generateStaticTrendingTopics(businessType, location, platform);
    }
}
function generateStaticTrendingTopics(businessType, location, platform = 'general') {
    const businessTrends = {
        'restaurant': [
            {
                topic: 'Sustainable dining trends',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Local food festivals',
                relevanceScore: 8,
                platform: 'facebook',
                category: 'local',
                timeframe: 'week',
                engagement_potential: 'high'
            },
            {
                topic: 'Plant-based menu innovations',
                relevanceScore: 7,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'month',
                engagement_potential: 'medium'
            }
        ],
        'fitness': [
            {
                topic: 'New Year fitness resolutions',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Mental health and exercise',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'lifestyle',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Home workout equipment trends',
                relevanceScore: 7,
                platform: 'facebook',
                category: 'lifestyle',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'technology': [
            {
                topic: 'AI in business automation',
                relevanceScore: 10,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Cybersecurity awareness',
                relevanceScore: 9,
                platform: 'twitter',
                category: 'technology',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Remote work productivity tools',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'beauty': [
            {
                topic: 'Clean beauty movement',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Skincare for different seasons',
                relevanceScore: 8,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Sustainable beauty packaging',
                relevanceScore: 7,
                platform: 'facebook',
                category: 'lifestyle',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'financial technology software': [
            {
                topic: 'Digital banking adoption in Africa',
                relevanceScore: 10,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Financial inclusion initiatives',
                relevanceScore: 9,
                platform: 'twitter',
                category: 'business',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Mobile payment security',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ]
    };
    // Get base trends for business type
    const baseTrends = businessTrends[businessType.toLowerCase()] || businessTrends['technology'];
    // Add location-specific trends
    const locationTrends = generateLocationTrends(location);
    // Combine and filter by platform
    const allTrends = [
        ...baseTrends,
        ...locationTrends
    ];
    return allTrends.filter((trend)=>trend.platform === platform || trend.platform === 'general').sort((a, b)=>b.relevanceScore - a.relevanceScore).slice(0, 5);
}
/**
 * Generates location-specific trending topics
 */ function generateLocationTrends(location) {
    const locationMap = {
        'nairobi': [
            {
                topic: 'Nairobi tech hub growth',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'high'
            },
            {
                topic: 'Kenyan startup ecosystem',
                relevanceScore: 7,
                platform: 'twitter',
                category: 'business',
                timeframe: 'today',
                engagement_potential: 'medium'
            }
        ],
        'new york': [
            {
                topic: 'NYC small business support',
                relevanceScore: 8,
                platform: 'facebook',
                category: 'local',
                timeframe: 'week',
                engagement_potential: 'high'
            }
        ],
        'london': [
            {
                topic: 'London fintech innovation',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'high'
            }
        ]
    };
    const locationKey = location.toLowerCase().split(',')[0].trim();
    return locationMap[locationKey] || [];
}
function generateCompetitorInsights(businessType, location, services) {
    const competitorStrategies = {
        'financial technology software': [
            {
                competitor_name: 'Traditional Banks',
                content_gap: 'Lack of educational content about digital banking benefits',
                differentiation_opportunity: 'Focus on simplicity and accessibility for everyday users',
                successful_strategy: 'Trust-building through security messaging',
                avoid_strategy: 'Overly technical jargon that confuses users'
            },
            {
                competitor_name: 'Other Fintech Apps',
                content_gap: 'Limited focus on local market needs and culture',
                differentiation_opportunity: 'Emphasize local partnerships and community impact',
                successful_strategy: 'User testimonials and success stories',
                avoid_strategy: 'Generic global messaging without local relevance'
            }
        ],
        'restaurant': [
            {
                competitor_name: 'Chain Restaurants',
                content_gap: 'Lack of personal connection and local community focus',
                differentiation_opportunity: 'Highlight local sourcing, chef personality, and community involvement',
                successful_strategy: 'Behind-the-scenes content and food preparation videos',
                avoid_strategy: 'Generic food photos without story or context'
            }
        ],
        'fitness': [
            {
                competitor_name: 'Large Gym Chains',
                content_gap: 'Impersonal approach and lack of individual attention',
                differentiation_opportunity: 'Focus on personal transformation stories and community support',
                successful_strategy: 'Client success stories and progress tracking',
                avoid_strategy: 'Intimidating fitness content that discourages beginners'
            }
        ]
    };
    return competitorStrategies[businessType.toLowerCase()] || [
        {
            competitor_name: 'Industry Leaders',
            content_gap: 'Generic messaging without personal touch',
            differentiation_opportunity: 'Focus on authentic storytelling and customer relationships',
            successful_strategy: 'Educational content that provides real value',
            avoid_strategy: 'Overly promotional content without substance'
        }
    ];
}
function generateCulturalContext(location) {
    const culturalMap = {
        'nairobi, kenya': {
            location: 'Nairobi, Kenya',
            cultural_nuances: [
                'Ubuntu philosophy - community and interconnectedness',
                'Respect for elders and traditional values',
                'Entrepreneurial spirit and innovation mindset',
                'Multilingual communication (English, Swahili, local languages)'
            ],
            local_customs: [
                'Harambee - community cooperation and fundraising',
                'Greeting customs and respect protocols',
                'Religious diversity and tolerance',
                'Family-centered decision making'
            ],
            language_preferences: [
                'Mix of English and Swahili phrases',
                'Respectful and formal tone in business contexts',
                'Storytelling and proverb usage',
                'Community-focused language'
            ],
            seasonal_relevance: [
                'Rainy seasons (March-May, October-December)',
                'School calendar considerations',
                'Agricultural seasons and harvest times',
                'Holiday seasons and celebrations'
            ],
            local_events: [
                'Nairobi Innovation Week',
                'Kenya Music Festival',
                'Nairobi Restaurant Week',
                'Local cultural festivals'
            ]
        }
    };
    const locationKey = location.toLowerCase();
    return culturalMap[locationKey] || {
        location: location,
        cultural_nuances: [
            'Local community values',
            'Regional business customs'
        ],
        local_customs: [
            'Local traditions',
            'Community practices'
        ],
        language_preferences: [
            'Local language nuances',
            'Regional communication styles'
        ],
        seasonal_relevance: [
            'Local seasons',
            'Regional events'
        ],
        local_events: [
            'Local festivals',
            'Community gatherings'
        ]
    };
}
function generateMarketIntelligence(businessType, location, platform, services) {
    return {
        trending_topics: generateStaticTrendingTopics(businessType, location, platform),
        competitor_insights: generateCompetitorInsights(businessType, location, services),
        cultural_context: generateCulturalContext(location),
        viral_content_patterns: [
            'Behind-the-scenes authentic moments',
            'User-generated content and testimonials',
            'Educational content that solves problems',
            'Emotional storytelling with clear outcomes',
            'Interactive content that encourages participation'
        ],
        engagement_triggers: [
            'Ask questions that require personal responses',
            'Share relatable struggles and solutions',
            'Use local references and cultural touchpoints',
            'Create content that people want to share with friends',
            'Provide exclusive insights or early access'
        ]
    };
}
}}),
"[project]/src/ai/utils/intelligent-context-selector.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Intelligent Context Selector
 * 
 * This module acts like a local expert who knows what information
 * is relevant for each business type, location, and content context.
 * It intelligently selects which data to use and which to ignore.
 */ __turbopack_context__.s({
    "filterContextData": (()=>filterContextData),
    "selectRelevantContext": (()=>selectRelevantContext)
});
function selectRelevantContext(businessType, location, platform, contentThemes, dayOfWeek) {
    const businessKey = businessType.toLowerCase();
    const locationKey = location.toLowerCase();
    const isWeekend = dayOfWeek === 'Saturday' || dayOfWeek === 'Sunday';
    return {
        weather: analyzeWeatherRelevance(businessKey, locationKey, platform, isWeekend),
        events: analyzeEventsRelevance(businessKey, locationKey, platform, isWeekend),
        trends: analyzeTrendsRelevance(businessKey, locationKey, platform),
        cultural: analyzeCulturalRelevance(businessKey, locationKey, platform)
    };
}
/**
 * Determines if weather information is relevant for this business/location
 */ function analyzeWeatherRelevance(businessType, location, platform, isWeekend) {
    // High weather relevance businesses
    const weatherSensitiveBusinesses = [
        'restaurant',
        'cafe',
        'food',
        'dining',
        'fitness',
        'gym',
        'sports',
        'outdoor',
        'retail',
        'shopping',
        'fashion',
        'tourism',
        'travel',
        'hotel',
        'construction',
        'agriculture',
        'delivery',
        'transportation'
    ];
    // Medium weather relevance
    const moderateWeatherBusinesses = [
        'beauty',
        'spa',
        'wellness',
        'entertainment',
        'events',
        'real estate',
        'automotive'
    ];
    // Low/No weather relevance
    const weatherIndependentBusinesses = [
        'financial technology software',
        'fintech',
        'banking',
        'software',
        'technology',
        'saas',
        'consulting',
        'legal',
        'accounting',
        'insurance',
        'healthcare',
        'education',
        'digital marketing',
        'design'
    ];
    // Check business type relevance
    const isHighRelevance = weatherSensitiveBusinesses.some((type)=>businessType.includes(type));
    const isMediumRelevance = moderateWeatherBusinesses.some((type)=>businessType.includes(type));
    const isLowRelevance = weatherIndependentBusinesses.some((type)=>businessType.includes(type));
    // Location-based adjustments
    const isExtremeWeatherLocation = location.includes('nairobi') || location.includes('kenya') || location.includes('tropical');
    if (isHighRelevance) {
        return {
            useWeather: true,
            relevanceReason: `${businessType} customers are highly influenced by weather conditions`,
            priority: 'high'
        };
    }
    if (isMediumRelevance) {
        return {
            useWeather: true,
            relevanceReason: `Weather can impact ${businessType} customer behavior`,
            priority: 'medium'
        };
    }
    if (isLowRelevance) {
        return {
            useWeather: false,
            relevanceReason: `${businessType} operates independently of weather conditions`,
            priority: 'ignore'
        };
    }
    // Default case
    return {
        useWeather: isExtremeWeatherLocation,
        relevanceReason: isExtremeWeatherLocation ? 'Local weather is culturally significant' : 'Weather has minimal business impact',
        priority: isExtremeWeatherLocation ? 'low' : 'ignore'
    };
}
/**
 * Determines if local events are relevant for this business/location
 */ function analyzeEventsRelevance(businessType, location, platform, isWeekend) {
    // Always relevant for networking/community businesses
    const networkingBusinesses = [
        'consulting',
        'marketing',
        'business services',
        'financial technology software',
        'fintech',
        'real estate',
        'insurance',
        'legal'
    ];
    // Event-dependent businesses
    const eventDependentBusinesses = [
        'restaurant',
        'entertainment',
        'retail',
        'fitness',
        'beauty',
        'tourism'
    ];
    // B2B vs B2C consideration
    const isB2B = networkingBusinesses.some((type)=>businessType.includes(type)) || businessType.includes('software') || businessType.includes('technology');
    const isB2C = eventDependentBusinesses.some((type)=>businessType.includes(type));
    // Relevant event types based on business
    let eventTypes = [];
    if (isB2B) {
        eventTypes = [
            'business',
            'networking',
            'conference',
            'workshop',
            'professional'
        ];
    }
    if (isB2C) {
        eventTypes = [
            'community',
            'festival',
            'entertainment',
            'cultural',
            'local'
        ];
    }
    // Location-based event culture
    const isEventCentricLocation = location.includes('nairobi') || location.includes('new york') || location.includes('london');
    if (isB2B && isEventCentricLocation) {
        return {
            useEvents: true,
            relevanceReason: `${businessType} benefits from professional networking events`,
            priority: 'high',
            eventTypes
        };
    }
    if (isB2C) {
        return {
            useEvents: true,
            relevanceReason: `Local events drive foot traffic for ${businessType}`,
            priority: 'medium',
            eventTypes
        };
    }
    return {
        useEvents: isEventCentricLocation,
        relevanceReason: isEventCentricLocation ? 'Local events show community engagement' : 'Events have minimal business relevance',
        priority: isEventCentricLocation ? 'low' : 'ignore',
        eventTypes: [
            'community'
        ]
    };
}
/**
 * Determines trending topics relevance
 */ function analyzeTrendsRelevance(businessType, location, platform) {
    // Always use trends for social media businesses
    const trendDependentBusinesses = [
        'marketing',
        'social media',
        'content',
        'entertainment',
        'fashion',
        'beauty',
        'technology',
        'startup'
    ];
    // Industry-specific trend types
    let trendTypes = [];
    if (businessType.includes('technology') || businessType.includes('fintech')) {
        trendTypes = [
            'technology',
            'business',
            'innovation',
            'startup'
        ];
    } else if (businessType.includes('restaurant') || businessType.includes('food')) {
        trendTypes = [
            'food',
            'lifestyle',
            'local',
            'cultural'
        ];
    } else if (businessType.includes('fitness')) {
        trendTypes = [
            'health',
            'wellness',
            'lifestyle',
            'sports'
        ];
    } else {
        trendTypes = [
            'business',
            'local',
            'community'
        ];
    }
    const isTrendSensitive = trendDependentBusinesses.some((type)=>businessType.includes(type));
    // Platform consideration
    const isSocialPlatform = platform === 'instagram' || platform === 'twitter';
    return {
        useTrends: true,
        relevanceReason: isTrendSensitive ? `${businessType} thrives on current trends and conversations` : 'Trending topics increase content relevance and engagement',
        priority: isTrendSensitive ? 'high' : 'medium',
        trendTypes
    };
}
/**
 * Determines cultural context relevance
 */ function analyzeCulturalRelevance(businessType, location, platform) {
    // Always high relevance for local businesses
    const localBusinesses = [
        'restaurant',
        'retail',
        'fitness',
        'beauty',
        'real estate',
        'healthcare',
        'education'
    ];
    // Cultural elements to emphasize
    let culturalElements = [];
    if (location.includes('nairobi') || location.includes('kenya')) {
        culturalElements = [
            'ubuntu philosophy',
            'harambee spirit',
            'swahili expressions',
            'community values'
        ];
    } else if (location.includes('new york')) {
        culturalElements = [
            'diversity',
            'hustle culture',
            'innovation',
            'fast-paced lifestyle'
        ];
    } else if (location.includes('london')) {
        culturalElements = [
            'tradition',
            'multiculturalism',
            'business etiquette',
            'dry humor'
        ];
    } else {
        culturalElements = [
            'local customs',
            'community values',
            'regional preferences'
        ];
    }
    const isLocalBusiness = localBusinesses.some((type)=>businessType.includes(type));
    const isInternationalLocation = !location.includes('united states');
    return {
        useCultural: true,
        relevanceReason: isLocalBusiness ? `Local ${businessType} must connect with community culture` : 'Cultural awareness builds authentic connections',
        priority: isLocalBusiness || isInternationalLocation ? 'high' : 'medium',
        culturalElements
    };
}
function filterContextData(relevance, availableData) {
    const result = {
        contextInstructions: generateContextInstructions(relevance)
    };
    // Filter weather data
    if (relevance.weather.useWeather && availableData.weather) {
        result.selectedWeather = availableData.weather;
    }
    // Filter events data
    if (relevance.events.useEvents && availableData.events) {
        result.selectedEvents = availableData.events.filter((event)=>relevance.events.eventTypes.some((type)=>event.category?.toLowerCase().includes(type) || event.name?.toLowerCase().includes(type))).slice(0, relevance.events.priority === 'high' ? 3 : 1);
    }
    // Filter trends data
    if (relevance.trends.useTrends && availableData.trends) {
        result.selectedTrends = availableData.trends.filter((trend)=>relevance.trends.trendTypes.some((type)=>trend.category?.toLowerCase().includes(type) || trend.topic?.toLowerCase().includes(type))).slice(0, relevance.trends.priority === 'high' ? 5 : 3);
    }
    // Filter cultural data
    if (relevance.cultural.useCultural && availableData.cultural) {
        result.selectedCultural = {
            ...availableData.cultural,
            cultural_nuances: availableData.cultural.cultural_nuances?.filter((nuance)=>relevance.cultural.culturalElements.some((element)=>nuance.toLowerCase().includes(element.toLowerCase()))).slice(0, 3)
        };
    }
    return result;
}
/**
 * Generates context-specific instructions for the AI
 */ function generateContextInstructions(relevance) {
    const instructions = [];
    if (relevance.weather.useWeather) {
        if (relevance.weather.priority === 'high') {
            instructions.push('WEATHER: Integrate weather naturally as it significantly impacts customer behavior');
        } else if (relevance.weather.priority === 'medium') {
            instructions.push('WEATHER: Mention weather subtly if it adds value to the message');
        }
    } else {
        instructions.push('WEATHER: Ignore weather data - not relevant for this business type');
    }
    if (relevance.events.useEvents) {
        if (relevance.events.priority === 'high') {
            instructions.push('EVENTS: Highlight relevant local events as key business opportunities');
        } else {
            instructions.push('EVENTS: Reference events only if they add community connection value');
        }
    } else {
        instructions.push('EVENTS: Skip event references - focus on core business value');
    }
    if (relevance.trends.priority === 'high') {
        instructions.push('TRENDS: Lead with trending topics to maximize engagement and relevance');
    } else {
        instructions.push('TRENDS: Use trends subtly to add contemporary relevance');
    }
    if (relevance.cultural.priority === 'high') {
        instructions.push('CULTURE: Deeply integrate local cultural elements for authentic connection');
    } else {
        instructions.push('CULTURE: Include respectful cultural awareness without overdoing it');
    }
    return instructions.join('\n');
}
}}),
"[project]/src/ai/utils/human-content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Human-Like Content Generation System
 * 
 * This module provides techniques to make AI-generated content
 * feel authentic, human, and engaging while avoiding AI detection.
 */ __turbopack_context__.s({
    "generateContentOptimization": (()=>generateContentOptimization),
    "generateHumanizationTechniques": (()=>generateHumanizationTechniques),
    "generateTrafficDrivingElements": (()=>generateTrafficDrivingElements)
});
function generateHumanizationTechniques(businessType, brandVoice, location) {
    const basePersonality = getPersonalityMarkers(brandVoice);
    const industryAuthenticity = getIndustryAuthenticity(businessType);
    const locationConversation = getLocationConversation(location);
    return {
        personality_markers: [
            ...basePersonality,
            'Use first-person perspective occasionally',
            'Include personal opinions and preferences',
            'Show vulnerability and learning moments',
            'Express genuine excitement about successes'
        ],
        authenticity_elements: [
            ...industryAuthenticity,
            'Share behind-the-scenes moments',
            'Admit mistakes and lessons learned',
            'Use specific details instead of generalities',
            'Reference real experiences and observations',
            'Include time-specific references (today, this morning, etc.)'
        ],
        conversational_patterns: [
            ...locationConversation,
            'Start sentences with "You know what?"',
            'Use rhetorical questions naturally',
            'Include conversational fillers like "honestly" or "actually"',
            'Break up long thoughts with shorter sentences',
            'Use contractions (we\'re, don\'t, can\'t) naturally'
        ],
        storytelling_devices: [
            'Start with "I remember when..." or "Last week..."',
            'Use the "But here\'s the thing..." transition',
            'Include dialogue: "My customer said..."',
            'Paint vivid scenes with sensory details',
            'End with unexpected insights or realizations'
        ],
        emotional_connectors: [
            'Share moments of doubt and breakthrough',
            'Express genuine gratitude to customers',
            'Show empathy for customer struggles',
            'Celebrate small wins with enthusiasm',
            'Use emotional language that resonates'
        ],
        imperfection_markers: [
            'Occasional typos that feel natural (but not distracting)',
            'Slightly informal grammar in casual contexts',
            'Stream-of-consciousness moments',
            'Self-corrections: "Actually, let me rephrase that..."',
            'Honest admissions: "I\'m still figuring this out..."'
        ]
    };
}
function generateTrafficDrivingElements(businessType, platform, targetAudience) {
    return {
        viral_hooks: [
            'Controversial but respectful opinions',
            'Surprising industry statistics',
            'Before/after transformations',
            'Myth-busting content',
            'Exclusive behind-the-scenes reveals',
            'Timely reactions to trending topics',
            'Unexpected collaborations or partnerships'
        ],
        engagement_magnets: [
            'Fill-in-the-blank questions',
            'This or that choices',
            'Caption this photo challenges',
            'Share your experience prompts',
            'Prediction requests',
            'Opinion polls and surveys',
            'Challenge participation invites'
        ],
        conversion_triggers: [
            'Limited-time offers with urgency',
            'Exclusive access for followers',
            'Free valuable resources',
            'Personal consultation offers',
            'Early bird opportunities',
            'Member-only benefits',
            'Referral incentives'
        ],
        shareability_factors: [
            'Relatable everyday struggles',
            'Inspirational success stories',
            'Useful tips people want to save',
            'Funny observations about the industry',
            'Heartwarming customer stories',
            'Educational content that teaches',
            'Content that makes people look smart for sharing'
        ],
        curiosity_gaps: [
            'The one thing nobody tells you about...',
            'What happened next will surprise you...',
            'The secret that changed everything...',
            'Why everyone is wrong about...',
            'The mistake I made that taught me...',
            'What I wish I knew before...',
            'The truth about... that nobody talks about'
        ],
        social_proof_elements: [
            'Customer testimonials and reviews',
            'User-generated content features',
            'Industry recognition and awards',
            'Media mentions and press coverage',
            'Collaboration with respected figures',
            'Community size and engagement stats',
            'Success metrics and achievements'
        ]
    };
}
/**
 * Gets personality markers based on brand voice
 */ function getPersonalityMarkers(brandVoice) {
    const voiceMap = {
        'friendly': [
            'Use warm, welcoming language',
            'Include friendly greetings and sign-offs',
            'Show genuine interest in followers',
            'Use inclusive language that brings people together'
        ],
        'professional': [
            'Maintain expertise while being approachable',
            'Use industry knowledge to build authority',
            'Balance formal tone with personal touches',
            'Show competence through specific examples'
        ],
        'casual': [
            'Use everyday language and slang appropriately',
            'Be relaxed and conversational',
            'Include humor and light-hearted moments',
            'Feel like talking to a friend'
        ],
        'innovative': [
            'Show forward-thinking perspectives',
            'Challenge conventional wisdom respectfully',
            'Share cutting-edge insights',
            'Express excitement about new possibilities'
        ]
    };
    // Extract key words from brand voice description
    const lowerVoice = brandVoice.toLowerCase();
    for (const [key, markers] of Object.entries(voiceMap)){
        if (lowerVoice.includes(key)) {
            return markers;
        }
    }
    return voiceMap['friendly']; // Default fallback
}
/**
 * Gets industry-specific authenticity elements
 */ function getIndustryAuthenticity(businessType) {
    const industryMap = {
        'restaurant': [
            'Share cooking failures and successes',
            'Talk about ingredient sourcing stories',
            'Mention customer reactions and feedback',
            'Describe the sensory experience of food'
        ],
        'fitness': [
            'Share personal workout struggles',
            'Admit to having off days',
            'Celebrate client progress genuinely',
            'Talk about the mental health benefits'
        ],
        'technology': [
            'Explain complex concepts simply',
            'Share debugging stories and solutions',
            'Admit when technology isn\'t perfect',
            'Focus on human impact of technology'
        ],
        'financial technology software': [
            'Share stories about financial inclusion impact',
            'Explain complex financial concepts simply',
            'Highlight real customer success stories',
            'Address common financial fears and concerns',
            'Show the human side of financial technology'
        ],
        'beauty': [
            'Share makeup fails and learning moments',
            'Talk about skin struggles and solutions',
            'Celebrate diverse beauty standards',
            'Share product testing experiences'
        ]
    };
    return industryMap[businessType.toLowerCase()] || [
        'Share real customer interactions',
        'Talk about daily business challenges',
        'Celebrate small business wins',
        'Show the human side of your industry'
    ];
}
/**
 * Gets location-specific conversational patterns
 */ function getLocationConversation(location) {
    const locationMap = {
        'nairobi': [
            'Use occasional Swahili phrases naturally',
            'Reference local landmarks and experiences',
            'Include community-focused language',
            'Show respect for local customs and values'
        ],
        'new york': [
            'Use direct, fast-paced communication',
            'Reference city experiences and culture',
            'Include diverse perspectives',
            'Show hustle and ambition'
        ],
        'london': [
            'Use British expressions naturally',
            'Include dry humor appropriately',
            'Reference local culture and experiences',
            'Maintain polite but direct communication'
        ]
    };
    const locationKey = location.toLowerCase().split(',')[0].trim();
    return locationMap[locationKey] || [
        'Use local expressions and references',
        'Include regional cultural touchpoints',
        'Show understanding of local context',
        'Connect with community values'
    ];
}
function generateContentOptimization(platform, businessType, timeOfDay = 'morning') {
    const platformStrategies = {
        'instagram': {
            posting_strategy: [
                'Use high-quality visuals as primary hook',
                'Write captions that encourage saves and shares',
                'Include clear call-to-actions in stories',
                'Use relevant hashtags strategically'
            ],
            engagement_timing: [
                'Post when your audience is most active',
                'Respond to comments within first hour',
                'Use stories for real-time engagement',
                'Go live during peak audience times'
            ]
        },
        'linkedin': {
            posting_strategy: [
                'Lead with valuable insights or questions',
                'Use professional but personal tone',
                'Include industry-relevant hashtags',
                'Share thought leadership content'
            ],
            engagement_timing: [
                'Post during business hours for B2B',
                'Engage with comments professionally',
                'Share in relevant LinkedIn groups',
                'Connect with commenters personally'
            ]
        },
        'twitter': {
            posting_strategy: [
                'Use trending hashtags when relevant',
                'Create tweetable quotes and insights',
                'Engage in real-time conversations',
                'Share quick tips and observations'
            ],
            engagement_timing: [
                'Tweet during peak conversation times',
                'Respond quickly to mentions',
                'Join trending conversations',
                'Retweet with thoughtful comments'
            ]
        },
        'facebook': {
            posting_strategy: [
                'Create community-focused content',
                'Use longer-form storytelling',
                'Encourage group discussions',
                'Share local community content'
            ],
            engagement_timing: [
                'Post when your community is online',
                'Respond to all comments personally',
                'Share in relevant Facebook groups',
                'Use Facebook events for promotion'
            ]
        }
    };
    const strategy = platformStrategies[platform.toLowerCase()] || platformStrategies['instagram'];
    return {
        ...strategy,
        content_mix: [
            '60% educational/valuable content',
            '20% behind-the-scenes/personal',
            '15% promotional/business',
            '5% trending/entertainment'
        ],
        performance_indicators: [
            'Comments and meaningful engagement',
            'Saves and shares over likes',
            'Profile visits and follows',
            'Website clicks and conversions',
            'Direct messages and inquiries'
        ]
    };
}
}}),
"[project]/src/ai/utils/design-trends.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Trends Integration System
 * 
 * Keeps design generation current with latest visual trends and best practices
 */ __turbopack_context__.s({
    "DesignTrendsSchema": (()=>DesignTrendsSchema),
    "generateTrendInstructions": (()=>generateTrendInstructions),
    "getCachedDesignTrends": (()=>getCachedDesignTrends),
    "getCurrentDesignTrends": (()=>getCurrentDesignTrends)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignTrendsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    currentTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Name of the design trend'),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Description of the trend'),
        applicability: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'low'
        ]).describe('How applicable this trend is to the business type'),
        implementation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How to implement this trend in the design'),
        examples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Visual examples or descriptions of the trend')
    })).describe('Current relevant design trends'),
    colorTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        palette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending color palette in hex format'),
        mood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall mood of trending colors'),
        application: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How to apply these colors effectively')
    }),
    typographyTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        styles: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending typography styles'),
        pairings: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Popular font pairings'),
        treatments: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Special text treatments and effects')
    }),
    layoutTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        compositions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending layout compositions'),
        spacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Current spacing and whitespace trends'),
        hierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Visual hierarchy trends')
    }),
    platformSpecific: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Instagram-specific design trends'),
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Facebook-specific design trends'),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Twitter/X-specific design trends'),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('LinkedIn-specific design trends')
    })
});
// Design trends analysis prompt
const designTrendsPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeDesignTrends',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            industry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignTrendsSchema
    },
    prompt: `You are a leading design trend analyst with deep knowledge of current visual design trends, social media best practices, and industry-specific design patterns.

Analyze and provide current design trends relevant to:
- Business Type: {{businessType}}
- Platform: {{platform}}
- Target Audience: {{targetAudience}}
- Industry: {{industry}}

Focus on trends that are:
1. Currently popular and effective (2024-2025)
2. Relevant to the specific business type and platform
3. Proven to drive engagement and conversions
4. Accessible and implementable in AI-generated designs

Provide specific, actionable trend insights that can be directly applied to design generation.`
});
async function getCurrentDesignTrends(businessType, platform, targetAudience, industry) {
    try {
        // For now, return fallback trends to avoid API issues
        // This provides current, relevant trends while the system is being tested
        return getFallbackTrends(businessType, platform);
    } catch (error) {
        console.error('Design trends analysis failed:', error);
        // Return fallback trends
        return getFallbackTrends(businessType, platform);
    }
}
function generateTrendInstructions(trends, platform) {
    const platformTrends = trends.platformSpecific[platform] || [];
    const highApplicabilityTrends = trends.currentTrends.filter((t)=>t.applicability === 'high');
    return `
**CURRENT DESIGN TRENDS INTEGRATION:**

**High-Priority Trends to Incorporate:**
${highApplicabilityTrends.map((trend)=>`
- **${trend.name}**: ${trend.description}
  Implementation: ${trend.implementation}`).join('\n')}

**Color Trends:**
- Trending Palette: ${trends.colorTrends.palette.join(', ')}
- Mood: ${trends.colorTrends.mood}
- Application: ${trends.colorTrends.application}

**Typography Trends:**
- Styles: ${trends.typographyTrends.styles.join(', ')}
- Popular Pairings: ${trends.typographyTrends.pairings.join(', ')}
- Special Treatments: ${trends.typographyTrends.treatments.join(', ')}

**Layout Trends:**
- Compositions: ${trends.layoutTrends.compositions.join(', ')}
- Spacing: ${trends.layoutTrends.spacing}
- Hierarchy: ${trends.layoutTrends.hierarchy}

**Platform-Specific Trends (${platform}):**
${platformTrends.map((trend)=>`- ${trend}`).join('\n')}

**TREND APPLICATION GUIDELINES:**
- Incorporate 2-3 relevant trends maximum to avoid overwhelming the design
- Ensure trends align with brand personality and business goals
- Prioritize trends that enhance readability and user experience
- Balance trendy elements with timeless design principles
`;
}
/**
 * Fallback trends when API fails
 */ function getFallbackTrends(businessType, platform) {
    return {
        currentTrends: [
            {
                name: "Bold Typography",
                description: "Large, impactful typography that commands attention",
                applicability: "high",
                implementation: "Use oversized headlines with strong contrast",
                examples: [
                    "Large sans-serif headers",
                    "Bold statement text",
                    "Typography as hero element"
                ]
            },
            {
                name: "Minimalist Design",
                description: "Clean, uncluttered designs with plenty of white space",
                applicability: "high",
                implementation: "Focus on essential elements, generous spacing, simple color palette",
                examples: [
                    "Clean layouts",
                    "Minimal color schemes",
                    "Focused messaging"
                ]
            },
            {
                name: "Authentic Photography",
                description: "Real, unposed photography over stock imagery",
                applicability: "medium",
                implementation: "Use candid, lifestyle photography that feels genuine",
                examples: [
                    "Behind-the-scenes shots",
                    "Real customer photos",
                    "Lifestyle imagery"
                ]
            }
        ],
        colorTrends: {
            palette: [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7"
            ],
            mood: "Vibrant yet calming, optimistic and approachable",
            application: "Use as accent colors against neutral backgrounds for maximum impact"
        },
        typographyTrends: {
            styles: [
                "Bold sans-serif",
                "Modern serif",
                "Custom lettering"
            ],
            pairings: [
                "Bold header + clean body",
                "Serif headline + sans-serif body"
            ],
            treatments: [
                "Gradient text",
                "Outlined text",
                "Text with shadows"
            ]
        },
        layoutTrends: {
            compositions: [
                "Asymmetrical balance",
                "Grid-based layouts",
                "Centered focal points"
            ],
            spacing: "Generous white space with intentional breathing room",
            hierarchy: "Clear size differentiation with strong contrast"
        },
        platformSpecific: {
            instagram: [
                "Square and vertical formats",
                "Story-friendly designs",
                "Carousel-optimized layouts"
            ],
            facebook: [
                "Horizontal emphasis",
                "Video-first approach",
                "Community-focused messaging"
            ],
            twitter: [
                "High contrast for timeline",
                "Text-heavy designs",
                "Trending hashtag integration"
            ],
            linkedin: [
                "Professional aesthetics",
                "Data visualization",
                "Thought leadership focus"
            ]
        }
    };
}
/**
 * Caches trends to avoid excessive API calls
 * Reduced cache duration and added randomization to prevent repetitive designs
 */ const trendsCache = new Map();
const CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours (reduced from 24 hours)
const MAX_USAGE_COUNT = 5; // Force refresh after 5 uses to add variety
async function getCachedDesignTrends(businessType, platform, targetAudience, industry) {
    // Add randomization to cache key to create more variety
    const hourOfDay = new Date().getHours();
    const randomSeed = Math.floor(hourOfDay / 2); // Changes every 2 hours
    const cacheKey = `${businessType}-${platform}-${targetAudience}-${industry}-${randomSeed}`;
    const cached = trendsCache.get(cacheKey);
    // Check if cache is valid and not overused
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION && cached.usageCount < MAX_USAGE_COUNT) {
        cached.usageCount++;
        return cached.trends;
    }
    const trends = await getCurrentDesignTrends(businessType, platform, targetAudience, industry);
    trendsCache.set(cacheKey, {
        trends,
        timestamp: Date.now(),
        usageCount: 1
    });
    return trends;
}
}}),
"[project]/src/ai/utils/design-analytics.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Performance Analytics System
 * 
 * Tracks design performance, learns from successful patterns, and optimizes future generations
 */ __turbopack_context__.s({
    "DesignPerformanceSchema": (()=>DesignPerformanceSchema),
    "exportAnalyticsData": (()=>exportAnalyticsData),
    "generatePerformanceOptimizedInstructions": (()=>generatePerformanceOptimizedInstructions),
    "getPerformanceInsights": (()=>getPerformanceInsights),
    "getTopPerformingDesigns": (()=>getTopPerformingDesigns),
    "recordDesignGeneration": (()=>recordDesignGeneration),
    "updateDesignPerformance": (()=>updateDesignPerformance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const DesignPerformanceSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    designId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    generatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].date(),
    metrics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        qualityScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        engagementPrediction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        brandAlignmentScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        technicalQuality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        trendRelevance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10)
    }),
    designElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        trends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        businessDNA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    }),
    performance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        actualEngagement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        clickThroughRate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        conversionRate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        brandRecall: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        userFeedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(5).optional()
    }).optional(),
    improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional()
});
// In-memory storage for design analytics (in production, use a database)
const designAnalytics = new Map();
const performancePatterns = new Map();
function recordDesignGeneration(designId, businessType, platform, visualStyle, qualityScore, designElements, predictions) {
    const record = {
        designId,
        businessType,
        platform,
        visualStyle,
        generatedAt: new Date(),
        metrics: {
            qualityScore,
            engagementPrediction: predictions.engagement,
            brandAlignmentScore: predictions.brandAlignment,
            technicalQuality: predictions.technicalQuality,
            trendRelevance: predictions.trendRelevance
        },
        designElements,
        tags: [
            businessType,
            platform,
            visualStyle
        ]
    };
    designAnalytics.set(designId, record);
    updatePerformancePatterns(record);
}
function updateDesignPerformance(designId, actualMetrics) {
    const record = designAnalytics.get(designId);
    if (!record) return;
    record.performance = {
        ...record.performance,
        ...actualMetrics
    };
    designAnalytics.set(designId, record);
    updatePerformancePatterns(record);
}
/**
 * Analyzes performance patterns to improve future designs
 */ function updatePerformancePatterns(record) {
    const patternKey = `${record.businessType}-${record.platform}-${record.visualStyle}`;
    if (!performancePatterns.has(patternKey)) {
        performancePatterns.set(patternKey, {
            count: 0,
            avgQuality: 0,
            avgEngagement: 0,
            successfulElements: new Map(),
            commonIssues: new Map(),
            bestPractices: []
        });
    }
    const pattern = performancePatterns.get(patternKey);
    pattern.count += 1;
    // Update averages
    pattern.avgQuality = (pattern.avgQuality * (pattern.count - 1) + record.metrics.qualityScore) / pattern.count;
    pattern.avgEngagement = (pattern.avgEngagement * (pattern.count - 1) + record.metrics.engagementPrediction) / pattern.count;
    // Track successful elements
    if (record.metrics.qualityScore >= 8) {
        record.designElements.trends.forEach((trend)=>{
            const count = pattern.successfulElements.get(trend) || 0;
            pattern.successfulElements.set(trend, count + 1);
        });
    }
    // Track common issues
    if (record.improvements) {
        record.improvements.forEach((issue)=>{
            const count = pattern.commonIssues.get(issue) || 0;
            pattern.commonIssues.set(issue, count + 1);
        });
    }
    performancePatterns.set(patternKey, pattern);
}
function getPerformanceInsights(businessType, platform, visualStyle) {
    const patternKey = visualStyle ? `${businessType}-${platform}-${visualStyle}` : `${businessType}-${platform}`;
    const pattern = performancePatterns.get(patternKey);
    if (!pattern) {
        return {
            averageQuality: 0,
            averageEngagement: 0,
            topSuccessfulElements: [],
            commonIssues: [],
            recommendations: [
                'Insufficient data for insights'
            ],
            sampleSize: 0
        };
    }
    // Get top successful elements
    const topElements = Array.from(pattern.successfulElements.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([element])=>element);
    // Get common issues
    const topIssues = Array.from(pattern.commonIssues.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 3).map(([issue])=>issue);
    // Generate recommendations
    const recommendations = generateRecommendations(pattern, topElements, topIssues);
    return {
        averageQuality: Math.round(pattern.avgQuality * 10) / 10,
        averageEngagement: Math.round(pattern.avgEngagement * 10) / 10,
        topSuccessfulElements: topElements,
        commonIssues: topIssues,
        recommendations,
        sampleSize: pattern.count
    };
}
/**
 * Generates actionable recommendations based on performance data
 */ function generateRecommendations(pattern, successfulElements, commonIssues) {
    const recommendations = [];
    // Quality-based recommendations
    if (pattern.avgQuality < 7) {
        recommendations.push('Focus on improving overall design quality through better composition and typography');
    }
    // Engagement-based recommendations
    if (pattern.avgEngagement < 7) {
        recommendations.push('Incorporate more attention-grabbing elements and bold visual choices');
    }
    // Element-based recommendations
    if (successfulElements.length > 0) {
        recommendations.push(`Continue using successful elements: ${successfulElements.slice(0, 3).join(', ')}`);
    }
    // Issue-based recommendations
    if (commonIssues.length > 0) {
        recommendations.push(`Address common issues: ${commonIssues.slice(0, 2).join(', ')}`);
    }
    // Sample size recommendations
    if (pattern.count < 10) {
        recommendations.push('Generate more designs to improve insights accuracy');
    }
    return recommendations;
}
function getTopPerformingDesigns(businessType, platform, limit = 10) {
    let designs = Array.from(designAnalytics.values());
    // Filter by business type and platform if specified
    if (businessType) {
        designs = designs.filter((d)=>d.businessType === businessType);
    }
    if (platform) {
        designs = designs.filter((d)=>d.platform === platform);
    }
    // Sort by quality score and engagement prediction
    designs.sort((a, b)=>{
        const scoreA = (a.metrics.qualityScore + a.metrics.engagementPrediction) / 2;
        const scoreB = (b.metrics.qualityScore + b.metrics.engagementPrediction) / 2;
        return scoreB - scoreA;
    });
    return designs.slice(0, limit);
}
function generatePerformanceOptimizedInstructions(businessType, platform, visualStyle) {
    const insights = getPerformanceInsights(businessType, platform, visualStyle);
    if (insights.sampleSize === 0) {
        return ''; // No data available
    }
    let instructions = `\n**PERFORMANCE-OPTIMIZED DESIGN INSTRUCTIONS:**\n`;
    if (insights.topSuccessfulElements.length > 0) {
        instructions += `**Proven Successful Elements (${insights.sampleSize} designs analyzed):**\n`;
        insights.topSuccessfulElements.forEach((element)=>{
            instructions += `- Incorporate: ${element}\n`;
        });
    }
    if (insights.commonIssues.length > 0) {
        instructions += `\n**Avoid Common Issues:**\n`;
        insights.commonIssues.forEach((issue)=>{
            instructions += `- Prevent: ${issue}\n`;
        });
    }
    if (insights.recommendations.length > 0) {
        instructions += `\n**Performance Recommendations:**\n`;
        insights.recommendations.forEach((rec)=>{
            instructions += `- ${rec}\n`;
        });
    }
    instructions += `\n**Performance Benchmarks:**\n`;
    instructions += `- Target Quality Score: ${Math.max(insights.averageQuality + 0.5, 8)}/10\n`;
    instructions += `- Target Engagement: ${Math.max(insights.averageEngagement + 0.5, 8)}/10\n`;
    return instructions;
}
function exportAnalyticsData() {
    const designs = Array.from(designAnalytics.values());
    const patterns = Array.from(performancePatterns.entries()).map(([key, data])=>({
            key,
            data
        }));
    // Calculate summary statistics
    const totalDesigns = designs.length;
    const averageQuality = designs.reduce((sum, d)=>sum + d.metrics.qualityScore, 0) / totalDesigns;
    const businessTypeCounts = designs.reduce((acc, d)=>{
        acc[d.businessType] = (acc[d.businessType] || 0) + 1;
        return acc;
    }, {});
    const platformCounts = designs.reduce((acc, d)=>{
        acc[d.platform] = (acc[d.platform] || 0) + 1;
        return acc;
    }, {});
    const topBusinessTypes = Object.entries(businessTypeCounts).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([type])=>type);
    const topPlatforms = Object.entries(platformCounts).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([platform])=>platform);
    return {
        designs,
        patterns,
        summary: {
            totalDesigns,
            averageQuality: Math.round(averageQuality * 10) / 10,
            topBusinessTypes,
            topPlatforms
        }
    };
}
}}),
"[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4053f02f119e79782e8f26f17575a21e480f290976":"generatePostFromProfile"},"",""] */ __turbopack_context__.s({
    "generatePostFromProfile": (()=>generatePostFromProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview This file defines a Genkit flow for generating a daily social media post.
 *
 * It takes into account business type, location, brand voice, current weather, and local events to create engaging content.
 * @exports generatePostFromProfile - The main function to generate a post.
 * @exports GeneratePostFromProfileInput - The input type for the generation flow.
 * @exports GeneratePostFromProfileOutput - The output type for the generation flow.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/tools/local-data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/tools/enhanced-local-data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$ai$2d$prompt$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-ai-prompt.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$hashtag$2d$strategy$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/hashtag-strategy.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/trending-topics.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/intelligent-context-selector.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/human-content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-trends.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analytics.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const GeneratePostFromProfileInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The type of business (e.g., restaurant, salon).'),
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The location of the business (city, state).'),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The visual style of the brand (e.g., modern, vintage).'),
    writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The brand voice of the business.'),
    contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The content themes of the business.'),
    logoDataUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe("The business logo as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."),
    designExamples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe("Array of design example data URIs to use as style reference for generating similar designs."),
    dayOfWeek: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The day of the week for the post.'),
    currentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The current date for the post.'),
    variants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    })).describe('An array of platform and aspect ratio variants to generate.'),
    primaryColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The primary brand color in HSL format.'),
    accentColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The accent brand color in HSL format.'),
    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The background brand color in HSL format.'),
    // New detailed fields for richer content
    services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of key services or products.'),
    targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A description of the target audience.'),
    keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of key features or selling points.'),
    competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of competitive advantages.'),
    // Brand consistency preferences
    brandConsistency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        strictConsistency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional(),
        followBrandColors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional()
    }).optional().describe('Brand consistency preferences for content generation.'),
    // Enhanced brand context
    websiteUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The business website URL for additional context.'),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Detailed business description for better content context.'),
    contactInfo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
    }).optional().describe('Contact information for business context.'),
    socialMedia: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
    }).optional().describe('Social media handles for cross-platform consistency.'),
    // Language preferences
    useLocalLanguage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional().describe('Whether to use local language in content generation (default: false).')
});
const GeneratePostFromProfileOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The primary generated social media post content (the caption).'),
    catchyWords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),
    subheadline: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),
    callToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),
    hashtags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Strategically selected hashtags for the post.'),
    contentVariants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Alternative caption variant.'),
        approach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),
        rationale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Why this variant might perform well.')
    })).optional().describe('Alternative caption variants for A/B testing.'),
    hashtagAnalysis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        trending: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending hashtags for reach.'),
        niche: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Industry-specific hashtags.'),
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Location-based hashtags.'),
        community: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Community engagement hashtags.')
    }).optional().describe('Categorized hashtag strategy.'),
    marketIntelligence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        trending_topics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            topic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            relevanceScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            engagement_potential: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).describe('Current trending topics relevant to the business.'),
        competitor_insights: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            competitor_name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            content_gap: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            differentiation_opportunity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).describe('Competitor analysis and differentiation opportunities.'),
        cultural_context: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            cultural_nuances: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
            local_customs: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string())
        }).describe('Cultural and location-specific context.'),
        viral_patterns: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Content patterns that drive viral engagement.'),
        engagement_triggers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Psychological triggers for maximum engagement.')
    }).optional().describe('Advanced market intelligence and optimization data.'),
    localContext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        weather: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            business_impact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            content_opportunities: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string())
        }).optional().describe('Current weather context and business opportunities.'),
        events: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            relevance_score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            start_date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).optional().describe('Relevant local events for content integration.')
    }).optional().describe('Local context including weather and events.'),
    variants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        imageUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    }))
});
async function generatePostFromProfile(input) {
    return generatePostFromProfileFlow(input);
}
/**
 * Combines catchy words, subheadline, and call to action into a single text for image overlay
 */ function combineTextComponents(catchyWords, subheadline, callToAction) {
    const components = [
        catchyWords
    ];
    if (subheadline && subheadline.trim()) {
        components.push(subheadline.trim());
    }
    if (callToAction && callToAction.trim()) {
        components.push(callToAction.trim());
    }
    return components.join('\n');
}
// Define the enhanced text generation prompt
const enhancedTextGenPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'enhancedGeneratePostTextPrompt',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            dayOfWeek: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            currentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            contentVariation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            contextInstructions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            selectedWeather: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            selectedEvents: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            selectedTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            selectedCultural: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            useLocalLanguage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional()
        })
    },
    output: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The primary generated social media post content (the caption).'),
            catchyWords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),
            subheadline: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),
            callToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),
            hashtags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Strategically selected hashtags for the post.'),
            contentVariants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
                content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Alternative caption variant.'),
                approach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),
                rationale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Why this variant might perform well.')
            })).describe('2-3 alternative caption variants for A/B testing.')
        })
    },
    tools: [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getWeatherTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEventsTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnhancedWeatherTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnhancedEventsTool"]
    ],
    prompt: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$ai$2d$prompt$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ADVANCED_AI_PROMPT"]
});
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                if (e.message && e.message.includes('429')) {
                    throw new Error("You've exceeded your request limit for the AI model. Please check your plan or try again later.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
const getMimeTypeFromDataURI = (dataURI)=>{
    const match = dataURI.match(/^data:(.*?);/);
    return match ? match[1] : 'application/octet-stream'; // Default if no match
};
// Helper function to generate an image for a single variant with advanced design principles
async function generateImageForVariant(variant, input, textOutput) {
    // Determine consistency level based on preferences first
    const isStrictConsistency = input.brandConsistency?.strictConsistency ?? false;
    const followBrandColors = input.brandConsistency?.followBrandColors ?? true;
    // Enhanced color instructions with psychology and usage guidelines
    const colorInstructions = followBrandColors ? `
  **BRAND COLOR PALETTE (MANDATORY):**
  - Primary Color: ${input.primaryColor} - Use for main elements, headers, and key focal points
  - Accent Color: ${input.accentColor} - Use for highlights, buttons, and secondary elements
  - Background Color: ${input.backgroundColor} - Use for backgrounds and neutral areas

  **COLOR USAGE REQUIREMENTS:**
  - Primary color should dominate the design (40-60% of color usage)
  - Accent color for emphasis and call-to-action elements (20-30% of color usage)
  - Background color for balance and readability (10-40% of color usage)
  - Ensure high contrast ratios for text readability (minimum 4.5:1)
  - Use color gradients and variations within the brand palette
  - Avoid colors outside the brand palette unless absolutely necessary for contrast
  ` : `
  **COLOR GUIDANCE:**
  - Brand colors available: Primary ${input.primaryColor}, Accent ${input.accentColor}, Background ${input.backgroundColor}
  - Feel free to use complementary colors that work well with the brand palette
  - Maintain visual harmony and professional appearance
  `;
    // Get platform-specific guidelines
    const platformGuidelines = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLATFORM_SPECIFIC_GUIDELINES"][variant.platform] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLATFORM_SPECIFIC_GUIDELINES"].instagram;
    // Get business-specific design DNA
    const businessDNA = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"][input.businessType] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"].default;
    // Get current design trends
    let trendInstructions = '';
    try {
        const trends = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedDesignTrends"])(input.businessType, variant.platform, input.targetAudience, input.businessType);
        trendInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateTrendInstructions"])(trends, variant.platform);
    } catch (error) {
        console.warn('Failed to get design trends, continuing without:', error);
    }
    // Get performance-optimized instructions
    const performanceInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePerformanceOptimizedInstructions"])(input.businessType, variant.platform, input.visualStyle);
    // Enhanced brand context for better design generation
    const businessContext = `
  **BUSINESS PROFILE:**
  - Name: ${input.businessName || 'Business'}
  - Type: ${input.businessType}
  - Location: ${input.location}
  - Description: ${input.description || 'Professional business'}
  ${input.services ? `- Services: ${input.services.split('\n').slice(0, 3).join(', ')}` : ''}
  ${input.targetAudience ? `- Target Audience: ${input.targetAudience}` : ''}
  ${input.websiteUrl ? `- Website: ${input.websiteUrl}` : ''}
  `;
    // Generate visual variation approach for diversity
    const visualVariations = [
        'minimalist_clean',
        'bold_dynamic',
        'elegant_sophisticated',
        'playful_creative',
        'modern_geometric',
        'organic_natural',
        'industrial_urban',
        'artistic_abstract',
        'photographic_realistic',
        'illustrated_stylized',
        'gradient_colorful',
        'monochrome_accent'
    ];
    const selectedVisualVariation = visualVariations[Math.floor(Math.random() * visualVariations.length)];
    console.log(`🎨 Selected visual variation: ${selectedVisualVariation}`);
    let imagePrompt = `Create a stunning, professional social media design for ${input.businessName || input.businessType} business.

  **VISUAL APPROACH:** ${selectedVisualVariation} (MANDATORY: Use this specific visual style approach)

    ${businessContext}

    **DESIGN SPECIFICATIONS:**
    - Platform: ${variant.platform} (${variant.aspectRatio} aspect ratio)
    - Visual Style: ${input.visualStyle}, modern, clean, professional
    - Text Content: "${combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction)}"

    ${colorInstructions}

    **DESIGN REQUIREMENTS:**
    - High-quality, professional design that reflects the business personality
    - Clear, readable text with excellent contrast (minimum 4.5:1 ratio)
    - ${input.visualStyle} aesthetic that appeals to ${input.targetAudience || 'target audience'}
    - Perfect representation of ${input.businessType} business values
    - Brand colors prominently and strategically featured
    - Clean, modern layout optimized for ${variant.platform}
    - Professional social media appearance that drives engagement
    - Text must be perfectly readable, properly sized, and not cut off
    - ${variant.aspectRatio} aspect ratio optimized for ${variant.platform}
    - Design should reflect the business's location (${input.location}) and cultural context

    **BUSINESS DNA INTEGRATION:**
    ${businessDNA}

    **PLATFORM OPTIMIZATION:**
    ${platformGuidelines.designGuidelines || `Optimize for ${variant.platform} best practices`}

    Create a beautiful, professional design that authentically represents the business and drives engagement.`;
    // Intelligent design examples processing
    let designDNA = '';
    let selectedExamples = [];
    if (input.designExamples && input.designExamples.length > 0) {
        try {
            // Analyze design examples for intelligent processing
            const analyses = [];
            for (const example of input.designExamples.slice(0, 5)){
                try {
                    const analysis = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeDesignExample"])(example, input.businessType, variant.platform, `${input.visualStyle} design for ${textOutput.imageText}`);
                    analyses.push(analysis);
                } catch (error) {
                    console.warn('Design analysis failed for example, skipping:', error);
                }
            }
            if (analyses.length > 0) {
                // Extract design DNA from analyzed examples
                designDNA = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractDesignDNA"])(analyses);
                // Select optimal examples based on analysis
                selectedExamples = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectOptimalDesignExamples"])(input.designExamples, analyses, textOutput.imageText, variant.platform, isStrictConsistency ? 3 : 1);
            } else {
                // Fallback to original logic if analysis fails
                selectedExamples = isStrictConsistency ? input.designExamples : [
                    input.designExamples[Math.floor(Math.random() * input.designExamples.length)]
                ];
            }
        } catch (error) {
            console.warn('Design analysis system failed, using fallback:', error);
            selectedExamples = isStrictConsistency ? input.designExamples : [
                input.designExamples[Math.floor(Math.random() * input.designExamples.length)]
            ];
        }
        // Add design consistency instructions based on analysis
        if (isStrictConsistency) {
            imagePrompt += `\n    **STRICT STYLE REFERENCE:**
      Use the provided design examples as strict style reference. Closely match the visual aesthetic, color scheme, typography, layout patterns, and overall design approach of the reference designs. Create content that looks very similar to the uploaded examples while incorporating the new text and subject matter.

      ${designDNA}`;
        } else {
            imagePrompt += `\n    **STYLE INSPIRATION:**
      Use the provided design examples as loose inspiration for the overall aesthetic and mood, but feel free to create more varied and creative designs while maintaining the brand essence.

      ${designDNA}

      **CREATIVE VARIATION:** Feel free to experiment with different layouts, compositions, and design elements to create fresh, engaging content that avoids repetitive appearance while maintaining brand recognition.

      **STRICT UNIQUENESS REQUIREMENT:** This design MUST be completely different from any previous generation. MANDATORY variations:
      - Layout compositions: Choose from grid, asymmetrical, centered, diagonal, circular, or organic layouts
      - Color emphasis: Vary primary/accent color dominance (primary-heavy, accent-heavy, or balanced)
      - Typography placement: Top, bottom, center, side, overlay, or integrated into imagery
      - Visual elements: Abstract shapes, geometric patterns, organic forms, or photographic elements
      - Background treatments: Solid, gradient, textured, patterned, or photographic
      - Design style: Minimalist, bold, elegant, playful, modern, or artistic
      - Content arrangement: Single focus, multiple elements, layered, or split-screen

      **DIVERSITY ENFORCEMENT:**
      - Never repeat the same layout pattern twice in a row
      - Alternate between different color emphasis approaches
      - Vary typography size, weight, and positioning significantly
      - Use different visual metaphors and imagery styles
      - Change background complexity and treatment style

      **GENERATION ID:** ${Date.now()}_${Math.random().toString(36).substr(2, 9)} - Use this unique identifier to ensure no two designs are identical.`;
        }
    }
    // Build prompt parts array
    const promptParts = [
        {
            text: imagePrompt
        }
    ];
    // Enhanced logo integration with analysis
    if (input.logoDataUrl) {
        // Add logo analysis instructions to the prompt
        const logoInstructions = `

    **CRITICAL LOGO USAGE REQUIREMENTS:**
    🚨 MANDATORY: You MUST use the uploaded brand logo image provided below. DO NOT create, generate, or design a new logo.

    **LOGO INTEGRATION REQUIREMENTS:**
    - Use ONLY the provided logo image - never create or generate a new logo
    - The uploaded logo is the official brand logo and must be used exactly as provided
    - Incorporate the provided logo naturally and prominently into the design
    - Ensure logo is clearly visible and properly sized for the platform (minimum 10% of design area)
    - Maintain logo's original proportions and readability - do not distort or modify the logo
    - Position logo strategically: ${platformGuidelines.logoPlacement || 'Place logo prominently in corner or integrated into layout'}
    - Ensure sufficient contrast between logo and background (minimum 4.5:1 ratio)
    - For ${variant.platform}: Logo should be clearly visible and recognizable

    **BRAND CONSISTENCY WITH UPLOADED LOGO:**
    - Extract and use colors from the provided logo for the overall color scheme
    - Match the design style to complement the logo's aesthetic and personality
    - Ensure visual harmony between the uploaded logo and all design elements
    - The logo is the primary brand identifier - treat it as the most important visual element

    **LOGO PLACEMENT PRIORITY:**
    - Logo visibility is more important than other design elements
    - If space is limited, reduce other elements to ensure logo prominence
    - Logo should be one of the first things viewers notice in the design
    `;
        // Update the main prompt with logo instructions
        promptParts[0].text += logoInstructions;
        // Add logo as media with high priority
        promptParts.push({
            media: {
                url: input.logoDataUrl,
                contentType: getMimeTypeFromDataURI(input.logoDataUrl)
            }
        });
        console.log(`🎨 Logo integrated: ${input.logoDataUrl.substring(0, 50)}...`);
    } else {
        console.log('⚠️ No logo provided - design will be generated without brand logo');
    }
    // Add selected design examples
    selectedExamples.forEach((example)=>{
        promptParts.push({
            media: {
                url: example,
                contentType: getMimeTypeFromDataURI(example)
            }
        });
    });
    // Generate initial design
    let finalImageUrl = '';
    let attempts = 0;
    const maxAttempts = 2; // Limit attempts to avoid excessive API calls
    while(attempts < maxAttempts){
        attempts++;
        try {
            const { media } = await generateWithRetry({
                model: 'googleai/gemini-2.0-flash-preview-image-generation',
                prompt: promptParts,
                config: {
                    responseModalities: [
                        'TEXT',
                        'IMAGE'
                    ]
                }
            });
            let imageUrl = media?.url ?? '';
            if (!imageUrl) {
                throw new Error('No image generated');
            }
            // Apply aspect ratio correction for non-square platforms
            const { cropImageFromUrl, needsAspectRatioCorrection } = await __turbopack_context__.r("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            if (needsAspectRatioCorrection(variant.platform)) {
                console.log(`🖼️ Applying aspect ratio correction for ${variant.platform}...`);
                try {
                    imageUrl = await cropImageFromUrl(imageUrl, variant.platform);
                    console.log(`✅ Image cropped successfully for ${variant.platform}`);
                } catch (cropError) {
                    console.warn('⚠️ Image cropping failed, using original:', cropError);
                // Continue with original image if cropping fails
                }
            }
            // Quality validation for first attempt
            if (attempts === 1) {
                try {
                    const quality = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["assessDesignQuality"])(imageUrl, input.businessType, variant.platform, input.visualStyle, followBrandColors && input.primaryColor ? colorInstructions : undefined, `Create engaging design for: ${textOutput.catchyWords}`);
                    // If quality is acceptable, use this design
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["meetsQualityStandards"])(quality, 7)) {
                        finalImageUrl = imageUrl;
                        break;
                    }
                    // If quality is poor and we have attempts left, try to improve
                    if (attempts < maxAttempts) {
                        console.log(`Design quality score: ${quality.overall.score}/10. Attempting improvement...`);
                        // Add improvement instructions to prompt
                        const improvementInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateImprovementPrompt"])(quality);
                        const improvedPrompt = `${imagePrompt}\n\n${improvementInstructions}`;
                        promptParts[0] = {
                            text: improvedPrompt
                        };
                        continue;
                    } else {
                        // Use the design even if quality is subpar (better than nothing)
                        finalImageUrl = imageUrl;
                        break;
                    }
                } catch (qualityError) {
                    console.warn('Quality assessment failed, using generated design:', qualityError);
                    finalImageUrl = imageUrl;
                    break;
                }
            } else {
                // For subsequent attempts, use the result
                finalImageUrl = imageUrl;
                break;
            }
        } catch (error) {
            console.error(`Design generation attempt ${attempts} failed:`, error);
            if (attempts === maxAttempts) {
                throw error;
            }
        }
    }
    // Record design generation for analytics
    if (finalImageUrl) {
        try {
            const designId = `design_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["recordDesignGeneration"])(designId, input.businessType, variant.platform, input.visualStyle, 9.2, {
                colorPalette: input.primaryColor ? [
                    input.primaryColor,
                    input.accentColor,
                    input.backgroundColor
                ].filter(Boolean) : [],
                typography: 'Modern social media optimized',
                composition: variant.aspectRatio,
                trends: selectedExamples.length > 0 ? [
                    'design-examples-based'
                ] : [
                    'ai-generated'
                ],
                businessDNA: businessDNA.substring(0, 100) // Truncate for storage
            }, {
                engagement: 8,
                brandAlignment: followBrandColors ? 9 : 7,
                technicalQuality: 8,
                trendRelevance: trendInstructions ? 8 : 6
            });
        } catch (analyticsError) {
            console.warn('Failed to record design analytics:', analyticsError);
        }
    }
    return {
        platform: variant.platform,
        imageUrl: finalImageUrl
    };
}
const generatePostFromProfileFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generatePostFromProfileFlow',
    inputSchema: GeneratePostFromProfileInputSchema,
    outputSchema: GeneratePostFromProfileOutputSchema
}, async (input)=>{
    // Determine the primary platform for optimization
    const primaryPlatform = input.variants[0]?.platform || 'instagram';
    // Generate unique content variation approach to ensure diversity
    const contentVariations = [
        'trending_hook',
        'story_driven',
        'educational_tip',
        'behind_scenes',
        'question_engagement',
        'statistic_driven',
        'personal_insight',
        'industry_contrarian',
        'local_cultural',
        'seasonal_relevance',
        'problem_solution',
        'inspiration_motivation'
    ];
    const selectedVariation = contentVariations[Math.floor(Math.random() * contentVariations.length)];
    console.log(`🎯 Selected content variation approach: ${selectedVariation}`);
    // Step 1: Intelligent Context Analysis - Determine what information is relevant
    const contextRelevance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectRelevantContext"])(input.businessType, input.location, primaryPlatform, input.contentThemes, input.dayOfWeek);
    console.log(`🧠 Context Analysis for ${input.businessType} in ${input.location}:`);
    console.log(`   Weather: ${contextRelevance.weather.priority} - ${contextRelevance.weather.relevanceReason}`);
    console.log(`   Events: ${contextRelevance.events.priority} - ${contextRelevance.events.relevanceReason}`);
    console.log(`   Trends: ${contextRelevance.trends.priority} - ${contextRelevance.trends.relevanceReason}`);
    console.log(`   Culture: ${contextRelevance.cultural.priority} - ${contextRelevance.cultural.relevanceReason}`);
    // Step 2: Fetch Real-Time Trending Topics (always useful)
    const realTimeTrends = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRealTimeTrendingTopics"])(input.businessType, input.location, primaryPlatform);
    // Step 3: Fetch Local Context (Weather + Events) - but filter intelligently
    const rawLocalContext = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchLocalContext"])(input.location, input.businessType);
    // Step 4: Generate Market Intelligence for Advanced Content
    const marketIntelligence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateMarketIntelligence"])(input.businessType, input.location, primaryPlatform, input.services);
    // Step 5: Intelligently Filter Context Data
    const filteredContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filterContextData"])(contextRelevance, {
        weather: rawLocalContext.weather,
        events: rawLocalContext.events,
        trends: realTimeTrends,
        cultural: marketIntelligence.cultural_context
    });
    // Enhance market intelligence with filtered real-time trends
    marketIntelligence.trending_topics = [
        ...(filteredContext.selectedTrends || []).slice(0, 3),
        ...marketIntelligence.trending_topics.slice(0, 2)
    ];
    // Step 6: Generate Human-like Content Techniques
    const humanizationTechniques = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateHumanizationTechniques"])(input.businessType, input.writingTone, input.location);
    // Step 7: Generate Traffic-Driving Elements
    const trafficElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateTrafficDrivingElements"])(input.businessType, primaryPlatform, input.targetAudience);
    // Step 8: Generate Enhanced Text Content with Intelligent Context
    const { output: textOutput } = await enhancedTextGenPrompt({
        businessType: input.businessType,
        location: input.location,
        writingTone: input.writingTone,
        contentThemes: input.contentThemes,
        dayOfWeek: input.dayOfWeek,
        currentDate: input.currentDate,
        platform: primaryPlatform,
        services: input.services,
        targetAudience: input.targetAudience,
        keyFeatures: input.keyFeatures,
        competitiveAdvantages: input.competitiveAdvantages,
        // Add intelligent context instructions
        contextInstructions: filteredContext.contextInstructions,
        selectedWeather: filteredContext.selectedWeather,
        selectedEvents: filteredContext.selectedEvents,
        selectedTrends: filteredContext.selectedTrends,
        selectedCultural: filteredContext.selectedCultural,
        // Add content variation for diversity
        contentVariation: selectedVariation,
        // Language preferences
        useLocalLanguage: input.useLocalLanguage || false
    });
    if (!textOutput) {
        throw new Error('Failed to generate advanced AI post content.');
    }
    // Step 9: Generate Strategic Hashtag Analysis (exactly 10 hashtags)
    const hashtagStrategy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$hashtag$2d$strategy$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateHashtagStrategy"])(input.businessType, input.location, primaryPlatform, input.services, input.targetAudience, textOutput.catchyWords || textOutput.content, input.visualStyle || 'modern' // Design style
    );
    // Step 10: Generate Image for each variant in parallel
    const imagePromises = input.variants.map((variant)=>generateImageForVariant(variant, input, textOutput));
    const variants = await Promise.all(imagePromises);
    // Step 11: Combine text components for image overlay
    const combinedImageText = combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction);
    // Step 12: Convert hashtag strategy to exactly 10 hashtags
    const finalHashtags = [
        ...hashtagStrategy.trending,
        ...hashtagStrategy.niche,
        ...hashtagStrategy.branded,
        ...hashtagStrategy.location,
        ...hashtagStrategy.community
    ].slice(0, 10); // Ensure exactly 10 hashtags
    // Step 13: Combine results with intelligently selected context
    return {
        content: textOutput.content,
        catchyWords: textOutput.catchyWords,
        subheadline: textOutput.subheadline,
        callToAction: textOutput.callToAction,
        hashtags: finalHashtags.join(' '),
        contentVariants: textOutput.contentVariants,
        hashtagAnalysis: {
            trending: hashtagStrategy.trending,
            niche: hashtagStrategy.niche,
            location: hashtagStrategy.location,
            community: hashtagStrategy.community
        },
        // Advanced AI features metadata (for future UI display)
        marketIntelligence: {
            trending_topics: marketIntelligence.trending_topics.slice(0, 3),
            competitor_insights: marketIntelligence.competitor_insights.slice(0, 2),
            cultural_context: marketIntelligence.cultural_context,
            viral_patterns: marketIntelligence.viral_content_patterns.slice(0, 3),
            engagement_triggers: marketIntelligence.engagement_triggers.slice(0, 3)
        },
        // Intelligently selected local context
        localContext: {
            weather: filteredContext.selectedWeather,
            events: filteredContext.selectedEvents,
            contextRelevance: {
                weather: contextRelevance.weather.priority,
                events: contextRelevance.events.priority,
                weatherReason: contextRelevance.weather.relevanceReason,
                eventsReason: contextRelevance.events.relevanceReason
            }
        },
        variants
    };
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generatePostFromProfile
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generatePostFromProfile, "4053f02f119e79782e8f26f17575a21e480f290976", null);
}}),
"[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.5 Content Generator
 * Enhanced content generation with advanced features
 */ __turbopack_context__.s({
    "Revo15ContentGenerator": (()=>Revo15ContentGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)");
;
class Revo15ContentGenerator {
    modelId = 'revo-1.5';
    /**
   * Generate enhanced content using Revo 1.5 specifications
   */ async generateContent(request) {
        const startTime = Date.now();
        try {
            console.log('✨ Revo 1.5: Starting enhanced content generation...');
            console.log('- Platform:', request.platform);
            console.log('- Business:', request.profile.businessName);
            console.log('- Artifacts:', request.artifactIds?.length || 0);
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid content generation request for Revo 1.5');
            }
            // Prepare enhanced generation parameters
            const generationParams = this.prepareEnhancedGenerationParams(request);
            // Generate content with enhanced features
            const postDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePostFromProfile"])(generationParams);
            // Create the enhanced generated post
            const generatedPost = {
                id: new Date().toISOString(),
                date: new Date().toISOString(),
                content: postDetails.content,
                hashtags: postDetails.hashtags,
                status: 'generated',
                variants: postDetails.variants,
                catchyWords: postDetails.catchyWords,
                subheadline: postDetails.subheadline,
                callToAction: postDetails.callToAction,
                // Enhanced features for Revo 1.5
                contentVariants: postDetails.contentVariants,
                hashtagAnalysis: postDetails.hashtagAnalysis,
                marketIntelligence: postDetails.marketIntelligence,
                localContext: postDetails.localContext,
                metadata: {
                    modelId: this.modelId,
                    modelVersion: '1.5.0',
                    generationType: 'enhanced',
                    processingTime: Date.now() - startTime,
                    qualityLevel: 'enhanced',
                    enhancedFeatures: this.getAppliedEnhancements(request),
                    artifactsUsed: request.artifactIds?.length || 0
                }
            };
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateEnhancedQualityScore(generatedPost);
            console.log(`✅ Revo 1.5: Enhanced content generated successfully in ${processingTime}ms`);
            console.log(`⭐ Quality Score: ${qualityScore}/10`);
            return {
                success: true,
                data: generatedPost,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 2,
                    enhancementsApplied: [
                        'enhanced-ai-engine',
                        'real-time-context',
                        'trending-topics',
                        'advanced-prompting',
                        'quality-optimization',
                        ...request.artifactIds?.length ? [
                            'artifact-integration'
                        ] : []
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            console.error('❌ Revo 1.5: Enhanced content generation failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Validate content generation request for Revo 1.5
   */ validateRequest(request) {
        // Check required fields
        if (!request.profile || !request.platform) {
            return false;
        }
        // Check if profile has minimum required information
        if (!request.profile.businessType || !request.profile.businessName) {
            return false;
        }
        // Revo 1.5 supports artifacts - validate if provided
        if (request.artifactIds && request.artifactIds.length > 5) {
            console.warn('⚠️ Revo 1.5: Too many artifacts (max 5), using first 5');
        }
        return true;
    }
    /**
   * Prepare enhanced generation parameters for Revo 1.5
   */ prepareEnhancedGenerationParams(request) {
        const { profile, platform, brandConsistency } = request;
        const today = new Date();
        // Enhanced parameter preparation with more sophisticated processing
        const keyFeaturesString = Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '';
        const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '';
        const servicesString = Array.isArray(profile.services) ? profile.services.map((service)=>typeof service === 'object' && service.name ? `${service.name}: ${service.description || ''}` : service).join('\n') : profile.services || '';
        return {
            businessType: profile.businessType,
            location: profile.location,
            writingTone: profile.writingTone,
            contentThemes: profile.contentThemes,
            visualStyle: profile.visualStyle,
            logoDataUrl: profile.logoDataUrl,
            designExamples: brandConsistency?.strictConsistency ? profile.designExamples || [] : [],
            primaryColor: profile.primaryColor,
            accentColor: profile.accentColor,
            backgroundColor: profile.backgroundColor,
            dayOfWeek: today.toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: today.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            variants: [
                {
                    platform: platform,
                    aspectRatio: this.getOptimalAspectRatio(platform)
                }
            ],
            services: servicesString,
            targetAudience: profile.targetAudience,
            keyFeatures: keyFeaturesString,
            competitiveAdvantages: competitiveAdvantagesString,
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            },
            // Revo 1.5 enhanced features
            modelConstraints: {
                maxComplexity: 'enhanced',
                enhancedFeatures: true,
                realTimeContext: true,
                trendingTopics: true,
                artifactSupport: true,
                advancedPrompting: true,
                qualityLevel: 'enhanced'
            },
            // Artifact integration
            artifactIds: request.artifactIds?.slice(0, 5) || [],
            customInstructions: request.customInstructions
        };
    }
    /**
   * Get optimal aspect ratio for platform (Revo 1.5 supports multiple)
   */ getOptimalAspectRatio(platform) {
        switch(platform){
            case 'Instagram':
                return '1:1'; // Square for feed, can also do 9:16 for stories
            case 'Facebook':
                return '16:9'; // Landscape for better engagement
            case 'Twitter':
                return '16:9'; // Landscape works well
            case 'LinkedIn':
                return '16:9'; // Professional landscape format
            default:
                return '1:1';
        }
    }
    /**
   * Calculate enhanced quality score for generated content
   */ calculateEnhancedQualityScore(post) {
        let score = 6; // Higher base score for Revo 1.5
        // Content quality checks
        if (post.content && post.content.length > 50) score += 0.5;
        if (post.content && post.content.length > 150) score += 0.5;
        // Enhanced content features
        if (post.subheadline && post.subheadline.trim().length > 0) score += 0.5;
        if (post.callToAction && post.callToAction.trim().length > 0) score += 0.5;
        // Hashtag quality and analysis
        if (post.hashtags && post.hashtags.length >= 8) score += 0.5;
        if (post.hashtagAnalysis) score += 0.5;
        // Advanced features
        if (post.contentVariants && post.contentVariants.length > 0) score += 0.5;
        if (post.marketIntelligence) score += 0.5;
        if (post.localContext) score += 0.5;
        // Image generation success
        if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {
            score += 0.5;
        }
        // Cap at 10
        return Math.min(score, 10);
    }
    /**
   * Get applied enhancements for this generation
   */ getAppliedEnhancements(request) {
        const enhancements = [
            'enhanced-ai-engine',
            'advanced-prompting'
        ];
        if (request.artifactIds && request.artifactIds.length > 0) {
            enhancements.push('artifact-integration');
        }
        if (request.profile.location) {
            enhancements.push('local-context', 'real-time-context');
        }
        enhancements.push('trending-topics', 'quality-optimization', 'brand-consistency-advanced');
        return enhancements;
    }
    /**
   * Health check for enhanced content generator
   */ async healthCheck() {
        try {
            // Check if we can access enhanced AI services
            const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
            return hasGeminiKey || hasOpenAIKey;
        } catch (error) {
            console.error('❌ Revo 1.5 Content Generator health check failed:', error);
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'content',
            capabilities: [
                'Enhanced content generation',
                'Real-time context integration',
                'Trending topics analysis',
                'Advanced brand consistency',
                'Artifact support (up to 5)',
                'Content variants generation',
                'Hashtag analysis',
                'Market intelligence',
                'Local context optimization'
            ],
            limitations: [
                'Higher credit cost (2x)',
                'Longer processing times',
                'Requires more system resources'
            ],
            averageProcessingTime: '20-30 seconds',
            qualityRange: '7-9/10',
            costPerGeneration: 2,
            enhancedFeatures: this.getEnhancedFeaturesList()
        };
    }
    getEnhancedFeaturesList() {
        return {
            realTimeContext: true,
            trendingTopics: true,
            artifactSupport: true,
            contentVariants: true,
            hashtagAnalysis: true,
            marketIntelligence: true,
            localOptimization: true,
            advancedPrompting: true,
            qualityOptimization: true
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.5 Design Generator
 * Enhanced design generation with advanced features
 */ __turbopack_context__.s({
    "Revo15DesignGenerator": (()=>Revo15DesignGenerator)
});
class Revo15DesignGenerator {
    modelId = 'revo-1.5';
    /**
   * Generate enhanced design using Revo 1.5 specifications
   */ async generateDesign(request) {
        const startTime = Date.now();
        try {
            console.log('🎨 Revo 1.5: Starting enhanced design generation...');
            console.log('- Business Type:', request.businessType);
            console.log('- Platform:', request.platform);
            console.log('- Visual Style:', request.visualStyle);
            console.log('- Artifacts:', request.artifactInstructions ? 'Yes' : 'No');
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid design generation request for Revo 1.5');
            }
            // Generate enhanced design using Gemini 2.5 or fallback
            const designResult = await this.generateEnhancedDesign(request);
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateEnhancedQualityScore(designResult);
            console.log(`✅ Revo 1.5: Enhanced design generated successfully in ${processingTime}ms`);
            console.log(`⭐ Quality Score: ${qualityScore}/10`);
            return {
                success: true,
                data: designResult,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 2,
                    enhancementsApplied: [
                        'enhanced-ai-engine',
                        'advanced-styling',
                        'brand-consistency-advanced',
                        'multi-aspect-ratio',
                        'quality-optimization',
                        ...request.artifactInstructions ? [
                            'artifact-integration'
                        ] : []
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            console.error('❌ Revo 1.5: Enhanced design generation failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Generate enhanced design using Gemini 2.5 or fallback services
   */ async generateEnhancedDesign(request) {
        try {
            // Try enhanced design generation first
            const { generateEnhancedDesign } = await __turbopack_context__.r("[project]/src/ai/gemini-2.5-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                // Enhanced text combination for Revo 1.5
                const components = [
                    request.imageText.catchyWords
                ];
                if (request.imageText.subheadline) {
                    components.push(request.imageText.subheadline);
                }
                if (request.imageText.callToAction) {
                    components.push(request.imageText.callToAction);
                }
                imageText = components.join('\n');
            }
            // Generate enhanced design
            const result = await generateEnhancedDesign({
                businessType: request.businessType,
                platform: request.platform,
                visualStyle: request.visualStyle,
                imageText,
                brandProfile: request.brandProfile,
                brandConsistency: request.brandConsistency,
                artifactInstructions: request.artifactInstructions
            });
            return {
                platform: request.platform,
                imageUrl: result.imageUrl,
                caption: imageText,
                hashtags: []
            };
        } catch (error) {
            console.warn('⚠️ Revo 1.5: Enhanced design failed, using fallback:', error);
            // Fallback to basic generation
            return this.generateFallbackDesign(request);
        }
    }
    /**
   * Fallback design generation
   */ async generateFallbackDesign(request) {
        try {
            const { generatePostFromProfile } = await __turbopack_context__.r("[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                imageText = request.imageText.catchyWords;
                if (request.imageText.subheadline) {
                    imageText += '\n' + request.imageText.subheadline;
                }
            }
            // Create generation parameters
            const generationParams = {
                businessType: request.businessType,
                location: request.brandProfile.location || '',
                writingTone: request.brandProfile.writingTone || 'professional',
                contentThemes: request.brandProfile.contentThemes || '',
                visualStyle: request.visualStyle,
                logoDataUrl: request.brandProfile.logoDataUrl,
                designExamples: request.brandConsistency?.strictConsistency ? request.brandProfile.designExamples || [] : [],
                primaryColor: request.brandProfile.primaryColor,
                accentColor: request.brandProfile.accentColor,
                backgroundColor: request.brandProfile.backgroundColor,
                dayOfWeek: new Date().toLocaleDateString('en-US', {
                    weekday: 'long'
                }),
                currentDate: new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }),
                variants: [
                    {
                        platform: request.platform,
                        aspectRatio: this.getOptimalAspectRatio(request.platform)
                    }
                ],
                services: '',
                targetAudience: request.brandProfile.targetAudience || '',
                keyFeatures: '',
                competitiveAdvantages: '',
                brandConsistency: request.brandConsistency || {
                    strictConsistency: false,
                    followBrandColors: true
                }
            };
            const result = await generatePostFromProfile(generationParams);
            if (result.variants && result.variants.length > 0) {
                return {
                    ...result.variants[0],
                    caption: imageText,
                    hashtags: result.hashtags || []
                };
            }
            // Final fallback
            return {
                platform: request.platform,
                imageUrl: '',
                caption: imageText,
                hashtags: []
            };
        } catch (error) {
            console.error('❌ Revo 1.5: Fallback design generation failed:', error);
            return {
                platform: request.platform,
                imageUrl: '',
                caption: typeof request.imageText === 'string' ? request.imageText : request.imageText.catchyWords,
                hashtags: []
            };
        }
    }
    /**
   * Get optimal aspect ratio for platform (Revo 1.5 supports multiple)
   */ getOptimalAspectRatio(platform) {
        switch(platform){
            case 'Instagram':
                return '1:1'; // Can also support 9:16 for stories
            case 'Facebook':
                return '16:9';
            case 'Twitter':
                return '16:9';
            case 'LinkedIn':
                return '16:9';
            default:
                return '1:1';
        }
    }
    /**
   * Validate design generation request for Revo 1.5
   */ validateRequest(request) {
        // Check required fields
        if (!request.businessType || !request.platform || !request.brandProfile) {
            return false;
        }
        // Check image text
        if (!request.imageText) {
            return false;
        }
        // Revo 1.5 supports multiple aspect ratios and artifacts
        return true;
    }
    /**
   * Calculate enhanced quality score for generated design
   */ calculateEnhancedQualityScore(variant) {
        let score = 6; // Higher base score for Revo 1.5
        // Image generation success
        if (variant.imageUrl && variant.imageUrl.length > 0) {
            score += 2;
        }
        // Caption quality
        if (variant.caption && variant.caption.length > 10) {
            score += 0.5;
        }
        if (variant.caption && variant.caption.length > 50) {
            score += 0.5;
        }
        // Hashtags presence and quality
        if (variant.hashtags && variant.hashtags.length > 0) {
            score += 0.5;
        }
        if (variant.hashtags && variant.hashtags.length >= 5) {
            score += 0.5;
        }
        // Platform optimization
        if (variant.platform) {
            score += 0.5;
        }
        // Revo 1.5 can achieve higher quality scores
        return Math.min(score, 9);
    }
    /**
   * Health check for enhanced design generator
   */ async healthCheck() {
        try {
            // Check if we can access enhanced AI services
            const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
            return hasGeminiKey || hasOpenAIKey;
        } catch (error) {
            console.error('❌ Revo 1.5 Design Generator health check failed:', error);
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'design',
            capabilities: [
                'Enhanced image generation',
                'Multiple aspect ratios (1:1, 16:9, 9:16)',
                'Advanced brand integration',
                'Artifact support',
                'Superior text overlay',
                'Advanced color harmony',
                'Layout optimization',
                'Platform-specific optimization'
            ],
            limitations: [
                'Higher credit cost (2x)',
                'Longer processing times',
                'Requires more system resources'
            ],
            supportedPlatforms: [
                'Instagram',
                'Facebook',
                'Twitter',
                'LinkedIn'
            ],
            supportedAspectRatios: [
                '1:1',
                '16:9',
                '9:16'
            ],
            averageProcessingTime: '20-35 seconds',
            qualityRange: '7-9/10',
            costPerGeneration: 2,
            resolution: '1024x1024 to 2048x2048',
            enhancedFeatures: {
                multipleAspectRatios: true,
                artifactSupport: true,
                advancedStyling: true,
                brandConsistencyAdvanced: true,
                qualityOptimization: true,
                textOverlayAdvanced: true
            }
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.5 Model Implementation
 * Enhanced Model - Advanced Features
 */ __turbopack_context__.s({
    "Revo15Implementation": (()=>Revo15Implementation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)");
;
;
;
class Revo15Implementation {
    model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getModelConfig"])('revo-1.5');
    contentGenerator;
    designGenerator;
    constructor(){
        this.contentGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15ContentGenerator"]();
        this.designGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15DesignGenerator"]();
    }
    /**
   * Check if the model is available and ready to use
   */ async isAvailable() {
        try {
            // Check if Gemini 2.5 (preferred) or fallback services are available
            const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
            // Revo 1.5 needs at least one advanced AI service
            return hasGeminiKey || hasOpenAIKey;
        } catch (error) {
            console.error('❌ Revo 1.5 availability check failed:', error);
            return false;
        }
    }
    /**
   * Validate a generation request for this model
   */ validateRequest(request) {
        try {
            // Basic validation
            if (!request || !request.modelId) {
                return false;
            }
            // Check if this is the correct model
            if (request.modelId !== 'revo-1.5') {
                return false;
            }
            // Content generation validation
            if ('profile' in request) {
                const contentRequest = request;
                return !!(contentRequest.profile && contentRequest.platform && contentRequest.profile.businessType);
            }
            // Design generation validation
            if ('businessType' in request) {
                const designRequest = request;
                return !!(designRequest.businessType && designRequest.platform && designRequest.visualStyle && designRequest.brandProfile);
            }
            return false;
        } catch (error) {
            console.error('❌ Revo 1.5 request validation failed:', error);
            return false;
        }
    }
    /**
   * Get model-specific information
   */ getModelInfo() {
        return {
            id: this.model.id,
            name: this.model.name,
            version: this.model.version,
            description: this.model.description,
            status: this.model.status,
            capabilities: this.model.capabilities,
            pricing: this.model.pricing,
            features: this.model.features,
            strengths: [
                'Advanced AI engine with superior capabilities',
                'Enhanced content generation algorithms',
                'Superior quality control and consistency',
                'Professional design generation',
                'Improved brand integration',
                'Real-time context and trending topics',
                'Full artifact support',
                'Multiple aspect ratios'
            ],
            limitations: [
                'Higher credit cost than Revo 1.0',
                'Longer processing times',
                'No video generation (coming in 2.0)',
                'Requires more system resources'
            ],
            bestUseCases: [
                'Growing businesses',
                'Marketing agencies',
                'Content creators',
                'Professional brands',
                'Users wanting enhanced quality',
                'Artifact-based workflows',
                'Multi-platform campaigns'
            ]
        };
    }
    /**
   * Get performance metrics for this model
   */ async getPerformanceMetrics() {
        return {
            modelId: this.model.id,
            averageProcessingTime: 25000,
            successRate: 0.92,
            averageQualityScore: 8.1,
            costEfficiency: 'medium',
            reliability: 'very good',
            userSatisfaction: 4.4,
            lastUpdated: new Date().toISOString()
        };
    }
    /**
   * Health check for this specific model
   */ async healthCheck() {
        try {
            const isAvailable = await this.isAvailable();
            const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;
            const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;
            const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;
            return {
                healthy,
                details: {
                    modelAvailable: isAvailable,
                    contentGenerator: contentGeneratorHealthy,
                    designGenerator: designGeneratorHealthy,
                    enhancedFeaturesEnabled: true,
                    artifactSupportEnabled: true,
                    realTimeContextEnabled: true,
                    timestamp: new Date().toISOString()
                }
            };
        } catch (error) {
            return {
                healthy: false,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    /**
   * Get enhanced features specific to Revo 1.5
   */ getEnhancedFeatures() {
        return {
            artifactSupport: {
                enabled: true,
                supportedTypes: [
                    'image',
                    'text',
                    'reference'
                ],
                maxArtifacts: 5,
                features: [
                    'exact-use',
                    'reference',
                    'text-overlay'
                ]
            },
            realTimeContext: {
                enabled: true,
                features: [
                    'weather',
                    'events',
                    'trending-topics',
                    'local-optimization'
                ]
            },
            advancedDesign: {
                enabled: true,
                aspectRatios: [
                    '1:1',
                    '16:9',
                    '9:16'
                ],
                qualityEnhancements: [
                    'color-harmony',
                    'layout-optimization',
                    'brand-consistency'
                ],
                textOverlay: 'advanced'
            },
            contentEnhancements: {
                enabled: true,
                features: [
                    'content-variants',
                    'hashtag-analysis',
                    'market-intelligence'
                ],
                qualityLevel: 'enhanced'
            }
        };
    }
}
;
;
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo15ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15ContentGenerator"]),
    "Revo15DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15DesignGenerator"]),
    "Revo15Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Revo15Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo15ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo15ContentGenerator"]),
    "Revo15DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo15DesignGenerator"]),
    "Revo15Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo15Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1473f2a8._.js.map