module.exports = {

"[project]/node_modules/node-fetch/src/utils/multipart-parser.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_1defaf5c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/utils/multipart-parser.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_utils_rss-feeds-integration_ts_cc1e7466._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root-of-the-server]__db310d95._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/gemini-2.5-design.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/ai/gemini-2.5-design.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)");
    });
});
}}),

};