{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAsHsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA2PsB,6BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/dashboard/post-card.tsx"], "sourcesContent": ["// src/components/dashboard/post-card.tsx\r\n\"use client\";\r\n\r\nimport * as React from 'react';\r\nimport Image from \"next/image\";\r\nimport { Facebook, Instagram, Linkedin, MoreVertical, Pen, RefreshCw, Twitter, CalendarIcon, Download, Loader2, Video, ChevronLeft, ChevronRight, ImageOff, Copy, Eye } from \"lucide-react\";\r\nimport { toPng } from 'html-to-image';\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardFooter,\r\n  CardHeader,\r\n} from \"@/components/ui/card\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport type { BrandProfile, GeneratedPost, Platform, PostVariant } from \"@/lib/types\";\r\nimport { format } from 'date-fns';\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Label } from '../ui/label';\r\nimport { Textarea } from '../ui/textarea';\r\nimport { Input } from '../ui/input';\r\nimport { generateContentAction, generateVideoContentAction } from '@/app/actions';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { cn } from '@/lib/utils';\r\nimport { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '../ui/carousel';\r\n\r\n// Helper function to validate URLs\r\nconst isValidUrl = (url: string): boolean => {\r\n  if (!url || typeof url !== 'string') {\r\n    return false;\r\n  }\r\n\r\n  // Handle compression placeholders\r\n  if (url === '[COMPRESSED_IMAGE]' || url === '[TRUNCATED]' || url.includes('[') && url.includes(']')) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    // Check for data URLs (base64 images)\r\n    if (url.startsWith('data:')) {\r\n      return url.includes('base64,') || url.includes('charset=');\r\n    }\r\n\r\n    // Check for HTTP/HTTPS URLs\r\n    const parsedUrl = new URL(url);\r\n    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';\r\n  } catch (error) {\r\n    // Don't log compression placeholders as errors\r\n    if (!url.includes('[') || !url.includes(']')) {\r\n      console.warn('URL validation failed for:', url.substring(0, 50) + '...', error.message);\r\n    }\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Utility function to detect image format from data URL\r\n */\r\nfunction getImageFormatFromDataUrl(dataUrl: string): { format: string; extension: string } {\r\n  if (dataUrl.startsWith('data:image/svg+xml')) {\r\n    return { format: 'svg', extension: 'svg' };\r\n  } else if (dataUrl.startsWith('data:image/png;base64,')) {\r\n    return { format: 'png', extension: 'png' };\r\n  } else if (dataUrl.startsWith('data:image/jpeg;base64,') || dataUrl.startsWith('data:image/jpg;base64,')) {\r\n    return { format: 'jpeg', extension: 'jpg' };\r\n  } else if (dataUrl.startsWith('data:image/webp;base64,')) {\r\n    return { format: 'webp', extension: 'webp' };\r\n  }\r\n  return { format: 'png', extension: 'png' }; // default fallback\r\n}\r\n\r\nconst platformIcons: { [key in Platform]: React.ReactElement } = {\r\n  Facebook: <Facebook className=\"h-4 w-4\" />,\r\n  Instagram: <Instagram className=\"h-4 w-4\" />,\r\n  LinkedIn: <Linkedin className=\"h-4 w-4\" />,\r\n  Twitter: <Twitter className=\"h-4 w-4\" />,\r\n};\r\n\r\ntype PostCardProps = {\r\n  post: GeneratedPost;\r\n  brandProfile: BrandProfile;\r\n  onPostUpdated: (post: GeneratedPost) => Promise<void>;\r\n};\r\n\r\nexport function PostCard({ post, brandProfile, onPostUpdated }: PostCardProps) {\r\n  const [isEditing, setIsEditing] = React.useState(false);\r\n  const [isRegenerating, setIsRegenerating] = React.useState(false);\r\n  const [isGeneratingVideo, setIsGeneratingVideo] = React.useState(false);\r\n  const [editedContent, setEditedContent] = React.useState(post.content);\r\n  const [editedHashtags, setEditedHashtags] = React.useState(post.hashtags);\r\n  const [videoUrl, setVideoUrl] = React.useState<string | undefined>(post.videoUrl);\r\n  const [showVideoDialog, setShowVideoDialog] = React.useState(false);\r\n  const [showImagePreview, setShowImagePreview] = React.useState(false);\r\n  const [previewImageUrl, setPreviewImageUrl] = React.useState<string>('');\r\n  // Ensure variants array exists and has at least one item\r\n  const safeVariants = post.variants && post.variants.length > 0 ? post.variants : [{\r\n    platform: (post.platform || 'instagram') as Platform,\r\n    imageUrl: post.imageUrl || ''\r\n  }];\r\n\r\n  const [activeTab, setActiveTab] = React.useState<Platform>(safeVariants[0]?.platform || 'instagram');\r\n  const downloadRefs = React.useRef<Record<Platform, HTMLDivElement | null>>({} as Record<Platform, HTMLDivElement | null>);\r\n\r\n  // Check if this is a Revo 2.0 post (single platform)\r\n  const isRevo2Post = post.id?.startsWith('revo2-') || safeVariants.length === 1;\r\n\r\n  const formattedDate = React.useMemo(() => {\r\n    try {\r\n      const date = new Date(post.date);\r\n      if (isNaN(date.getTime())) {\r\n        // If date is invalid, use current date\r\n        return format(new Date(), 'MMM d, yyyy');\r\n      }\r\n      return format(date, 'MMM d, yyyy');\r\n    } catch (error) {\r\n      // Fallback to current date if any error occurs\r\n      return format(new Date(), 'MMM d, yyyy');\r\n    }\r\n  }, [post.date]);\r\n  const { toast } = useToast();\r\n\r\n  // Platform-specific dimensions - MUST match backend Revo 2.0 generation\r\n  const getPlatformDimensions = React.useCallback((platform: Platform) => {\r\n    switch (platform.toLowerCase()) {\r\n      case 'instagram':\r\n        return { width: 1080, height: 1080, aspectClass: 'aspect-square' };\r\n      case 'facebook':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'twitter':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'linkedin':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'tiktok':\r\n        return { width: 1080, height: 1920, aspectClass: 'aspect-[9/16]' };\r\n      default:\r\n        return { width: 1080, height: 1080, aspectClass: 'aspect-square' };\r\n    }\r\n  }, []);\r\n\r\n  // Copy functionality\r\n  const handleCopyCaption = React.useCallback(async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(post.content);\r\n      toast({\r\n        title: \"Caption Copied!\",\r\n        description: \"The caption has been copied to your clipboard.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Copy Failed\",\r\n        description: \"Could not copy the caption. Please try again.\",\r\n      });\r\n    }\r\n  }, [post.content, toast]);\r\n\r\n  const handleCopyHashtags = React.useCallback(async () => {\r\n    try {\r\n      const hashtagsText = typeof post.hashtags === 'string' ? post.hashtags : post.hashtags?.join(' ') || '';\r\n      await navigator.clipboard.writeText(hashtagsText);\r\n      toast({\r\n        title: \"Hashtags Copied!\",\r\n        description: \"The hashtags have been copied to your clipboard.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Copy Failed\",\r\n        description: \"Could not copy the hashtags. Please try again.\",\r\n      });\r\n    }\r\n  }, [post.hashtags, toast]);\r\n\r\n  // Image preview functionality\r\n  const handleImagePreview = React.useCallback((imageUrl: string) => {\r\n    setPreviewImageUrl(imageUrl);\r\n    setShowImagePreview(true);\r\n  }, []);\r\n\r\n  const handleDownload = React.useCallback(async () => {\r\n    const activeVariant = safeVariants.find(v => v.platform === activeTab);\r\n\r\n    // First try to download the original HD image directly if URL is valid\r\n    if (activeVariant?.imageUrl && isValidUrl(activeVariant.imageUrl)) {\r\n      try {\r\n        // Check if it's a data URL (base64 encoded image)\r\n        if (activeVariant.imageUrl.startsWith('data:')) {\r\n          const { format, extension } = getImageFormatFromDataUrl(activeVariant.imageUrl);\r\n\r\n          // For social media posts, we need raster images (PNG/JPEG), not SVG\r\n          if (format === 'svg') {\r\n            console.log('🎨 Converting SVG to PNG for social media compatibility...');\r\n            // Fall through to the canvas conversion method below\r\n            // This will convert the SVG to a high-quality PNG\r\n          } else {\r\n            // Handle other data URL formats (PNG, JPEG, etc.) directly\r\n            const link = document.createElement('a');\r\n            link.href = activeVariant.imageUrl;\r\n            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n\r\n            toast({\r\n              title: \"Social Media Image Ready\",\r\n              description: `High-definition ${format.toUpperCase()} image downloaded successfully.`,\r\n            });\r\n            return;\r\n          }\r\n        } else {\r\n          // Handle regular HTTP/HTTPS URLs (not data URLs)\r\n          try {\r\n            const response = await fetch(activeVariant.imageUrl);\r\n            const blob = await response.blob();\r\n            const url = window.URL.createObjectURL(blob);\r\n\r\n            // Determine file extension based on content type\r\n            const contentType = response.headers.get('content-type') || blob.type;\r\n            let extension = 'png'; // default\r\n            if (contentType.includes('jpeg') || contentType.includes('jpg')) {\r\n              extension = 'jpg';\r\n            } else if (contentType.includes('webp')) {\r\n              extension = 'webp';\r\n            }\r\n\r\n            const link = document.createElement('a');\r\n            link.href = url;\r\n            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n            window.URL.revokeObjectURL(url);\r\n\r\n            toast({\r\n              title: \"Social Media Image Ready\",\r\n              description: \"High-definition image downloaded successfully.\",\r\n            });\r\n            return;\r\n          } catch (error) {\r\n            console.warn('Direct download failed, falling back to canvas conversion:', error);\r\n            // Fall through to canvas conversion\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.warn('Direct HD download failed, falling back to capture method:', error);\r\n      }\r\n    }\r\n\r\n    // Fallback: Capture the displayed image with maximum quality settings\r\n    const nodeToCapture = downloadRefs.current[activeTab];\r\n    if (!nodeToCapture) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Download Failed\",\r\n        description: \"Could not find the image element to download.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Check if we're converting an SVG enhanced design\r\n      const activeVariant = safeVariants.find(v => v.platform === activeTab);\r\n      const isSvgDataUrl = activeVariant?.imageUrl?.startsWith('data:image/svg+xml');\r\n      const platformDimensions = getPlatformDimensions(activeTab);\r\n\r\n      // Platform-specific optimized settings for social media posts\r\n      const socialMediaSettings = {\r\n        cacheBust: true,\r\n        canvasWidth: platformDimensions.width,\r\n        canvasHeight: platformDimensions.height,\r\n        pixelRatio: 3, // High DPI for crisp images\r\n        quality: 1.0, // Maximum quality\r\n        backgroundColor: '#ffffff', // White background for transparency\r\n        style: {\r\n          borderRadius: '0',\r\n          border: 'none',\r\n        }\r\n      };\r\n\r\n      // Enhanced settings for SVG conversion\r\n      if (isSvgDataUrl) {\r\n        socialMediaSettings.canvasWidth = platformDimensions.width;\r\n        socialMediaSettings.canvasHeight = platformDimensions.height;\r\n        socialMediaSettings.pixelRatio = 4; // Extra high DPI for SVG conversion\r\n        console.log(`🎨 Converting enhanced SVG design to ${platformDimensions.width}x${platformDimensions.height} PNG for ${activeTab}...`);\r\n      }\r\n\r\n      const dataUrl = await toPng(nodeToCapture, socialMediaSettings);\r\n\r\n      const link = document.createElement('a');\r\n      link.href = dataUrl;\r\n      link.download = `nevis-social-${post.id}-${activeTab}.png`;\r\n      link.click();\r\n\r\n      // Provide specific feedback based on content type\r\n      const successMessage = isSvgDataUrl\r\n        ? \"Enhanced design converted to PNG for social media use.\"\r\n        : \"High-definition image ready for social media posting.\";\r\n\r\n      toast({\r\n        title: \"Social Media Image Ready\",\r\n        description: successMessage,\r\n      });\r\n\r\n    } catch (err) {\r\n      console.error(err);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Download Failed\",\r\n        description: `Could not download the image. Please try again. Error: ${(err as Error).message}`,\r\n      });\r\n    }\r\n  }, [post.id, activeTab, toast]);\r\n\r\n\r\n  const handleSaveChanges = async () => {\r\n    const updatedPost = {\r\n      ...post,\r\n      content: editedContent,\r\n      hashtags: editedHashtags,\r\n      status: 'edited' as const,\r\n    };\r\n    await onPostUpdated(updatedPost);\r\n    setIsEditing(false);\r\n    toast({\r\n      title: \"Post Updated\",\r\n      description: \"Your changes have been saved.\",\r\n    });\r\n  };\r\n\r\n  const handleRegenerate = async () => {\r\n    setIsRegenerating(true);\r\n    try {\r\n      const platform = safeVariants[0].platform;\r\n      const newPost = await generateContentAction(brandProfile, platform);\r\n      onPostUpdated({ ...newPost, id: post.id }); // Keep old id for replacement\r\n      toast({\r\n        title: \"Post Regenerated!\",\r\n        description: \"A new version of your post has been generated.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Regeneration Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsRegenerating(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateVideo = async () => {\r\n    if (!post.catchyWords) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Cannot Generate Video\",\r\n        description: \"The post is missing the required catchy words.\",\r\n      });\r\n      return;\r\n    }\r\n    setIsGeneratingVideo(true);\r\n    try {\r\n      const result = await generateVideoContentAction(brandProfile, post.catchyWords, post.content);\r\n      const newVideoUrl = result.videoUrl;\r\n      setVideoUrl(newVideoUrl);\r\n      await onPostUpdated({ ...post, videoUrl: newVideoUrl });\r\n      setShowVideoDialog(true);\r\n      toast({\r\n        title: \"Video Generated!\",\r\n        description: \"Your video is ready to be viewed.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Video Generation Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsGeneratingVideo(false);\r\n    }\r\n  };\r\n\r\n  const activeVariant = safeVariants.find(v => v.platform === activeTab) || safeVariants[0];\r\n\r\n  return (\r\n    <>\r\n      <Card className=\"flex flex-col w-full\">\r\n        <CardHeader className=\"flex-row items-center justify-between gap-4 p-4\">\r\n          <div className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\r\n            <CalendarIcon className=\"h-4 w-4\" />\r\n            <span>{formattedDate}</span>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button size=\"icon\" variant=\"ghost\" className=\"h-6 w-6\" disabled={isRegenerating || isGeneratingVideo}>\r\n                <MoreVertical className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuItem onClick={() => setIsEditing(true)}>\r\n                <Pen className=\"mr-2 h-4 w-4\" />\r\n                Edit Text\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleRegenerate} disabled={isRegenerating}>\r\n                {isRegenerating ? (\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                ) : (\r\n                  <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                Regenerate Image\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleGenerateVideo} disabled={isGeneratingVideo}>\r\n                {isGeneratingVideo ? (\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                ) : (\r\n                  <Video className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                Generate Video\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleDownload}>\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Download Image\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </CardHeader>\r\n        <CardContent className=\"flex-grow space-y-4 p-4 pt-0\">\r\n          {isRevo2Post ? (\r\n            // Revo 2.0 single-platform layout with platform icon at top left\r\n            <div className=\"space-y-4\">\r\n              {/* Platform Icon Header - Left aligned */}\r\n              <div className=\"flex items-center justify-start p-3 bg-muted/30 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  {platformIcons[safeVariants[0]?.platform || 'instagram']}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Single Image Display - Platform-specific dimensions */}\r\n              {(() => {\r\n                const variant = safeVariants[0];\r\n                const dimensions = getPlatformDimensions(variant?.platform || 'instagram');\r\n\r\n                return (\r\n                  <div className={`relative ${dimensions.aspectClass} w-full overflow-hidden`}>\r\n                    {(isRegenerating || isGeneratingVideo) && (\r\n                      <div className=\"absolute inset-0 z-10 flex items-center justify-center bg-card/80\">\r\n                        <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                        <span className=\"sr-only\">{isRegenerating ? 'Regenerating image...' : 'Generating video...'}</span>\r\n                      </div>\r\n                    )}\r\n                    <div ref={el => (downloadRefs.current[variant?.platform || 'instagram'] = el)} className={`relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`}>\r\n                      {variant?.imageUrl && isValidUrl(variant.imageUrl) ? (\r\n                        <div\r\n                          className=\"relative h-full w-full cursor-pointer\"\r\n                          onClick={() => handleImagePreview(variant.imageUrl)}\r\n                        >\r\n                          <Image\r\n                            alt={`Generated post image for ${variant.platform}`}\r\n                            className={cn('h-full w-full object-cover transition-opacity', (isRegenerating || isGeneratingVideo) ? 'opacity-50' : 'opacity-100')}\r\n                            height={dimensions.height}\r\n                            src={variant.imageUrl}\r\n                            data-ai-hint=\"social media post\"\r\n                            width={dimensions.width}\r\n                            crossOrigin=\"anonymous\"\r\n                            unoptimized={variant.imageUrl.startsWith('data:')} // Don't optimize data URLs\r\n                          />\r\n                          {/* Preview overlay */}\r\n                          <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                            <div className=\"bg-white/90 rounded-full p-2\">\r\n                              <Eye className=\"h-5 w-5 text-gray-700\" />\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"flex h-full w-full items-center justify-center bg-muted flex-col gap-2\">\r\n                          <ImageOff className=\"h-12 w-12 text-muted-foreground\" />\r\n                          {variant?.imageUrl && !isValidUrl(variant.imageUrl) && (\r\n                            <div className=\"absolute bottom-2 left-2 right-2\">\r\n                              <div className=\"text-xs text-red-500 bg-white/90 p-2 rounded\">\r\n                                {variant.imageUrl.includes('[') && variant.imageUrl.includes(']') ? (\r\n                                  <div>\r\n                                    <p className=\"font-medium\">Image temporarily unavailable</p>\r\n                                    <p className=\"text-gray-600 mt-1\">\r\n                                      {variant.imageUrl.includes('Large image data removed')\r\n                                        ? 'Image was too large for storage. Try regenerating.'\r\n                                        : 'Image data was optimized for storage.'\r\n                                      }\r\n                                    </p>\r\n                                  </div>\r\n                                ) : (\r\n                                  <p>Invalid image URL</p>\r\n                                )}\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          ) : (\r\n            // Multi-platform tab layout for Revo 1.0/1.5\r\n            <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as Platform)} className=\"w-full\">\r\n              <TabsList className=\"grid w-full grid-cols-4\">\r\n                {safeVariants.map(variant => (\r\n                  <TabsTrigger key={variant.platform} value={variant.platform}>\r\n                    {platformIcons[variant.platform]}\r\n                  </TabsTrigger>\r\n                ))}\r\n              </TabsList>\r\n              {safeVariants.map(variant => {\r\n                const dimensions = getPlatformDimensions(variant.platform);\r\n                return (\r\n                  <TabsContent key={variant.platform} value={variant.platform}>\r\n                    <div className={`relative ${dimensions.aspectClass} w-full overflow-hidden`}>\r\n                      {(isRegenerating || isGeneratingVideo) && (\r\n                        <div className=\"absolute inset-0 z-10 flex items-center justify-center bg-card/80\">\r\n                          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                          <span className=\"sr-only\">{isRegenerating ? 'Regenerating image...' : 'Generating video...'}</span>\r\n                        </div>\r\n                      )}\r\n                      <div ref={el => (downloadRefs.current[variant.platform] = el)} className={`relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`}>\r\n                        {variant.imageUrl && isValidUrl(variant.imageUrl) ? (\r\n                          <div\r\n                            className=\"relative h-full w-full cursor-pointer\"\r\n                            onClick={() => handleImagePreview(variant.imageUrl)}\r\n                          >\r\n                            <Image\r\n                              alt={`Generated post image for ${variant.platform}`}\r\n                              className={cn('h-full w-full object-cover transition-opacity', (isRegenerating || isGeneratingVideo) ? 'opacity-50' : 'opacity-100')}\r\n                              height={dimensions.height}\r\n                              src={variant.imageUrl}\r\n                              data-ai-hint=\"social media post\"\r\n                              width={dimensions.width}\r\n                              crossOrigin=\"anonymous\"\r\n                              unoptimized={variant.imageUrl.startsWith('data:')} // Don't optimize data URLs\r\n                            />\r\n                            {/* Preview overlay */}\r\n                            <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                              <div className=\"bg-white/90 rounded-full p-2\">\r\n                                <Eye className=\"h-5 w-5 text-gray-700\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"flex h-full w-full items-center justify-center bg-muted\">\r\n                            <ImageOff className=\"h-12 w-12 text-muted-foreground\" />\r\n                            {variant.imageUrl && !isValidUrl(variant.imageUrl) && (\r\n                              <div className=\"absolute bottom-2 left-2 right-2\">\r\n                                <p className=\"text-xs text-red-500 bg-white/90 p-1 rounded\">\r\n                                  Invalid image URL\r\n                                </p>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </TabsContent>\r\n                );\r\n              })}\r\n            </Tabs>\r\n          )}\r\n\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-start justify-between gap-2\">\r\n              <p className=\"text-sm text-foreground line-clamp-4 flex-1\">{post.content}</p>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={handleCopyCaption}\r\n                className=\"h-8 w-8 p-0 flex-shrink-0\"\r\n                title=\"Copy caption\"\r\n              >\r\n                <Copy className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n        <CardFooter className=\"p-4 pt-0\">\r\n          <div className=\"flex items-start justify-between gap-2\">\r\n            <div className=\"flex flex-wrap gap-1 flex-1\">\r\n              {post.hashtags && (() => {\r\n                // Handle both string and array formats for hashtags\r\n                const hashtagsArray = typeof post.hashtags === 'string'\r\n                  ? post.hashtags.split(\" \")\r\n                  : Array.isArray(post.hashtags)\r\n                    ? post.hashtags\r\n                    : [];\r\n\r\n                return hashtagsArray.map((tag, index) => (\r\n                  <Badge key={index} variant=\"secondary\" className=\"font-normal\">\r\n                    {tag}\r\n                  </Badge>\r\n                ));\r\n              })()}\r\n              {!post.hashtags && (\r\n                <Badge variant=\"secondary\" className=\"font-normal\">\r\n                  #enhanced #ai #design\r\n                </Badge>\r\n              )}\r\n            </div>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleCopyHashtags}\r\n              className=\"h-8 w-8 p-0 flex-shrink-0\"\r\n              title=\"Copy hashtags\"\r\n            >\r\n              <Copy className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n      {/* Edit Post Dialog */}\r\n      <Dialog open={isEditing} onOpenChange={setIsEditing}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Edit Post</DialogTitle>\r\n            <DialogDescription>\r\n              Make changes to your post content and hashtags below. Click save when you're done.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"content\">Content</Label>\r\n              <Textarea\r\n                id=\"content\"\r\n                value={editedContent}\r\n                onChange={(e) => setEditedContent(e.target.value)}\r\n                className=\"h-32\"\r\n              />\r\n            </div>\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"hashtags\">Hashtags</Label>\r\n              <Input\r\n                id=\"hashtags\"\r\n                value={editedHashtags}\r\n                onChange={(e) => setEditedHashtags(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsEditing(false)}>Cancel</Button>\r\n            <Button onClick={handleSaveChanges}>Save Changes</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* View Video Dialog */}\r\n      <Dialog open={showVideoDialog} onOpenChange={setShowVideoDialog}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Generated Video</DialogTitle>\r\n            <DialogDescription>\r\n              Here is the video generated for your post. You can download it from here.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"my-4\">\r\n            {videoUrl ? (\r\n              <video controls autoPlay src={videoUrl} className=\"w-full rounded-md\" />\r\n            ) : (\r\n              <p>No video available.</p>\r\n            )}\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowVideoDialog(false)}>Close</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Image Preview Modal */}\r\n      <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>\r\n        <DialogContent className=\"sm:max-w-4xl max-h-[90vh] p-2\">\r\n          <DialogHeader className=\"pb-2\">\r\n            <DialogTitle>Image Preview</DialogTitle>\r\n            <DialogDescription>\r\n              Click and drag to pan, scroll to zoom\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"flex items-center justify-center max-h-[70vh] overflow-hidden\">\r\n            {previewImageUrl && (\r\n              <img\r\n                src={previewImageUrl}\r\n                alt=\"Post image preview\"\r\n                className=\"max-w-full max-h-full object-contain rounded-lg\"\r\n              />\r\n            )}\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowImagePreview(false)}>\r\n              Close\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAMA;AAQA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AArCA;;;;;;;;;;;;;;;;;;;AAwCA,mCAAmC;AACnC,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,kCAAkC;IAClC,IAAI,QAAQ,wBAAwB,QAAQ,iBAAiB,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM;QACnG,OAAO;IACT;IAEA,IAAI;QACF,sCAAsC;QACtC,IAAI,IAAI,UAAU,CAAC,UAAU;YAC3B,OAAO,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC;QACjD;QAEA,4BAA4B;QAC5B,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,QAAQ,KAAK,WAAW,UAAU,QAAQ,KAAK;IAClE,EAAE,OAAO,OAAO;QACd,+CAA+C;QAC/C,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM;YAC5C,QAAQ,IAAI,CAAC,8BAA8B,IAAI,SAAS,CAAC,GAAG,MAAM,OAAO,MAAM,OAAO;QACxF;QACA,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,OAAe;IAChD,IAAI,QAAQ,UAAU,CAAC,uBAAuB;QAC5C,OAAO;YAAE,QAAQ;YAAO,WAAW;QAAM;IAC3C,OAAO,IAAI,QAAQ,UAAU,CAAC,2BAA2B;QACvD,OAAO;YAAE,QAAQ;YAAO,WAAW;QAAM;IAC3C,OAAO,IAAI,QAAQ,UAAU,CAAC,8BAA8B,QAAQ,UAAU,CAAC,2BAA2B;QACxG,OAAO;YAAE,QAAQ;YAAQ,WAAW;QAAM;IAC5C,OAAO,IAAI,QAAQ,UAAU,CAAC,4BAA4B;QACxD,OAAO;YAAE,QAAQ;YAAQ,WAAW;QAAO;IAC7C;IACA,OAAO;QAAE,QAAQ;QAAO,WAAW;IAAM,GAAG,mBAAmB;AACjE;AAEA,MAAM,gBAA2D;IAC/D,wBAAU,8OAAC,0MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,yBAAW,8OAAC,4MAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IAChC,wBAAU,8OAAC,0MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,uBAAS,8OAAC,wMAAA,CAAA,UAAO;QAAC,WAAU;;;;;;AAC9B;AAQO,SAAS,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAiB;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,KAAK,OAAO;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,KAAK,QAAQ;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAsB,KAAK,QAAQ;IAChF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAU;IACrE,yDAAyD;IACzD,MAAM,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAAI,KAAK,QAAQ,GAAG;QAAC;YAChF,UAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;QAC7B;KAAE;IAEF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAY,YAAY,CAAC,EAAE,EAAE,YAAY;IACxF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAA2C,CAAC;IAE5E,qDAAqD;IACrD,MAAM,cAAc,KAAK,EAAE,EAAE,WAAW,aAAa,aAAa,MAAM,KAAK;IAE7E,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAClC,IAAI;YACF,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;YAC/B,IAAI,MAAM,KAAK,OAAO,KAAK;gBACzB,uCAAuC;gBACvC,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;YAC5B;YACA,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,+CAA+C;YAC/C,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;QAC5B;IACF,GAAG;QAAC,KAAK,IAAI;KAAC;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,wEAAwE;IACxE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAC/C,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAM,QAAQ;oBAAM,aAAa;gBAAgB;YACnE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAM,QAAQ;oBAAK,aAAa;gBAAgB;YAClE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAM,QAAQ;oBAAK,aAAa;gBAAgB;YAClE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAM,QAAQ;oBAAK,aAAa;gBAAgB;YAClE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAM,QAAQ;oBAAM,aAAa;gBAAgB;YACnE;gBACE,OAAO;oBAAE,OAAO;oBAAM,QAAQ;oBAAM,aAAa;gBAAgB;QACrE;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QAC1C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;YAChD,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF,GAAG;QAAC,KAAK,OAAO;QAAE;KAAM;IAExB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QAC3C,IAAI;YACF,MAAM,eAAe,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG,KAAK,QAAQ,EAAE,KAAK,QAAQ;YACrG,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF,GAAG;QAAC,KAAK,QAAQ;QAAE;KAAM;IAEzB,8BAA8B;IAC9B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAC5C,mBAAmB;QACnB,oBAAoB;IACtB,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACvC,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAE5D,uEAAuE;QACvE,IAAI,eAAe,YAAY,WAAW,cAAc,QAAQ,GAAG;YACjE,IAAI;gBACF,kDAAkD;gBAClD,IAAI,cAAc,QAAQ,CAAC,UAAU,CAAC,UAAU;oBAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,0BAA0B,cAAc,QAAQ;oBAE9E,oEAAoE;oBACpE,IAAI,WAAW,OAAO;wBACpB,QAAQ,GAAG,CAAC;oBACZ,qDAAqD;oBACrD,kDAAkD;oBACpD,OAAO;wBACL,2DAA2D;wBAC3D,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,IAAI,GAAG,cAAc,QAAQ;wBAClC,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;wBACnE,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,KAAK,KAAK;wBACV,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,MAAM;4BACJ,OAAO;4BACP,aAAa,CAAC,gBAAgB,EAAE,OAAO,WAAW,GAAG,+BAA+B,CAAC;wBACvF;wBACA;oBACF;gBACF,OAAO;oBACL,iDAAiD;oBACjD,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,cAAc,QAAQ;wBACnD,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;wBAEvC,iDAAiD;wBACjD,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,IAAI;wBACrE,IAAI,YAAY,OAAO,UAAU;wBACjC,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,QAAQ;4BAC/D,YAAY;wBACd,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;4BACvC,YAAY;wBACd;wBAEA,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,IAAI,GAAG;wBACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;wBACnE,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,KAAK,KAAK;wBACV,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;wBAE3B,MAAM;4BACJ,OAAO;4BACP,aAAa;wBACf;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,8DAA8D;oBAC3E,oCAAoC;oBACtC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,8DAA8D;YAC7E;QACF;QAEA,sEAAsE;QACtE,MAAM,gBAAgB,aAAa,OAAO,CAAC,UAAU;QACrD,IAAI,CAAC,eAAe;YAClB,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,IAAI;YACF,mDAAmD;YACnD,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAC5D,MAAM,eAAe,eAAe,UAAU,WAAW;YACzD,MAAM,qBAAqB,sBAAsB;YAEjD,8DAA8D;YAC9D,MAAM,sBAAsB;gBAC1B,WAAW;gBACX,aAAa,mBAAmB,KAAK;gBACrC,cAAc,mBAAmB,MAAM;gBACvC,YAAY;gBACZ,SAAS;gBACT,iBAAiB;gBACjB,OAAO;oBACL,cAAc;oBACd,QAAQ;gBACV;YACF;YAEA,uCAAuC;YACvC,IAAI,cAAc;gBAChB,oBAAoB,WAAW,GAAG,mBAAmB,KAAK;gBAC1D,oBAAoB,YAAY,GAAG,mBAAmB,MAAM;gBAC5D,oBAAoB,UAAU,GAAG,GAAG,oCAAoC;gBACxE,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,mBAAmB,KAAK,CAAC,CAAC,EAAE,mBAAmB,MAAM,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC;YACrI;YAEA,MAAM,UAAU,MAAM,CAAA,GAAA,kJAAA,CAAA,QAAK,AAAD,EAAE,eAAe;YAE3C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC;YAC1D,KAAK,KAAK;YAEV,kDAAkD;YAClD,MAAM,iBAAiB,eACnB,2DACA;YAEJ,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,CAAC,uDAAuD,EAAE,AAAC,IAAc,OAAO,EAAE;YACjG;QACF;IACF,GAAG;QAAC,KAAK,EAAE;QAAE;QAAW;KAAM;IAG9B,MAAM,oBAAoB;QACxB,MAAM,cAAc;YAClB,GAAG,IAAI;YACP,SAAS;YACT,UAAU;YACV,QAAQ;QACV;QACA,MAAM,cAAc;QACpB,aAAa;QACb,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,YAAY,CAAC,EAAE,CAAC,QAAQ;YACzC,MAAM,UAAU,MAAM,CAAA,GAAA,kJAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;YAC1D,cAAc;gBAAE,GAAG,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC,IAAI,8BAA8B;YAC1E,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QACA,qBAAqB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kJAAA,CAAA,6BAA0B,AAAD,EAAE,cAAc,KAAK,WAAW,EAAE,KAAK,OAAO;YAC5F,MAAM,cAAc,OAAO,QAAQ;YACnC,YAAY;YACZ,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY;YACrD,mBAAmB;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,YAAY,CAAC,EAAE;IAEzF,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;kDAAM;;;;;;;;;;;;0CAET,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAQ,WAAU;4CAAU,UAAU,kBAAkB;sDAClF,cAAA,8OAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,aAAa;;kEAC5C,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAkB,UAAU;;oDACpD,+BACC,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACrB;;;;;;;0DAGJ,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAqB,UAAU;;oDACvD,kCACC,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDACjB;;;;;;;0DAGJ,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,cACC,iEAAiE;0CACjE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,YAAY;;;;;;;;;;;oCAK3D,CAAC;wCACA,MAAM,UAAU,YAAY,CAAC,EAAE;wCAC/B,MAAM,aAAa,sBAAsB,SAAS,YAAY;wCAE9D,qBACE,8OAAC;4CAAI,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,uBAAuB,CAAC;;gDACxE,CAAC,kBAAkB,iBAAiB,mBACnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAAW,iBAAiB,0BAA0B;;;;;;;;;;;;8DAG1E,8OAAC;oDAAI,KAAK,CAAA,KAAO,aAAa,OAAO,CAAC,SAAS,YAAY,YAAY,GAAG;oDAAK,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,+CAA+C,CAAC;8DAC1K,SAAS,YAAY,WAAW,QAAQ,QAAQ,kBAC/C,8OAAC;wDACC,WAAU;wDACV,SAAS,IAAM,mBAAmB,QAAQ,QAAQ;;0EAElD,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KAAK,CAAC,yBAAyB,EAAE,QAAQ,QAAQ,EAAE;gEACnD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD,AAAC,kBAAkB,oBAAqB,eAAe;gEACtH,QAAQ,WAAW,MAAM;gEACzB,KAAK,QAAQ,QAAQ;gEACrB,gBAAa;gEACb,OAAO,WAAW,KAAK;gEACvB,aAAY;gEACZ,aAAa,QAAQ,QAAQ,CAAC,UAAU,CAAC;;;;;;0EAG3C,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;6EAKrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,SAAS,YAAY,CAAC,WAAW,QAAQ,QAAQ,mBAChD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,QAAQ,CAAC,qBAC3D,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAc;;;;;;0FAC3B,8OAAC;gFAAE,WAAU;0FACV,QAAQ,QAAQ,CAAC,QAAQ,CAAC,8BACvB,uDACA;;;;;;;;;;;6FAKR,8OAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAUvB,CAAC;;;;;;uCAGH,6CAA6C;0CAC7C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,OAAO;gCAAW,eAAe,CAAC,IAAM,aAAa;gCAAgB,WAAU;;kDACnF,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;kDACjB,aAAa,GAAG,CAAC,CAAA,wBAChB,8OAAC,gIAAA,CAAA,cAAW;gDAAwB,OAAO,QAAQ,QAAQ;0DACxD,aAAa,CAAC,QAAQ,QAAQ,CAAC;+CADhB,QAAQ,QAAQ;;;;;;;;;;oCAKrC,aAAa,GAAG,CAAC,CAAA;wCAChB,MAAM,aAAa,sBAAsB,QAAQ,QAAQ;wCACzD,qBACE,8OAAC,gIAAA,CAAA,cAAW;4CAAwB,OAAO,QAAQ,QAAQ;sDACzD,cAAA,8OAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,uBAAuB,CAAC;;oDACxE,CAAC,kBAAkB,iBAAiB,mBACnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAW,iBAAiB,0BAA0B;;;;;;;;;;;;kEAG1E,8OAAC;wDAAI,KAAK,CAAA,KAAO,aAAa,OAAO,CAAC,QAAQ,QAAQ,CAAC,GAAG;wDAAK,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,+CAA+C,CAAC;kEAC1J,QAAQ,QAAQ,IAAI,WAAW,QAAQ,QAAQ,kBAC9C,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,mBAAmB,QAAQ,QAAQ;;8EAElD,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK,CAAC,yBAAyB,EAAE,QAAQ,QAAQ,EAAE;oEACnD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD,AAAC,kBAAkB,oBAAqB,eAAe;oEACtH,QAAQ,WAAW,MAAM;oEACzB,KAAK,QAAQ,QAAQ;oEACrB,gBAAa;oEACb,OAAO,WAAW,KAAK;oEACvB,aAAY;oEACZ,aAAa,QAAQ,QAAQ,CAAC,UAAU,CAAC;;;;;;8EAG3C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;iFAKrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,QAAQ,QAAQ,IAAI,CAAC,WAAW,QAAQ,QAAQ,mBAC/C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CApCxD,QAAQ,QAAQ;;;;;oCA+CtC;;;;;;;0CAIJ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA+C,KAAK,OAAO;;;;;;sDACxE,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKxB,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,QAAQ,IAAI,CAAC;4CACjB,oDAAoD;4CACpD,MAAM,gBAAgB,OAAO,KAAK,QAAQ,KAAK,WAC3C,KAAK,QAAQ,CAAC,KAAK,CAAC,OACpB,MAAM,OAAO,CAAC,KAAK,QAAQ,IACzB,KAAK,QAAQ,GACb,EAAE;4CAER,OAAO,cAAc,GAAG,CAAC,CAAC,KAAK,sBAC7B,8OAAC,iIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAY,WAAU;8DAC9C;mDADS;;;;;wCAIhB,CAAC;wCACA,CAAC,KAAK,QAAQ,kBACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAc;;;;;;;;;;;;8CAKvD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAW,cAAc;0BACrC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAIvD,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,aAAa;8CAAQ;;;;;;8CAC9D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;0BAM1C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAiB,cAAc;0BAC3C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;sCACZ,yBACC,8OAAC;gCAAM,QAAQ;gCAAC,QAAQ;gCAAC,KAAK;gCAAU,WAAU;;;;;qDAElD,8OAAC;0CAAE;;;;;;;;;;;sCAGP,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAM1E,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;sCACZ,iCACC,8OAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;;;;;;sCAIhB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,oBAAoB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAQjF", "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAwesB,qCAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-brand-profiles.ts"], "sourcesContent": ["// Hook for managing brand profiles with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { brandProfileFirebaseService } from '@/lib/firebase/services/brand-profile-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\nexport interface BrandProfilesState {\r\n  profiles: CompleteBrandProfile[];\r\n  currentProfile: CompleteBrandProfile | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useBrandProfiles() {\r\n  const userId = useUserId();\r\n  const [state, setState] = useState<BrandProfilesState>({\r\n    profiles: [],\r\n    currentProfile: null,\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load brand profiles\r\n  const loadProfiles = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, profiles: [], currentProfile: null }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n\r\n      // Try to load from Firestore, fallback to localStorage\r\n      let profiles: CompleteBrandProfile[] = [];\r\n      try {\r\n        profiles = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n      } catch (firebaseError) {\r\n        console.log('🔄 Firebase unavailable, using localStorage fallback');\r\n        // Fallback to localStorage\r\n        const stored = localStorage.getItem('completeBrandProfile');\r\n        if (stored) {\r\n          const profile = JSON.parse(stored);\r\n          profiles = [profile];\r\n        }\r\n      }\r\n\r\n      const currentProfile = profiles.length > 0 ? profiles[0] : null;\r\n\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles,\r\n        currentProfile,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\r\n      }));\r\n    }\r\n  }, [userId]);\r\n\r\n  // Save brand profile\r\n  const saveProfile = useCallback(async (profile: CompleteBrandProfile): Promise<string> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to save profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      const profileId = await brandProfileFirebaseService.saveBrandProfile(profile, userId);\r\n\r\n      // Reload profiles to get the updated list\r\n      await loadProfiles();\r\n\r\n      setState(prev => ({ ...prev, saving: false }));\r\n      return profileId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, loadProfiles]);\r\n\r\n  // Update brand profile\r\n  const updateProfile = useCallback(async (\r\n    profileId: string,\r\n    updates: Partial<CompleteBrandProfile>\r\n  ): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to update profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      await brandProfileFirebaseService.updateBrandProfile(profileId, updates);\r\n\r\n      // Update local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.map(p =>\r\n          p.id === profileId ? { ...p, ...updates } : p\r\n        ),\r\n        currentProfile: prev.currentProfile?.id === profileId\r\n          ? { ...prev.currentProfile, ...updates }\r\n          : prev.currentProfile,\r\n        saving: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to update profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Delete brand profile\r\n  const deleteProfile = useCallback(async (profileId: string): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to delete profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, error: null }));\r\n\r\n      await brandProfileFirebaseService.delete(profileId);\r\n\r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.filter(p => p.id !== profileId),\r\n        currentProfile: prev.currentProfile?.id === profileId ? null : prev.currentProfile,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Set current profile\r\n  const setCurrentProfile = useCallback((profile: CompleteBrandProfile | null) => {\r\n    console.log('🎯 setCurrentProfile called with:', profile?.businessName || 'null');\r\n    setState(prev => {\r\n      console.log('📊 Previous current profile:', prev.currentProfile?.businessName || 'none');\r\n      return { ...prev, currentProfile: profile };\r\n    });\r\n  }, []);\r\n\r\n  // Get profile by ID\r\n  const getProfileById = useCallback(async (profileId: string): Promise<CompleteBrandProfile | null> => {\r\n    try {\r\n      return await brandProfileFirebaseService.getBrandProfileById(profileId);\r\n    } catch (error) {\r\n      console.error('Failed to get profile by ID:', error);\r\n      return null;\r\n    }\r\n  }, []);\r\n\r\n  // Load profiles when userId changes\r\n  useEffect(() => {\r\n    loadProfiles();\r\n  }, [loadProfiles]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = brandProfileFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (profiles) => {\r\n        console.log('🔄 Real-time profiles update received:', profiles.length, 'profiles');\r\n        setState(prev => {\r\n          // Preserve the current profile if it still exists in the updated profiles\r\n          let preservedCurrentProfile = prev.currentProfile;\r\n\r\n          if (prev.currentProfile) {\r\n            // Check if current profile still exists in the updated list\r\n            const stillExists = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n            if (!stillExists) {\r\n              console.log('⚠️ Current profile no longer exists, clearing selection');\r\n              preservedCurrentProfile = null;\r\n            } else {\r\n              // Update with the latest version of the current profile\r\n              const updatedProfile = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n              if (updatedProfile) {\r\n                console.log('✅ Current profile updated with latest data:', updatedProfile.businessName);\r\n                preservedCurrentProfile = updatedProfile;\r\n              }\r\n            }\r\n          }\r\n\r\n          // Only auto-select first profile if there's no current profile at all AND this is the initial load\r\n          const finalCurrentProfile = preservedCurrentProfile ||\r\n            (!prev.currentProfile && profiles.length > 0 ? profiles[0] : null);\r\n\r\n          if (finalCurrentProfile && !prev.currentProfile) {\r\n            console.log('🎯 Auto-selecting first profile on initial load:', finalCurrentProfile.businessName);\r\n          }\r\n\r\n          return {\r\n            ...prev,\r\n            profiles,\r\n            currentProfile: finalCurrentProfile,\r\n          };\r\n        });\r\n      },\r\n      { orderBy: 'updatedAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId]);\r\n\r\n  return {\r\n    ...state,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    setCurrentProfile,\r\n    getProfileById,\r\n    reload: loadProfiles,\r\n  };\r\n}\r\n\r\n// Hook for getting the current/latest brand profile\r\nexport function useCurrentBrandProfile() {\r\n  const { currentProfile, loading, error } = useBrandProfiles();\r\n\r\n  return {\r\n    profile: currentProfile,\r\n    loading,\r\n    error,\r\n  };\r\n}\r\n\r\n// Hook for checking if user has a complete brand profile\r\nexport function useHasCompleteBrandProfile(): boolean {\r\n  const { currentProfile, loading } = useBrandProfiles();\r\n\r\n  if (loading || !currentProfile) return false;\r\n\r\n  // Check if profile has required fields\r\n  const requiredFields = [\r\n    'businessName',\r\n    'businessType',\r\n    'location',\r\n    'description',\r\n    'services',\r\n  ];\r\n\r\n  return requiredFields.every(field => {\r\n    const value = currentProfile[field as keyof CompleteBrandProfile];\r\n    return value && (\r\n      typeof value === 'string' ? value.trim().length > 0 :\r\n        Array.isArray(value) ? value.length > 0 :\r\n          true\r\n    );\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AACA;;;;AAWO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,UAAU,EAAE;QACZ,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,QAAQ;YACX,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,UAAU,EAAE;oBAAE,gBAAgB;gBAAK,CAAC;YACjF;QACF;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,uDAAuD;YACvD,IAAI,WAAmC,EAAE;YACzC,IAAI;gBACF,WAAW,MAAM,iKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;YACpE,EAAE,OAAO,eAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,2BAA2B;gBAC3B,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,IAAI,QAAQ;oBACV,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,WAAW;wBAAC;qBAAQ;gBACtB;YACF;YAEA,MAAM,iBAAiB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;YAE3D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA;oBACA,SAAS;gBACX,CAAC;QACH,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG;QAAC;KAAO;IAEX,qBAAqB;IACrB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAM,OAAO;gBAAK,CAAC;YAExD,MAAM,YAAY,MAAM,iKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,SAAS;YAE9E,0CAA0C;YAC1C,MAAM;YAEN,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAM,CAAC;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF,GAAG;QAAC;QAAQ;KAAa;IAEzB,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAChC,WACA;QAEA,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAM,OAAO;gBAAK,CAAC;YAExD,MAAM,iKAAA,CAAA,8BAA2B,CAAC,kBAAkB,CAAC,WAAW;YAEhE,oCAAoC;YACpC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC1B,EAAE,EAAE,KAAK,YAAY;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;oBAE9C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YACxC;wBAAE,GAAG,KAAK,cAAc;wBAAE,GAAG,OAAO;oBAAC,IACrC,KAAK,cAAc;oBACvB,QAAQ;gBACV,CAAC;QACH,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF,GAAG;QAAC;KAAO;IAEX,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAK,CAAC;YAE1C,MAAM,iKAAA,CAAA,8BAA2B,CAAC,MAAM,CAAC;YAEzC,qBAAqB;YACrB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC7C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YAAY,OAAO,KAAK,cAAc;gBACpF,CAAC;QACH,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF,GAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,QAAQ,GAAG,CAAC,qCAAqC,SAAS,gBAAgB;QAC1E,SAAS,CAAA;YACP,QAAQ,GAAG,CAAC,gCAAgC,KAAK,cAAc,EAAE,gBAAgB;YACjF,OAAO;gBAAE,GAAG,IAAI;gBAAE,gBAAgB;YAAQ;QAC5C;IACF,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,OAAO,MAAM,iKAAA,CAAA,8BAA2B,CAAC,mBAAmB,CAAC;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF,GAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,cAAc,iKAAA,CAAA,8BAA2B,CAAC,qBAAqB,CACnE,QACA,CAAC;YACC,QAAQ,GAAG,CAAC,0CAA0C,SAAS,MAAM,EAAE;YACvE,SAAS,CAAA;gBACP,0EAA0E;gBAC1E,IAAI,0BAA0B,KAAK,cAAc;gBAEjD,IAAI,KAAK,cAAc,EAAE;oBACvB,4DAA4D;oBAC5D,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;oBAC9E,IAAI,CAAC,aAAa;wBAChB,QAAQ,GAAG,CAAC;wBACZ,0BAA0B;oBAC5B,OAAO;wBACL,wDAAwD;wBACxD,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;wBACjF,IAAI,gBAAgB;4BAClB,QAAQ,GAAG,CAAC,+CAA+C,eAAe,YAAY;4BACtF,0BAA0B;wBAC5B;oBACF;gBACF;gBAEA,mGAAmG;gBACnG,MAAM,sBAAsB,2BAC1B,CAAC,CAAC,KAAK,cAAc,IAAI,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG,IAAI;gBAEnE,IAAI,uBAAuB,CAAC,KAAK,cAAc,EAAE;oBAC/C,QAAQ,GAAG,CAAC,oDAAoD,oBAAoB,YAAY;gBAClG;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP;oBACA,gBAAgB;gBAClB;YACF;QACF,GACA;YAAE,SAAS;YAAa,gBAAgB;QAAO;QAGjD,OAAO;IACT,GAAG;QAAC;KAAO;IAEX,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;AAGO,SAAS;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS;QACT;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;IAEpC,IAAI,WAAW,CAAC,gBAAgB,OAAO;IAEvC,uCAAuC;IACvC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,eAAe,KAAK,CAAC,CAAA;QAC1B,MAAM,QAAQ,cAAc,CAAC,MAAoC;QACjE,OAAO,SAAS,CACd,OAAO,UAAU,WAAW,MAAM,IAAI,GAAG,MAAM,GAAG,IAChD,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,IACpC,IACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 1774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-generated-posts.ts"], "sourcesContent": ["// Hook for managing generated posts with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { generatedPostFirebaseService } from '@/lib/firebase/services/generated-post-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport { useCurrentBrandProfile } from './use-brand-profiles';\r\nimport type { GeneratedPost, Platform } from '@/lib/types';\r\n\r\nexport interface GeneratedPostsState {\r\n  posts: GeneratedPost[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useGeneratedPosts(limit: number = 10) {\r\n  const userId = useUserId();\r\n  const { profile: currentProfile } = useCurrentBrandProfile();\r\n  const [state, setState] = useState<GeneratedPostsState>({\r\n    posts: [],\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load generated posts\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, posts: [] }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      \r\n      const posts = await generatedPostFirebaseService.getUserGeneratedPosts(userId, { limit });\r\n      \r\n      setState(prev => ({\r\n        ...prev,\r\n        posts,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load posts',\r\n      }));\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Save generated post\r\n  const savePost = useCallback(async (post: GeneratedPost): Promise<string> => {\r\n    if (!userId || !currentProfile) {\r\n      throw new Error('User must be authenticated and have a brand profile to save posts');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n      \r\n      const postId = await generatedPostFirebaseService.saveGeneratedPost(post, userId, currentProfile.id);\r\n      \r\n      // Add to local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: [{ ...post, id: postId }, ...prev.posts].slice(0, limit),\r\n        saving: false,\r\n      }));\r\n      \r\n      return postId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save post',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, currentProfile, limit]);\r\n\r\n  // Update post analytics\r\n  const updatePostAnalytics = useCallback(async (\r\n    postId: string,\r\n    analytics: {\r\n      views?: number;\r\n      likes?: number;\r\n      shares?: number;\r\n      comments?: number;\r\n      qualityScore?: number;\r\n      engagementPrediction?: number;\r\n      brandAlignmentScore?: number;\r\n    }\r\n  ): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.updatePostAnalytics(postId, analytics);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, ...analytics }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to update post analytics:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Update post status\r\n  const updatePostStatus = useCallback(async (\r\n    postId: string,\r\n    status: 'generated' | 'edited' | 'posted'\r\n  ): Promise<void> => {\r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      const publishedAt = status === 'posted' ? new Date() : undefined;\r\n      \r\n      await generatedPostFirebaseService.updatePostStatus(postId, firestoreStatus, undefined, publishedAt);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, status }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to update post status:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Delete post\r\n  const deletePost = useCallback(async (postId: string): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.delete(postId);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.filter(post => post.id !== postId),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to delete post:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Get posts by platform\r\n  const getPostsByPlatform = useCallback(async (platform: Platform): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      return await generatedPostFirebaseService.getUserGeneratedPosts(userId, { platform, limit });\r\n    } catch (error) {\r\n      console.error('Failed to get posts by platform:', error);\r\n      return [];\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Get posts by status\r\n  const getPostsByStatus = useCallback(async (status: 'generated' | 'edited' | 'posted'): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      return await generatedPostFirebaseService.getPostsByStatus(userId, firestoreStatus);\r\n    } catch (error) {\r\n      console.error('Failed to get posts by status:', error);\r\n      return [];\r\n    }\r\n  }, [userId]);\r\n\r\n  // Load posts when dependencies change\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = generatedPostFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (posts) => {\r\n        setState(prev => ({\r\n          ...prev,\r\n          posts: posts.slice(0, limit),\r\n        }));\r\n      },\r\n      { limit, orderBy: 'createdAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId, limit]);\r\n\r\n  return {\r\n    ...state,\r\n    savePost,\r\n    updatePostAnalytics,\r\n    updatePostStatus,\r\n    deletePost,\r\n    getPostsByPlatform,\r\n    getPostsByStatus,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for getting posts for a specific brand profile\r\nexport function useGeneratedPostsForBrand(brandProfileId: string, limit: number = 10) {\r\n  const userId = useUserId();\r\n  const [posts, setPosts] = useState<GeneratedPost[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId || !brandProfileId) {\r\n      setPosts([]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const brandPosts = await generatedPostFirebaseService.getRecentPostsForBrand(\r\n        userId, \r\n        brandProfileId, \r\n        limit\r\n      );\r\n      \r\n      setPosts(brandPosts);\r\n      setLoading(false);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load posts');\r\n      setLoading(false);\r\n    }\r\n  }, [userId, brandProfileId, limit]);\r\n\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  return {\r\n    posts,\r\n    loading,\r\n    error,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for post statistics\r\nexport function usePostStatistics() {\r\n  const { posts } = useGeneratedPosts(100); // Get more posts for statistics\r\n  \r\n  const statistics = {\r\n    total: posts.length,\r\n    byPlatform: posts.reduce((acc, post) => {\r\n      acc[post.platform] = (acc[post.platform] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<Platform, number>),\r\n    byStatus: posts.reduce((acc, post) => {\r\n      acc[post.status] = (acc[post.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>),\r\n    averageQuality: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.qualityScore || 0), 0) / posts.length \r\n      : 0,\r\n    averageEngagement: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.engagementPrediction || 0), 0) / posts.length \r\n      : 0,\r\n  };\r\n\r\n  return statistics;\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;;AACnD;AACA;AACA;AACA;;;;;AAUO,SAAS,kBAAkB,QAAgB,EAAE;IAClD,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,OAAO,EAAE;QACT,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,uBAAuB;IACvB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,QAAQ;YACX,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,OAAO,EAAE;gBAAC,CAAC;YACxD;QACF;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,QAAQ,MAAM,kKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;gBAAE;YAAM;YAEvF,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA,SAAS;gBACX,CAAC;QACH,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAClC,IAAI,CAAC,UAAU,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAM,OAAO;gBAAK,CAAC;YAExD,MAAM,SAAS,MAAM,kKAAA,CAAA,+BAA4B,CAAC,iBAAiB,CAAC,MAAM,QAAQ,eAAe,EAAE;YAEnG,oCAAoC;YACpC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,OAAO;wBAAC;4BAAE,GAAG,IAAI;4BAAE,IAAI;wBAAO;2BAAM,KAAK,KAAK;qBAAC,CAAC,KAAK,CAAC,GAAG;oBACzD,QAAQ;gBACV,CAAC;YAED,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,MAAM;QACR;IACF,GAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACtC,QACA;QAUA,IAAI;YACF,MAAM,kKAAA,CAAA,+BAA4B,CAAC,mBAAmB,CAAC,QAAQ;YAE/D,qBAAqB;YACrB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;4BAAE,GAAG,IAAI;4BAAE,GAAG,SAAS;wBAAC,IACxB;gBAER,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,QACA;QAEA,IAAI;YACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;YAC5D,MAAM,cAAc,WAAW,WAAW,IAAI,SAAS;YAEvD,MAAM,kKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ,iBAAiB,WAAW;YAExF,qBAAqB;YACrB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;4BAAE,GAAG,IAAI;4BAAE;wBAAO,IAClB;gBAER,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,MAAM,kKAAA,CAAA,+BAA4B,CAAC,MAAM,CAAC;YAE1C,qBAAqB;YACrB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAC/C,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5C,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,IAAI;YACF,OAAO,MAAM,kKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;gBAAE;gBAAU;YAAM;QAC5F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,IAAI;YACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;YAC5D,OAAO,MAAM,kKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ;QACrE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,EAAE;QACX;IACF,GAAG;QAAC;KAAO;IAEX,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,cAAc,kKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CACpE,QACA,CAAC;YACC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,OAAO,MAAM,KAAK,CAAC,GAAG;gBACxB,CAAC;QACH,GACA;YAAE;YAAO,SAAS;YAAa,gBAAgB;QAAO;QAGxD,OAAO;IACT,GAAG;QAAC;QAAQ;KAAM;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;AAGO,SAAS,0BAA0B,cAAsB,EAAE,QAAgB,EAAE;IAClF,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB;YAC9B,SAAS,EAAE;YACX,WAAW;YACX;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,aAAa,MAAM,kKAAA,CAAA,+BAA4B,CAAC,sBAAsB,CAC1E,QACA,gBACA;YAGF,SAAS;YACT,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,WAAW;QACb;IACF,GAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA,QAAQ;IACV;AACF;AAGO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,kBAAkB,MAAM,gCAAgC;IAE1E,MAAM,aAAa;QACjB,OAAO,MAAM,MAAM;QACnB,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK;YAC7B,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QACJ,UAAU,MAAM,MAAM,CAAC,CAAC,KAAK;YAC3B,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;YAC7C,OAAO;QACT,GAAG,CAAC;QACJ,gBAAgB,MAAM,MAAM,GAAG,IAC3B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GAC7E;QACJ,mBAAmB,MAAM,MAAM,GAAG,IAC9B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,oBAAoB,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GACrF;IACN;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/dashboard/content-calendar.tsx"], "sourcesContent": ["// src/components/dashboard/content-calendar.tsx\n\"use client\";\n\nimport React from \"react\";\nimport { Loader2, Facebook, Instagram, Linkedin, Twitter, Settings, Palette, Sparkles } from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { PostCard } from \"@/components/dashboard/post-card\";\nimport { generateContentAction, generateEnhancedDesignAction, generateContentWithArtifactsAction } from \"@/app/actions\";\n\nimport { useToast } from \"@/hooks/use-toast\";\nimport { useGeneratedPosts } from \"@/hooks/use-generated-posts\";\nimport { useFirebaseAuth } from \"@/hooks/use-firebase-auth\";\nimport type { BrandProfile, GeneratedPost, Platform, BrandConsistencyPreferences } from \"@/lib/types\";\n\ntype RevoModel = 'revo-1.0' | 'revo-1.5';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\nimport { Card, CardContent, CardDes<PERSON>, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Label } from \"@/components/ui/label\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { ArtifactSelector } from \"@/components/artifacts/artifact-selector\";\n\ntype ContentCalendarProps = {\n  brandProfile: BrandProfile;\n  posts: GeneratedPost[];\n  onPostGenerated: (post: GeneratedPost) => void;\n  onPostUpdated: (post: GeneratedPost) => Promise<void>;\n};\n\nconst platforms: { name: Platform; icon: React.ElementType }[] = [\n  { name: 'Instagram', icon: Instagram },\n  { name: 'Facebook', icon: Facebook },\n  { name: 'Twitter', icon: Twitter },\n  { name: 'LinkedIn', icon: Linkedin },\n];\n\nexport function ContentCalendar({ brandProfile, posts, onPostGenerated, onPostUpdated }: ContentCalendarProps) {\n  const [isGenerating, setIsGenerating] = React.useState<Platform | null>(null);\n  const { toast } = useToast();\n  const { user } = useFirebaseAuth();\n  const { savePost, saving } = useGeneratedPosts();\n\n  // Brand consistency preferences - default to consistent if design examples exist\n  const [brandConsistency, setBrandConsistency] = React.useState<BrandConsistencyPreferences>({\n    strictConsistency: !!(brandProfile.designExamples && brandProfile.designExamples.length > 0), // Auto-check if design examples exist\n    followBrandColors: true, // Always follow brand colors\n  });\n\n  // Revo model selection\n  const [selectedRevoModel, setSelectedRevoModel] = React.useState<RevoModel>('revo-1.5');\n\n  // Artifact selection for content generation\n  const [selectedArtifacts, setSelectedArtifacts] = React.useState<string[]>([]);\n\n  // Include people in designs toggle\n  const [includePeopleInDesigns, setIncludePeopleInDesigns] = React.useState<boolean>(true);\n\n  // Use local language toggle\n  const [useLocalLanguage, setUseLocalLanguage] = React.useState<boolean>(false);\n\n  // Save preferences to localStorage\n  React.useEffect(() => {\n    const savedPreferences = localStorage.getItem('brandConsistencyPreferences');\n    if (savedPreferences) {\n      setBrandConsistency(JSON.parse(savedPreferences));\n    }\n\n    const savedRevoModel = localStorage.getItem('selectedRevoModel');\n    if (savedRevoModel) {\n      setSelectedRevoModel(savedRevoModel as RevoModel);\n    }\n\n    const savedIncludePeople = localStorage.getItem('includePeopleInDesigns');\n    if (savedIncludePeople !== null) {\n      setIncludePeopleInDesigns(JSON.parse(savedIncludePeople));\n    }\n\n    const savedUseLocalLanguage = localStorage.getItem('useLocalLanguage');\n    if (savedUseLocalLanguage !== null) {\n      setUseLocalLanguage(JSON.parse(savedUseLocalLanguage));\n    }\n  }, []);\n\n  React.useEffect(() => {\n    localStorage.setItem('brandConsistencyPreferences', JSON.stringify(brandConsistency));\n  }, [brandConsistency]);\n\n  React.useEffect(() => {\n    localStorage.setItem('selectedRevoModel', selectedRevoModel);\n  }, [selectedRevoModel]);\n\n  React.useEffect(() => {\n    localStorage.setItem('includePeopleInDesigns', JSON.stringify(includePeopleInDesigns));\n  }, [includePeopleInDesigns]);\n\n  React.useEffect(() => {\n    localStorage.setItem('useLocalLanguage', JSON.stringify(useLocalLanguage));\n  }, [useLocalLanguage]);\n\n  const handleGenerateClick = async (platform: Platform) => {\n    setIsGenerating(platform);\n    try {\n      console.log('🚀 Starting content generation for platform:', platform);\n      console.log('👤 User authenticated:', !!user);\n      console.log('🏢 Brand profile:', brandProfile?.businessName);\n\n      let newPost;\n\n      // Check if artifacts are enabled (simple toggle approach)\n      const artifactsEnabled = selectedArtifacts.length > 0;\n\n      const useEnhancedGeneration = artifactsEnabled || selectedRevoModel === 'revo-1.5' || selectedRevoModel === 'revo-2.0';\n\n      if (selectedRevoModel === 'revo-2.0') {\n        console.log(`🚀 Using Revo 2.0 (Gemini 2.5 Flash Image) generation via server action`);\n\n        // Use server action to avoid client-side imports\n        const response = await fetch('/api/generate-revo-2.0', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            businessType: brandProfile.businessType || 'Business',\n            platform: platform.toLowerCase(),\n            visualStyle: brandProfile.visualStyle || 'modern',\n            imageText: `${brandProfile.businessName || brandProfile.businessType} - Premium Content`,\n            brandProfile,\n            aspectRatio: '1:1',\n            includePeopleInDesigns,\n            useLocalLanguage\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Revo 2.0 generation failed: ${response.statusText}`);\n        }\n\n        const revo20Result = await response.json();\n\n        newPost = {\n          id: `revo-2.0-${Date.now()}`,\n          content: revo20Result.caption || `🚀 Generated with Revo 2.0 (Gemini 2.5 Flash Image)\\n\\n${brandProfile.businessName || brandProfile.businessType} - Premium Content`,\n          hashtags: revo20Result.hashtags || ['#NextGen', '#AI', '#Innovation'],\n          imageUrl: revo20Result.imageUrl,\n          platform: platform,\n          date: new Date().toISOString(),\n          analytics: {\n            views: 0,\n            likes: 0,\n            shares: 0,\n            comments: 0,\n            engagementPrediction: 85,\n            brandAlignmentScore: 95,\n            qualityScore: revo20Result.qualityScore || 10\n          },\n          metadata: {\n            aiModel: revo20Result.model || 'Revo 2.0',\n            generationPrompt: 'Revo 2.0 Native Generation',\n            processingTime: revo20Result.processingTime || 0,\n            enhancementsApplied: revo20Result.enhancementsApplied || []\n          }\n        };\n      } else if (useEnhancedGeneration) {\n        console.log(`✨ Using enhanced generation with ${selectedRevoModel} model`);\n        // Use artifact-enhanced generation - will automatically use active artifacts from artifacts page\n        newPost = await generateContentWithArtifactsAction(\n          brandProfile,\n          platform,\n          brandConsistency,\n          [], // Empty array - let the action use active artifacts from artifacts service\n          selectedRevoModel === 'revo-1.5', // Enhanced design for Revo 1.5\n          includePeopleInDesigns,\n          useLocalLanguage\n        );\n      } else {\n        console.log(`📝 Using standard content generation with ${selectedRevoModel} model`);\n        // Use standard content generation\n        newPost = await generateContentAction(brandProfile, platform, brandConsistency);\n      }\n\n      console.log('📄 Generated post:', newPost.content.substring(0, 100) + '...');\n\n      // Save to Firestore database first\n      try {\n        console.log('💾 Saving post to Firestore database...');\n        const postId = await savePost(newPost);\n        console.log('✅ Post saved to Firestore with ID:', postId);\n\n        // Update the post with the Firestore ID\n        const savedPost = { ...newPost, id: postId };\n        onPostGenerated(savedPost);\n      } catch (saveError) {\n        console.error('❌ Failed to save to Firestore, falling back to localStorage:', saveError);\n        // Fallback to localStorage if Firestore fails\n        onPostGenerated(newPost);\n      }\n\n      // Dynamic toast message based on generation type\n      let title = \"Content Generated!\";\n      let description = `A new ${platform} post has been saved to your database.`;\n\n      if (selectedArtifacts.length > 0) {\n        title = \"Content Generated with References! 📎\";\n        description = `A new ${platform} post using ${selectedArtifacts.length} reference${selectedArtifacts.length !== 1 ? 's' : ''} has been saved.`;\n      } else if (selectedRevoModel === 'revo-1.5') {\n        title = \"Enhanced Content Generated! ✨\";\n        description = `A new enhanced ${platform} post with ${selectedRevoModel} has been saved.`;\n      } else {\n        title = \"Content Generated! 🚀\";\n        description = `A new ${platform} post with ${selectedRevoModel} has been saved.`;\n      }\n\n      toast({ title, description });\n    } catch (error) {\n      toast({\n        variant: \"destructive\",\n        title: \"Generation Failed\",\n        description: (error as Error).message,\n      });\n    } finally {\n      setIsGenerating(null);\n    }\n  };\n\n  // Ensure this component is always full-bleed inside the app shell and does not cause horizontal overflow.\n  return (\n    <div className=\"w-full max-w-[100vw] box-border overflow-x-hidden\">\n      <div className=\"w-full px-6 py-10 lg:py-16 lg:px-12\">\n        <div className=\"w-full box-border space-y-6\">\n          {/* Compact Brand Consistency Controls */}\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <Settings className=\"h-4 w-4 text-blue-600\" />\n                <span className=\"font-medium text-sm\">Brand Consistency</span>\n              </div>\n              <div className=\"flex items-center gap-4\">\n                <div className=\"flex items-center gap-2\">\n                  <Palette className=\"h-3 w-3 text-gray-500\" />\n                  <span className=\"text-xs text-gray-600\">Strict</span>\n                  <Switch\n                    checked={brandConsistency.strictConsistency}\n                    onCheckedChange={(checked) =>\n                      setBrandConsistency(prev => ({ ...prev, strictConsistency: checked }))\n                    }\n                  />\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Sparkles className=\"h-3 w-3 text-gray-500\" />\n                  <span className=\"text-xs text-gray-600\">Colors</span>\n                  <Switch\n                    checked={brandConsistency.followBrandColors}\n                    onCheckedChange={(checked) =>\n                      setBrandConsistency(prev => ({ ...prev, followBrandColors: checked }))\n                    }\n                  />\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-xs text-gray-600\">👥 People</span>\n                  <Switch\n                    checked={includePeopleInDesigns}\n                    onCheckedChange={setIncludePeopleInDesigns}\n                  />\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-xs text-gray-600\">🌍 Local</span>\n                  <Switch\n                    checked={useLocalLanguage}\n                    onCheckedChange={setUseLocalLanguage}\n                  />\n                </div>\n                <Separator orientation=\"vertical\" className=\"h-4\" />\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-xs text-gray-600\">AI Model:</span>\n                  <select\n                    value={selectedRevoModel}\n                    onChange={(e) => setSelectedRevoModel(e.target.value as RevoModel)}\n                    className=\"appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  >\n                    <option value=\"revo-1.0\">Revo 1.0</option>\n                    <option value=\"revo-1.5\">Revo 1.5</option>\n                    <option value=\"revo-2.0\">Revo 2.0</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n            <p className=\"text-xs text-gray-500 mt-2\">\n              {selectedRevoModel === 'revo-2.0'\n                ? `🚀 ${selectedRevoModel}: Next-Gen AI with native image generation, character consistency & intelligent editing`\n                : selectedRevoModel === 'revo-1.5'\n                  ? `✨ ${selectedRevoModel}: Enhanced AI with professional design principles + ${brandConsistency.strictConsistency ? \"strict consistency\" : \"brand colors\"}`\n                  : selectedRevoModel === 'revo-1.0'\n                    ? `🚀 ${selectedRevoModel}: Standard reliable AI + ${brandConsistency.strictConsistency ? \"strict consistency\" : \"brand colors\"}`\n                    : `🌟 ${selectedRevoModel}: Next-generation AI (coming soon)`\n              }\n            </p>\n          </div>\n\n          {/* Simple Artifacts Toggle */}\n          <div className=\"mb-6\">\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"space-y-1\">\n                    <Label className=\"text-sm font-medium\">Use Artifacts</Label>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Enable to use your uploaded reference materials and exact-use content\n                    </p>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <Switch\n                      checked={selectedArtifacts.length > 0}\n                      onCheckedChange={(checked) => {\n                        if (checked) {\n                          // Enable artifacts - this will use active artifacts from the artifacts page\n                          setSelectedArtifacts(['active']);\n                        } else {\n                          // Disable artifacts\n                          setSelectedArtifacts([]);\n                        }\n                      }}\n                    />\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => window.open('/artifacts', '_blank')}\n                      className=\"text-xs\"\n                    >\n                      Manage\n                    </Button>\n                  </div>\n                </div>\n                {selectedArtifacts.length > 0 && (\n                  <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md\">\n                    <p className=\"text-xs text-blue-700\">\n                      ✓ Artifacts enabled - Content will use your reference materials and exact-use items from the Artifacts page\n                    </p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n\n\n\n          <div className=\"flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold tracking-tight font-headline\">Content Calendar</h1>\n              <p className=\"text-muted-foreground\">\n                Here's your generated content. Click a post to edit or regenerate.\n              </p>\n            </div>\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button disabled={!!isGenerating}>\n                  {isGenerating ? (\n                    <>\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                      Generating for {isGenerating}...\n                    </>\n                  ) : (\n                    \"✨ Generate New Post\"\n                  )}\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent>\n                {platforms.map((p) => (\n                  <DropdownMenuItem key={p.name} onClick={() => handleGenerateClick(p.name)} disabled={!!isGenerating}>\n                    <p.icon className=\"mr-2 h-4 w-4\" />\n                    <span>{p.name}</span>\n                  </DropdownMenuItem>\n                ))}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n\n          {posts.length > 0 ? (\n            <div className=\"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full max-w-none\">\n              {posts.map((post) => (\n                <PostCard\n                  key={post.id}\n                  post={post}\n                  brandProfile={brandProfile}\n                  onPostUpdated={onPostUpdated}\n                />\n              ))}\n            </div>\n          ) : (\n            <div className=\"flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/30 bg-card p-12 text-center w-full\">\n              <h3 className=\"text-xl font-semibold\">Your calendar is empty</h3>\n              <p className=\"text-muted-foreground mt-2\">\n                Click the \"Generate\" button to create your first social media post!\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAGhD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;AA4BA,MAAM,YAA2D;IAC/D;QAAE,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;IAAC;IACrC;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACnC;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;IAAC;IACjC;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACpC;AAEM,SAAS,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAwB;IAC3G,MAAM,CAAC,cAAc,gBAAgB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB;IACxE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,oBAAiB,AAAD;IAE7C,iFAAiF;IACjF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAA8B;QAC1F,mBAAmB,CAAC,CAAC,CAAC,aAAa,cAAc,IAAI,aAAa,cAAc,CAAC,MAAM,GAAG,CAAC;QAC3F,mBAAmB;IACrB;IAEA,uBAAuB;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAY;IAE5E,4CAA4C;IAC5C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAW,EAAE;IAE7E,mCAAmC;IACnC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAU;IAEpF,4BAA4B;IAC5B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAU;IAExE,mCAAmC;IACnC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,IAAI,kBAAkB;YACpB,oBAAoB,KAAK,KAAK,CAAC;QACjC;QAEA,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,qBAAqB;QACvB;QAEA,MAAM,qBAAqB,aAAa,OAAO,CAAC;QAChD,IAAI,uBAAuB,MAAM;YAC/B,0BAA0B,KAAK,KAAK,CAAC;QACvC;QAEA,MAAM,wBAAwB,aAAa,OAAO,CAAC;QACnD,IAAI,0BAA0B,MAAM;YAClC,oBAAoB,KAAK,KAAK,CAAC;QACjC;IACF,GAAG,EAAE;IAEL,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,aAAa,OAAO,CAAC,+BAA+B,KAAK,SAAS,CAAC;IACrE,GAAG;QAAC;KAAiB;IAErB,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,aAAa,OAAO,CAAC,qBAAqB;IAC5C,GAAG;QAAC;KAAkB;IAEtB,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;IAChE,GAAG;QAAC;KAAuB;IAE3B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;IAC1D,GAAG;QAAC;KAAiB;IAErB,MAAM,sBAAsB,OAAO;QACjC,gBAAgB;QAChB,IAAI;YACF,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,QAAQ,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,QAAQ,GAAG,CAAC,qBAAqB,cAAc;YAE/C,IAAI;YAEJ,0DAA0D;YAC1D,MAAM,mBAAmB,kBAAkB,MAAM,GAAG;YAEpD,MAAM,wBAAwB,oBAAoB,sBAAsB,cAAc,sBAAsB;YAE5G,IAAI,sBAAsB,YAAY;gBACpC,QAAQ,GAAG,CAAC,CAAC,uEAAuE,CAAC;gBAErF,iDAAiD;gBACjD,MAAM,WAAW,MAAM,MAAM,0BAA0B;oBACrD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,cAAc,aAAa,YAAY,IAAI;wBAC3C,UAAU,SAAS,WAAW;wBAC9B,aAAa,aAAa,WAAW,IAAI;wBACzC,WAAW,GAAG,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,kBAAkB,CAAC;wBACxF;wBACA,aAAa;wBACb;wBACA;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;gBACtE;gBAEA,MAAM,eAAe,MAAM,SAAS,IAAI;gBAExC,UAAU;oBACR,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;oBAC5B,SAAS,aAAa,OAAO,IAAI,CAAC,uDAAuD,EAAE,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,kBAAkB,CAAC;oBACrK,UAAU,aAAa,QAAQ,IAAI;wBAAC;wBAAY;wBAAO;qBAAc;oBACrE,UAAU,aAAa,QAAQ;oBAC/B,UAAU;oBACV,MAAM,IAAI,OAAO,WAAW;oBAC5B,WAAW;wBACT,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,UAAU;wBACV,sBAAsB;wBACtB,qBAAqB;wBACrB,cAAc,aAAa,YAAY,IAAI;oBAC7C;oBACA,UAAU;wBACR,SAAS,aAAa,KAAK,IAAI;wBAC/B,kBAAkB;wBAClB,gBAAgB,aAAa,cAAc,IAAI;wBAC/C,qBAAqB,aAAa,mBAAmB,IAAI,EAAE;oBAC7D;gBACF;YACF,OAAO,IAAI,uBAAuB;gBAChC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,kBAAkB,MAAM,CAAC;gBACzE,iGAAiG;gBACjG,UAAU,MAAM,CAAA,GAAA,kJAAA,CAAA,qCAAkC,AAAD,EAC/C,cACA,UACA,kBACA,EAAE,EACF,sBAAsB,YACtB,wBACA;YAEJ,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,kBAAkB,MAAM,CAAC;gBAClF,kCAAkC;gBAClC,UAAU,MAAM,CAAA,GAAA,kJAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,UAAU;YAChE;YAEA,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;YAEtE,mCAAmC;YACnC,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,SAAS,MAAM,SAAS;gBAC9B,QAAQ,GAAG,CAAC,sCAAsC;gBAElD,wCAAwC;gBACxC,MAAM,YAAY;oBAAE,GAAG,OAAO;oBAAE,IAAI;gBAAO;gBAC3C,gBAAgB;YAClB,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,gEAAgE;gBAC9E,8CAA8C;gBAC9C,gBAAgB;YAClB;YAEA,iDAAiD;YACjD,IAAI,QAAQ;YACZ,IAAI,cAAc,CAAC,MAAM,EAAE,SAAS,sCAAsC,CAAC;YAE3E,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,QAAQ;gBACR,cAAc,CAAC,MAAM,EAAE,SAAS,YAAY,EAAE,kBAAkB,MAAM,CAAC,UAAU,EAAE,kBAAkB,MAAM,KAAK,IAAI,MAAM,GAAG,gBAAgB,CAAC;YAChJ,OAAO,IAAI,sBAAsB,YAAY;gBAC3C,QAAQ;gBACR,cAAc,CAAC,eAAe,EAAE,SAAS,WAAW,EAAE,kBAAkB,gBAAgB,CAAC;YAC3F,OAAO;gBACL,QAAQ;gBACR,cAAc,CAAC,MAAM,EAAE,SAAS,WAAW,EAAE,kBAAkB,gBAAgB,CAAC;YAClF;YAEA,MAAM;gBAAE;gBAAO;YAAY;QAC7B,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0GAA0G;IAC1G,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,iBAAiB;wDAC3C,iBAAiB,CAAC,UAChB,oBAAoB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB;gEAAQ,CAAC;;;;;;;;;;;;0DAI1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,iBAAiB;wDAC3C,iBAAiB,CAAC,UAChB,oBAAoB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB;gEAAQ,CAAC;;;;;;;;;;;;0DAI1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,iBAAiB;;;;;;;;;;;;0DAGrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,iBAAiB;;;;;;;;;;;;0DAGrB,8OAAC,qIAAA,CAAA,YAAS;gDAAC,aAAY;gDAAW,WAAU;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACpD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,8OAAC;gCAAE,WAAU;0CACV,sBAAsB,aACnB,CAAC,GAAG,EAAE,kBAAkB,uFAAuF,CAAC,GAChH,sBAAsB,aACpB,CAAC,EAAE,EAAE,kBAAkB,oDAAoD,EAAE,iBAAiB,iBAAiB,GAAG,uBAAuB,gBAAgB,GACzJ,sBAAsB,aACpB,CAAC,GAAG,EAAE,kBAAkB,yBAAyB,EAAE,iBAAiB,iBAAiB,GAAG,uBAAuB,gBAAgB,GAC/H,CAAC,GAAG,EAAE,kBAAkB,kCAAkC,CAAC;;;;;;;;;;;;kCAMvE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,kBAAkB,MAAM,GAAG;wDACpC,iBAAiB,CAAC;4DAChB,IAAI,SAAS;gEACX,4EAA4E;gEAC5E,qBAAqB;oEAAC;iEAAS;4DACjC,OAAO;gEACL,oBAAoB;gEACpB,qBAAqB,EAAE;4DACzB;wDACF;;;;;;kEAEF,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;wDACzC,WAAU;kEACX;;;;;;;;;;;;;;;;;;oCAKJ,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,UAAU,CAAC,CAAC;sDACjB,6BACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;oDACjC;oDAAa;;+DAG/B;;;;;;;;;;;kDAIN,8OAAC,4IAAA,CAAA,sBAAmB;kDACjB,UAAU,GAAG,CAAC,CAAC,kBACd,8OAAC,4IAAA,CAAA,mBAAgB;gDAAc,SAAS,IAAM,oBAAoB,EAAE,IAAI;gDAAG,UAAU,CAAC,CAAC;;kEACrF,8OAAC,EAAE,IAAI;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAM,EAAE,IAAI;;;;;;;+CAFQ,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;oBASpC,MAAM,MAAM,GAAG,kBACd,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,+IAAA,CAAA,WAAQ;gCAEP,MAAM;gCACN,cAAc;gCACd,eAAe;+BAHV,KAAK,EAAE;;;;;;;;;6CAQlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 2828, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/layout/unified-brand-layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { UnifiedBrandProvider, useUnifiedBrand, useBrandChangeListener } from '@/contexts/unified-brand-context';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\ninterface UnifiedBrandLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Inner component that uses the unified brand context\r\nfunction UnifiedBrandLayoutContent({ children }: UnifiedBrandLayoutProps) {\r\n  const { currentBrand, loading, error } = useUnifiedBrand();\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Listen for brand changes and log them\r\n  useBrandChangeListener((brand) => {\r\n    console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');\r\n    \r\n    // Mark as initialized once we have a brand or finished loading\r\n    if (!isInitialized && (!loading || brand)) {\r\n      setIsInitialized(true);\r\n    }\r\n  });\r\n\r\n  // Show loading state while initializing\r\n  if (!isInitialized && loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading brand profiles...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an error\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n            <span className=\"text-red-600 text-2xl\">⚠️</span>\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-red-900 mb-2\">Error Loading Brands</h2>\r\n          <p className=\"text-red-600 mb-4\">{error}</p>\r\n          <button \r\n            onClick={() => window.location.reload()} \r\n            className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"unified-brand-layout\">\r\n      {/* Debug info in development */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div className=\"fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl\">\r\n          <div>🔥 Unified Brand System</div>\r\n          <div>Brand: {currentBrand?.businessName || currentBrand?.name || 'None'}</div>\r\n          <div>ID: {currentBrand?.id || 'None'}</div>\r\n        </div>\r\n      )}\r\n      \r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Main layout component that provides the unified brand context\r\nexport function UnifiedBrandLayout({ children }: UnifiedBrandLayoutProps) {\r\n  return (\r\n    <UnifiedBrandProvider>\r\n      <UnifiedBrandLayoutContent>\r\n        {children}\r\n      </UnifiedBrandLayoutContent>\r\n    </UnifiedBrandProvider>\r\n  );\r\n}\r\n\r\n// Hook to make any component brand-aware\r\nexport function useBrandAware() {\r\n  const { currentBrand, selectBrand, loading } = useUnifiedBrand();\r\n  \r\n  return {\r\n    currentBrand,\r\n    selectBrand,\r\n    loading,\r\n    isReady: !loading && currentBrand !== null,\r\n    brandId: currentBrand?.id || null,\r\n    brandName: currentBrand?.businessName || currentBrand?.name || null,\r\n  };\r\n}\r\n\r\n// Higher-order component to make any component brand-aware\r\nexport function withBrandAware<P extends object>(\r\n  Component: React.ComponentType<P & { brand: CompleteBrandProfile | null }>\r\n) {\r\n  return function BrandAwareComponent(props: P) {\r\n    const { currentBrand } = useUnifiedBrand();\r\n    \r\n    return <Component {...props} brand={currentBrand} />;\r\n  };\r\n}\r\n\r\n// Component to show brand-specific content\r\ninterface BrandContentProps {\r\n  children: (brand: CompleteBrandProfile) => React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function BrandContent({ children, fallback }: BrandContentProps) {\r\n  const { currentBrand, loading } = useUnifiedBrand();\r\n  \r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  if (!currentBrand) {\r\n    return fallback || (\r\n      <div className=\"text-center p-8\">\r\n        <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <span className=\"text-gray-400 text-2xl\">🏢</span>\r\n        </div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Brand Selected</h3>\r\n        <p className=\"text-gray-600\">Please select a brand to continue.</p>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  return <>{children(currentBrand)}</>;\r\n}\r\n\r\n// Component to conditionally render content based on brand\r\ninterface ConditionalBrandContentProps {\r\n  brandId?: string;\r\n  brandName?: string;\r\n  children: React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function ConditionalBrandContent({ \r\n  brandId, \r\n  brandName, \r\n  children, \r\n  fallback \r\n}: ConditionalBrandContentProps) {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  \r\n  const shouldRender = \r\n    (!brandId || currentBrand?.id === brandId) &&\r\n    (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);\r\n  \r\n  if (shouldRender) {\r\n    return <>{children}</>;\r\n  }\r\n  \r\n  return fallback || null;\r\n}\r\n\r\n// Hook to get brand-scoped data with automatic updates\r\nexport function useBrandScopedData<T>(\r\n  feature: string,\r\n  defaultValue: T,\r\n  loader?: (brandId: string) => T | Promise<T>\r\n): [T, (data: T) => void, boolean] {\r\n  const { currentBrand, getBrandStorage } = useUnifiedBrand();\r\n  const [data, setData] = useState<T>(defaultValue);\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // Load data when brand changes\r\n  useEffect(() => {\r\n    if (!currentBrand?.id) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    const storage = getBrandStorage(feature);\r\n    if (!storage) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    \r\n    try {\r\n      if (loader) {\r\n        // Use custom loader\r\n        const result = loader(currentBrand.id);\r\n        if (result instanceof Promise) {\r\n          result.then(loadedData => {\r\n            setData(loadedData);\r\n            setLoading(false);\r\n          }).catch(error => {\r\n            console.error(`Failed to load ${feature} data:`, error);\r\n            setData(defaultValue);\r\n            setLoading(false);\r\n          });\r\n        } else {\r\n          setData(result);\r\n          setLoading(false);\r\n        }\r\n      } else {\r\n        // Use storage\r\n        const storedData = storage.getItem<T>();\r\n        setData(storedData || defaultValue);\r\n        setLoading(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to load ${feature} data:`, error);\r\n      setData(defaultValue);\r\n      setLoading(false);\r\n    }\r\n  }, [currentBrand?.id, feature, defaultValue, loader, getBrandStorage]);\r\n  \r\n  // Save data function\r\n  const saveData = (newData: T) => {\r\n    setData(newData);\r\n    \r\n    if (currentBrand?.id) {\r\n      const storage = getBrandStorage(feature);\r\n      if (storage) {\r\n        storage.setItem(newData);\r\n      }\r\n    }\r\n  };\r\n  \r\n  return [data, saveData, loading];\r\n}\r\n\r\n// Component to display brand switching status\r\nexport function BrandSwitchingStatus() {\r\n  const { loading, currentBrand } = useUnifiedBrand();\r\n  const [switching, setSwitching] = useState(false);\r\n  \r\n  useBrandChangeListener((brand) => {\r\n    setSwitching(true);\r\n    const timer = setTimeout(() => setSwitching(false), 1000);\r\n    return () => clearTimeout(timer);\r\n  });\r\n  \r\n  if (!switching && !loading) return null;\r\n  \r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n        <span className=\"text-sm\">\r\n          {switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAHA;;;;AAUA,sDAAsD;AACtD,SAAS,0BAA0B,EAAE,QAAQ,EAA2B;IACtE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,CAAA,GAAA,+IAAA,CAAA,yBAAsB,AAAD,EAAE,CAAC;QACtB,QAAQ,GAAG,CAAC,+BAA+B,OAAO,gBAAgB,OAAO,QAAQ;QAEjF,+DAA+D;QAC/D,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,KAAK,GAAG;YACzC,iBAAiB;QACnB;IACF;IAEA,wCAAwC;IACxC,IAAI,CAAC,iBAAiB,SAAS;QAC7B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,uCAAuC;IACvC,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;kCAE1C,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,oDAAyB,+BACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAI;;;;;;kCACL,8OAAC;;4BAAI;4BAAQ,cAAc,gBAAgB,cAAc,QAAQ;;;;;;;kCACjE,8OAAC;;4BAAI;4BAAK,cAAc,MAAM;;;;;;;;;;;;;YAIjC;;;;;;;AAGP;AAGO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,qBACE,8OAAC,+IAAA,CAAA,uBAAoB;kBACnB,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT;AAGO,SAAS;IACd,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,WAAW,iBAAiB;QACtC,SAAS,cAAc,MAAM;QAC7B,WAAW,cAAc,gBAAgB,cAAc,QAAQ;IACjE;AACF;AAGO,SAAS,eACd,SAA0E;IAE1E,OAAO,SAAS,oBAAoB,KAAQ;QAC1C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;QAEvC,qBAAO,8OAAC;YAAW,GAAG,KAAK;YAAE,OAAO;;;;;;IACtC;AACF;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAqB;IACpE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAEhD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,0BACL,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAyB;;;;;;;;;;;8BAE3C,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBAAO;kBAAG,SAAS;;AACrB;AAUO,SAAS,wBAAwB,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACqB;IAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAEvC,MAAM,eACJ,CAAC,CAAC,WAAW,cAAc,OAAO,OAAO,KACzC,CAAC,CAAC,aAAa,cAAc,iBAAiB,aAAa,cAAc,SAAS,SAAS;IAE7F,IAAI,cAAc;QAChB,qBAAO;sBAAG;;IACZ;IAEA,OAAO,YAAY;AACrB;AAGO,SAAS,mBACd,OAAe,EACf,YAAe,EACf,MAA4C;IAE5C,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc,IAAI;YACrB,QAAQ;YACR;QACF;QAEA,MAAM,UAAU,gBAAgB;QAChC,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR;QACF;QAEA,WAAW;QAEX,IAAI;YACF,IAAI,QAAQ;gBACV,oBAAoB;gBACpB,MAAM,SAAS,OAAO,aAAa,EAAE;gBACrC,IAAI,kBAAkB,SAAS;oBAC7B,OAAO,IAAI,CAAC,CAAA;wBACV,QAAQ;wBACR,WAAW;oBACb,GAAG,KAAK,CAAC,CAAA;wBACP,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,EAAE;wBACjD,QAAQ;wBACR,WAAW;oBACb;gBACF,OAAO;oBACL,QAAQ;oBACR,WAAW;gBACb;YACF,OAAO;gBACL,cAAc;gBACd,MAAM,aAAa,QAAQ,OAAO;gBAClC,QAAQ,cAAc;gBACtB,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,EAAE;YACjD,QAAQ;YACR,WAAW;QACb;IACF,GAAG;QAAC,cAAc;QAAI;QAAS;QAAc;QAAQ;KAAgB;IAErE,qBAAqB;IACrB,MAAM,WAAW,CAAC;QAChB,QAAQ;QAER,IAAI,cAAc,IAAI;YACpB,MAAM,UAAU,gBAAgB;YAChC,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC;YAClB;QACF;IACF;IAEA,OAAO;QAAC;QAAM;QAAU;KAAQ;AAClC;AAGO,SAAS;IACd,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,+IAAA,CAAA,yBAAsB,AAAD,EAAE,CAAC;QACtB,aAAa;QACb,MAAM,QAAQ,WAAW,IAAM,aAAa,QAAQ;QACpD,OAAO,IAAM,aAAa;IAC5B;IAEA,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BACb,YAAY,CAAC,aAAa,EAAE,cAAc,gBAAgB,cAAc,KAAK,GAAG,CAAC,GAAG;;;;;;;;;;;;;;;;;AAK/F", "debugId": null}}, {"offset": {"line": 3223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/utils/enable-firebase-storage.ts"], "sourcesContent": ["/**\n * Enable Firebase Storage Utility\n * Helper to re-enable Firebase Storage after rules are deployed\n */\n\nexport const FIREBASE_STORAGE_INSTRUCTIONS = `\n🔥 FIREBASE STORAGE RULES DEPLOYMENT INSTRUCTIONS\n\n1. Go to Firebase Console: https://console.firebase.google.com/\n2. Select your project: localbuzz-mpkuv\n3. Go to Storage → Rules\n4. Replace the current rules with:\n\nrules_version = '2';\nservice firebase.storage {\n  match /b/{bucket}/o {\n    // Users can upload and manage their own generated content\n    match /generated-content/{userId}/{allPaths=**} {\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n    }\n    \n    // Users can upload and manage their own artifacts\n    match /artifacts/{userId}/{allPaths=**} {\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n    }\n    \n    // Users can upload and manage their own brand assets\n    match /brand-assets/{userId}/{allPaths=**} {\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n    }\n    \n    // Temporary uploads (for processing)\n    match /temp/{userId}/{allPaths=**} {\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n    }\n  }\n}\n\n5. Click \"Publish\"\n6. Wait 2-3 minutes for rules to propagate\n7. Come back and run: enableFirebaseStorage()\n`;\n\nexport const CODE_TO_UNCOMMENT = `\nAfter deploying Firebase Storage rules, go to:\nsrc/app/quick-content/page.tsx\n\nFind this section around line 209:\n// TEMPORARY: Skip Firebase Storage upload until rules are deployed\n\nReplace the entire processPostImages function with the commented code below it.\n\nOr simply run: enableFirebaseStorage() in the browser console.\n`;\n\n/**\n * Enable Firebase Storage by updating the code\n */\nexport function enableFirebaseStorage() {\n  console.log('🔥 Firebase Storage Enable Instructions:');\n  console.log(FIREBASE_STORAGE_INSTRUCTIONS);\n  console.log('📝 Code Update Instructions:');\n  console.log(CODE_TO_UNCOMMENT);\n  \n  return {\n    instructions: FIREBASE_STORAGE_INSTRUCTIONS,\n    codeInstructions: CODE_TO_UNCOMMENT,\n    status: 'Instructions displayed - manual code update required'\n  };\n}\n\n/**\n * Check if Firebase Storage rules are working\n */\nexport async function testFirebaseStorageRules() {\n  try {\n    // This would need to be implemented with actual Firebase Storage test\n    console.log('🧪 Testing Firebase Storage rules...');\n    console.log('⚠️ Manual test required - try generating content after deploying rules');\n    \n    return {\n      success: false,\n      message: 'Manual test required - generate content to test'\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n// Make functions available globally for easy access\nif (typeof window !== 'undefined') {\n  (window as any).enableFirebaseStorage = enableFirebaseStorage;\n  (window as any).testFirebaseStorageRules = testFirebaseStorageRules;\n  \n  // Auto-display instructions on load\n  console.log('🔥 Firebase Storage is currently disabled.');\n  console.log('📋 To enable permanent image storage, run: enableFirebaseStorage()');\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAEM,MAAM,gCAAgC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoC9C,CAAC;AAEM,MAAM,oBAAoB,CAAC;;;;;;;;;;AAUlC,CAAC;AAKM,SAAS;IACd,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,OAAO;QACL,cAAc;QACd,kBAAkB;QAClB,QAAQ;IACV;AACF;AAKO,eAAe;IACpB,IAAI;QACF,sEAAsE;QACtE,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA,oDAAoD;AACpD,uCAAmC;;AAOnC", "debugId": null}}, {"offset": {"line": 3317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/quick-content/page.tsx"], "sourcesContent": ["// src/app/content-calendar/page.tsx\r\n\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { SidebarInset, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { ContentCalendar } from \"@/components/dashboard/content-calendar\";\r\n// TODO: Re-enable once ActiveArtifactsIndicator is properly set up\r\n// import { ActiveArtifactsIndicator } from \"@/components/artifacts/active-artifacts-indicator\";\r\nimport type { BrandProfile, GeneratedPost } from \"@/lib/types\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { User, PanelLeftClose, PanelLeftOpen } from \"lucide-react\";\r\nimport { useUnifiedBrand, useBrandStorage, useBrandChangeListener } from \"@/contexts/unified-brand-context\";\r\nimport { UnifiedBrandLayout, BrandContent, BrandSwitchingStatus } from \"@/components/layout/unified-brand-layout\";\r\nimport { STORAGE_FEATURES, getStorageUsage, cleanupAllStorage } from \"@/lib/services/brand-scoped-storage\";\r\nimport { processGeneratedPost } from \"@/lib/services/generated-post-storage\";\r\nimport { useFirebaseAuth } from \"@/hooks/use-firebase-auth\";\r\nimport { useGeneratedPosts } from \"@/hooks/use-generated-posts\";\r\nimport \"@/lib/utils/enable-firebase-storage\"; // Load Firebase Storage utilities\r\n\r\n// No limit on posts - store all generated content\r\n\r\n// Brand-scoped storage cleanup utility\r\nconst cleanupBrandScopedStorage = (brandStorage: any) => {\r\n  try {\r\n    const posts = brandStorage.getItem() || [];\r\n\r\n    // Fix invalid dates in existing posts\r\n    const fixedPosts = posts.map((post: GeneratedPost) => {\r\n      if (!post.date || isNaN(new Date(post.date).getTime())) {\r\n        return {\r\n          ...post,\r\n          date: new Date().toISOString()\r\n        };\r\n      }\r\n      return post;\r\n    });\r\n\r\n    if (fixedPosts.length > 5) {\r\n      // Keep only the 5 most recent posts\r\n      const recentPosts = fixedPosts.slice(0, 5);\r\n      brandStorage.setItem(recentPosts);\r\n      return recentPosts;\r\n    } else {\r\n      // Save the fixed posts back\r\n      brandStorage.setItem(fixedPosts);\r\n      return fixedPosts;\r\n    }\r\n  } catch (error) {\r\n    console.warn('Brand-scoped storage cleanup failed:', error);\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction QuickContentPage() {\r\n  const { currentBrand, brands, loading: brandLoading, selectBrand } = useUnifiedBrand();\r\n  const postsStorage = useBrandStorage(STORAGE_FEATURES.QUICK_CONTENT);\r\n  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n  const { open: sidebarOpen, toggleSidebar } = useSidebar();\r\n  const { user } = useFirebaseAuth();\r\n  const { savePost, saving } = useGeneratedPosts();\r\n\r\n  // Inline brand restoration function\r\n  const forceBrandRestore = React.useCallback(() => {\r\n    try {\r\n      // Try to restore from full brand data first\r\n      const savedBrandData = localStorage.getItem('currentBrandData');\r\n      if (savedBrandData) {\r\n        const parsedData = JSON.parse(savedBrandData);\r\n        console.log('🔄 Attempting to restore brand from full data:', parsedData.businessName || parsedData.name);\r\n\r\n        // Find matching brand in current brands list\r\n        const matchingBrand = brands.find(b => b.id === parsedData.id);\r\n        if (matchingBrand) {\r\n          console.log('✅ Found matching brand in brands list, using fresh data');\r\n          selectBrand(matchingBrand);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // Fallback to brand ID restoration\r\n      const savedBrandId = localStorage.getItem('selectedBrandId');\r\n      if (savedBrandId && brands.length > 0) {\r\n        const savedBrand = brands.find(b => b.id === savedBrandId);\r\n        if (savedBrand) {\r\n          console.log('🔄 Restored brand from ID:', savedBrand.businessName || savedBrand.name);\r\n          selectBrand(savedBrand);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Failed to restore brand from storage:', error);\r\n      return false;\r\n    }\r\n  }, [brands, selectBrand]);\r\n\r\n  // Load posts when brand changes using unified brand system\r\n  useBrandChangeListener(React.useCallback((brand) => {\r\n    const brandName = brand?.businessName || brand?.name || 'none';\r\n    console.log('🔄 Quick Content: brand changed to:', brandName);\r\n\r\n    if (!brand) {\r\n      setGeneratedPosts([]);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      if (postsStorage) {\r\n        const posts = postsStorage.getItem<GeneratedPost[]>() || [];\r\n\r\n        // Check if any posts have invalid dates\r\n        const hasInvalidDates = posts.some((post: GeneratedPost) =>\r\n          !post.date || isNaN(new Date(post.date).getTime())\r\n        );\r\n\r\n        if (hasInvalidDates) {\r\n          console.warn('Found posts with invalid dates, clearing brand storage...');\r\n          postsStorage.removeItem();\r\n          setGeneratedPosts([]);\r\n        } else {\r\n          setGeneratedPosts(posts);\r\n        }\r\n\r\n        console.log(`✅ Loaded ${posts.length} posts for brand ${brandName}`);\r\n      } else {\r\n        setGeneratedPosts([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load posts for brand:', brandName, error);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Failed to load data\",\r\n        description: \"Could not read your posts data. It might be corrupted.\",\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [postsStorage, toast]));\r\n\r\n  // Enhanced brand selection logic with persistence recovery\r\n  useEffect(() => {\r\n    console.log('🔍 Enhanced brand selection check:', {\r\n      brandLoading,\r\n      brandsCount: brands.length,\r\n      currentBrand: currentBrand?.businessName || currentBrand?.name || 'null',\r\n      postsStorageAvailable: !!postsStorage\r\n    });\r\n\r\n    if (!brandLoading) {\r\n      // Add a small delay to ensure brands have time to load\r\n      const timer = setTimeout(() => {\r\n        if (brands.length === 0) {\r\n          // No brands exist, redirect to brand setup\r\n          console.log('🔄 Quick Content: No brands found, redirecting to brand setup');\r\n          try { router.prefetch('/brand-profile'); } catch { }\r\n          router.push('/brand-profile');\r\n        } else if (brands.length > 0 && !currentBrand) {\r\n          // Try to restore from persistence first\r\n          console.log('🔧 Attempting brand restoration from persistence...');\r\n          const restored = forceBrandRestore();\r\n\r\n          if (!restored) {\r\n            // If restoration failed, auto-select the first brand\r\n            console.log('🎯 Auto-selecting first available brand:', brands[0].businessName || brands[0].name);\r\n            selectBrand(brands[0]);\r\n          }\r\n        }\r\n      }, 1000); // 1 second delay\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [currentBrand, brands.length, brandLoading, router, selectBrand, forceBrandRestore]);\r\n\r\n\r\n  // Process generated post with Firebase Storage upload and database fallback\r\n  const processPostImages = async (post: GeneratedPost): Promise<GeneratedPost> => {\r\n    try {\r\n      // Check if user is authenticated for Firebase Storage\r\n      if (!user) {\r\n        console.log('⚠️ User not authenticated, saving to database only');\r\n        toast({\r\n          title: \"Content Saved\",\r\n          description: \"Content saved to database. Sign in to save images permanently in the cloud.\",\r\n          variant: \"default\",\r\n        });\r\n        return post; // Return original post with data URLs\r\n      }\r\n\r\n      console.log('🔄 Processing post images for permanent storage...');\r\n\r\n      // TEMPORARY: Skip Firebase Storage upload until rules are deployed\r\n      console.log('⚠️ Skipping Firebase Storage upload - rules not deployed yet');\r\n\r\n      // Save to database with data URLs (temporary solution)\r\n      toast({\r\n        title: \"Content Saved to Database\",\r\n        description: \"Content saved successfully. Deploy Firebase Storage rules for permanent image URLs.\",\r\n        variant: \"default\",\r\n      });\r\n\r\n      return post; // Return original post with data URLs\r\n\r\n      /* UNCOMMENT THIS AFTER DEPLOYING FIREBASE STORAGE RULES:\r\n      try {\r\n        // Try Firebase Storage first\r\n        const processedPost = await processGeneratedPost(post, user.uid);\r\n\r\n        console.log('✅ Post images processed successfully');\r\n\r\n        // Show success message\r\n        toast({\r\n          title: \"Images Saved to Cloud\",\r\n          description: \"Images have been permanently saved to Firebase Storage.\",\r\n          variant: \"default\",\r\n        });\r\n\r\n        return processedPost;\r\n      } catch (storageError) {\r\n        console.warn('⚠️ Firebase Storage failed, falling back to database storage:', storageError);\r\n\r\n        // Fallback: Save to database with data URLs (temporary)\r\n        toast({\r\n          title: \"Content Saved to Database\",\r\n          description: \"Images stored temporarily. Please update Firebase Storage rules for permanent cloud storage.\",\r\n          variant: \"default\",\r\n        });\r\n\r\n        return post; // Return original post with data URLs\r\n      }\r\n      */\r\n    } catch (error) {\r\n      console.warn('⚠️ Failed to process post, using original post:', error);\r\n      toast({\r\n        title: \"Content Saved Locally\",\r\n        description: \"Content generated successfully but stored locally only.\",\r\n        variant: \"default\",\r\n      });\r\n      return post; // Return original post if all processing fails\r\n    }\r\n  };\r\n\r\n  const handlePostGenerated = async (post: GeneratedPost) => {\r\n    console.log('📝 Processing generated post...');\r\n\r\n    // Process images with Firebase Storage upload\r\n    let processedPost = await processPostImages(post);\r\n\r\n    // Add the processed post to the beginning of the array (no limit)\r\n    const newPosts = [processedPost, ...generatedPosts];\r\n    setGeneratedPosts(newPosts);\r\n\r\n    if (!postsStorage) {\r\n      console.warn('No posts storage available for current brand - keeping in memory only');\r\n      toast({\r\n        title: \"Storage Unavailable\",\r\n        description: \"Post generated but couldn't be saved. Please select a brand.\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Save to localStorage first (immediate)\r\n      postsStorage.setItem(newPosts);\r\n      console.log(`💾 Saved ${newPosts.length} posts to localStorage for brand ${currentBrand?.businessName || currentBrand?.name}`);\r\n\r\n      // Also save to Firestore database (permanent backup)\r\n      if (user) {\r\n        try {\r\n          console.log('💾 Saving post to Firestore database...');\r\n          const postId = await savePost(processedPost);\r\n          console.log('✅ Post saved to Firestore with ID:', postId);\r\n\r\n          // Update the post with the Firestore ID\r\n          const savedPost = { ...processedPost, id: postId };\r\n          const updatedPosts = [savedPost, ...generatedPosts];\r\n          setGeneratedPosts(updatedPosts);\r\n          postsStorage.setItem(updatedPosts);\r\n\r\n          toast({\r\n            title: \"Content Saved Successfully\",\r\n            description: \"Your content has been saved to both local storage and the database.\",\r\n            variant: \"default\",\r\n          });\r\n        } catch (firestoreError) {\r\n          console.error('❌ Failed to save to Firestore, but localStorage succeeded:', firestoreError);\r\n          toast({\r\n            title: \"Content Saved Locally\",\r\n            description: \"Content saved locally. Database save failed but content is secure.\",\r\n            variant: \"default\",\r\n          });\r\n        }\r\n      } else {\r\n        toast({\r\n          title: \"Content Saved Locally\",\r\n          description: \"Content saved locally. Sign in to save to database permanently.\",\r\n          variant: \"default\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('Storage error in handlePostGenerated:', error);\r\n\r\n      // Show user-friendly error message\r\n      toast({\r\n        title: \"Storage Issue\",\r\n        description: \"Post generated successfully but couldn't be saved. Storage may be full.\",\r\n        variant: \"destructive\",\r\n      });\r\n\r\n      // Keep the post in memory even if storage fails\r\n      console.log('Post kept in memory despite storage failure');\r\n    }\r\n  };\r\n\r\n  // Debug function to clear all posts for current brand\r\n  const clearAllPosts = () => {\r\n    if (!postsStorage) {\r\n      console.warn('No posts storage available for current brand');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      postsStorage.removeItem();\r\n      setGeneratedPosts([]);\r\n      toast({\r\n        title: \"Posts Cleared\",\r\n        description: `All stored posts have been cleared for ${currentBrand?.businessName || currentBrand?.name}.`,\r\n      });\r\n      console.log(`🗑️ Cleared all posts for brand ${currentBrand?.businessName || currentBrand?.name}`);\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Clear Failed\",\r\n        description: \"Could not clear stored posts.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handlePostUpdated = async (updatedPost: GeneratedPost) => {\r\n    if (!postsStorage) {\r\n      console.warn('No posts storage available for current brand');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const updatedPosts = generatedPosts.map((post) =>\r\n        post.id === updatedPost.id ? updatedPost : post\r\n      );\r\n      setGeneratedPosts(updatedPosts);\r\n\r\n      // Check storage size before saving\r\n      const postsData = JSON.stringify(updatedPosts);\r\n      const maxSize = 5 * 1024 * 1024; // 5MB limit\r\n\r\n      if (postsData.length > maxSize) {\r\n        // If too large, keep fewer posts\r\n        const reducedPosts = updatedPosts.slice(0, Math.max(1, Math.floor(MAX_POSTS_TO_STORE / 2)));\r\n        postsStorage.setItem(reducedPosts);\r\n        setGeneratedPosts(reducedPosts);\r\n\r\n        toast({\r\n          title: \"Storage Optimized\",\r\n          description: \"Reduced stored posts to prevent storage overflow. Some older posts were removed.\",\r\n        });\r\n      } else {\r\n        postsStorage.setItem(updatedPosts);\r\n      }\r\n\r\n      console.log(`💾 Updated post for brand ${currentBrand?.businessName || currentBrand?.name}`);\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Failed to update post\",\r\n        description: \"Unable to save post updates. Your browser storage may be full.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <SidebarInset key={currentBrand?.id || 'no-brand'} fullWidth>\r\n      <header className=\"flex h-14 items-center justify-between gap-4 border-b bg-card px-4 lg:h-[60px] lg:px-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={toggleSidebar}\r\n            className=\"h-8 w-8\"\r\n            title={sidebarOpen ? \"Hide sidebar for full-screen mode\" : \"Show sidebar\"}\r\n          >\r\n            {sidebarOpen ? (\r\n              <PanelLeftClose className=\"h-4 w-4\" />\r\n            ) : (\r\n              <PanelLeftOpen className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            {sidebarOpen ? \"Sidebar visible\" : \"Full-screen mode\"}\r\n          </span>\r\n        </div>\r\n\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"secondary\" size=\"icon\" className=\"rounded-full\">\r\n              <Avatar>\r\n                <AvatarImage src=\"https://placehold.co/40x40.png\" alt=\"User\" data-ai-hint=\"user avatar\" />\r\n                <AvatarFallback><User /></AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"sr-only\">Toggle user menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuLabel>My Account</DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem\r\n              onClick={() => {\r\n                if (postsStorage) {\r\n                  const cleaned = cleanupBrandScopedStorage(postsStorage);\r\n                  if (cleaned) {\r\n                    setGeneratedPosts(cleaned);\r\n                    toast({\r\n                      title: \"Storage Cleaned\",\r\n                      description: `Removed older posts for ${currentBrand?.businessName || currentBrand?.name}.`,\r\n                    });\r\n                  } else {\r\n                    toast({\r\n                      title: \"Storage Clean\",\r\n                      description: \"Storage is already optimized.\",\r\n                    });\r\n                  }\r\n                } else {\r\n                  toast({\r\n                    variant: \"destructive\",\r\n                    title: \"No Brand Selected\",\r\n                    description: \"Please select a brand first.\",\r\n                  });\r\n                }\r\n              }}\r\n            >\r\n              Clear Old Posts\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </header>\r\n      <main className=\"flex-1 overflow-auto\">\r\n        <div className=\"min-h-full bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              {isLoading || brandLoading ? (\r\n                <div className=\"flex w-full min-h-[300px] items-center justify-center\">\r\n                  <div className=\"w-full max-w-3xl text-center\">\r\n                    <p>Loading Quick Content...</p>\r\n                  </div>\r\n                </div>\r\n              ) : !currentBrand ? (\r\n                <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n                  <h2 className=\"text-xl font-semibold\">Select a Brand</h2>\r\n                  <p className=\"text-muted-foreground text-center\">\r\n                    Please select a brand to start generating content.\r\n                  </p>\r\n                  {brands.length > 0 ? (\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                      {brands.map((brand) => (\r\n                        <Button\r\n                          key={brand.id}\r\n                          onClick={() => selectBrand(brand)}\r\n                          variant=\"outline\"\r\n                        >\r\n                          {brand.businessName || brand.name}\r\n                        </Button>\r\n                      ))}\r\n                    </div>\r\n                  ) : (\r\n                    <Button onMouseEnter={() => router.prefetch('/brand-profile')} onFocus={() => router.prefetch('/brand-profile')} onClick={() => router.push('/brand-profile')}>\r\n                      Create Brand Profile\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {/* TODO: Re-enable Active Artifacts Indicator once component is set up */}\r\n                  {/* <ActiveArtifactsIndicator\r\n              onArtifactDeactivate={() => {\r\n                // Refresh content when artifacts are deactivated\r\n                console.log('Artifact deactivated, content generation will use updated active artifacts');\r\n              }}\r\n              onManageArtifacts={() => {\r\n                // Navigate to artifacts page\r\n                window.open('/artifacts', '_blank');\r\n              }}\r\n            /> */}\r\n\r\n                  {/* Content Calendar */}\r\n                  {/* Map unified CompleteBrandProfile to the simplified BrandProfile expected by ContentCalendar */}\r\n                  {currentBrand && (\r\n                    <ContentCalendar\r\n                      brandProfile={{\r\n                        businessName: currentBrand.businessName,\r\n                        businessType: currentBrand.businessType || '',\r\n                        location: currentBrand.location || '',\r\n                        logoDataUrl: currentBrand.logoDataUrl || '',\r\n                        visualStyle: currentBrand.visualStyle || '',\r\n                        writingTone: currentBrand.writingTone || '',\r\n                        contentThemes: currentBrand.contentThemes || '',\r\n                        websiteUrl: currentBrand.websiteUrl || '',\r\n                        description: currentBrand.description || '',\r\n                        // Convert services array to newline-separated string to match BrandProfile.services\r\n                        services: Array.isArray((currentBrand as any).services)\r\n                          ? (currentBrand as any).services.map((s: any) => s.name).join('\\n')\r\n                          : (currentBrand as any).services || '',\r\n                        targetAudience: currentBrand.targetAudience || '',\r\n                        keyFeatures: currentBrand.keyFeatures || '',\r\n                        competitiveAdvantages: currentBrand.competitiveAdvantages || '',\r\n                        contactInfo: {\r\n                          phone: currentBrand.contactPhone || '',\r\n                          email: currentBrand.contactEmail || '',\r\n                          address: currentBrand.contactAddress || '',\r\n                        },\r\n                        socialMedia: {\r\n                          facebook: currentBrand.facebookUrl || '',\r\n                          instagram: currentBrand.instagramUrl || '',\r\n                          twitter: currentBrand.twitterUrl || '',\r\n                          linkedin: currentBrand.linkedinUrl || '',\r\n                        },\r\n                        primaryColor: currentBrand.primaryColor || undefined,\r\n                        accentColor: currentBrand.accentColor || undefined,\r\n                        backgroundColor: currentBrand.backgroundColor || undefined,\r\n                        designExamples: currentBrand.designExamples || [],\r\n                      }}\r\n                      posts={generatedPosts}\r\n                      onPostGenerated={handlePostGenerated}\r\n                      onPostUpdated={handlePostUpdated}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </SidebarInset>\r\n  );\r\n}\r\n\r\nfunction QuickContentPageWithUnifiedBrand() {\r\n  return (\r\n    <UnifiedBrandLayout>\r\n      <QuickContentPage />\r\n      <BrandSwitchingStatus />\r\n    </UnifiedBrandLayout>\r\n  );\r\n}\r\n\r\nexport default QuickContentPageWithUnifiedBrand;\r\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AAGpC;AAEA;AAQA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA,iQAA8C,kCAAkC;AA5BhF;;;;;;;;;;;;;;;;;;AA8BA,kDAAkD;AAElD,uCAAuC;AACvC,MAAM,4BAA4B,CAAC;IACjC,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,MAAM,EAAE;QAE1C,sCAAsC;QACtC,MAAM,aAAa,MAAM,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK;gBACtD,OAAO;oBACL,GAAG,IAAI;oBACP,MAAM,IAAI,OAAO,WAAW;gBAC9B;YACF;YACA,OAAO;QACT;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,oCAAoC;YACpC,MAAM,cAAc,WAAW,KAAK,CAAC,GAAG;YACxC,aAAa,OAAO,CAAC;YACrB,OAAO;QACT,OAAO;YACL,4BAA4B;YAC5B,aAAa,OAAO,CAAC;YACrB,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,wCAAwC;IACvD;IACA,OAAO;AACT;AAEA,SAAS;IACP,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IACnF,MAAM,eAAe,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD,EAAE,oJAAA,CAAA,mBAAgB,CAAC,aAAa;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,MAAM,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,oBAAiB,AAAD;IAE7C,oCAAoC;IACpC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QAC1C,IAAI;YACF,4CAA4C;YAC5C,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,QAAQ,GAAG,CAAC,kDAAkD,WAAW,YAAY,IAAI,WAAW,IAAI;gBAExG,6CAA6C;gBAC7C,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;gBAC7D,IAAI,eAAe;oBACjB,QAAQ,GAAG,CAAC;oBACZ,YAAY;oBACZ,OAAO;gBACT;YACF;YAEA,mCAAmC;YACnC,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,gBAAgB,OAAO,MAAM,GAAG,GAAG;gBACrC,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC7C,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,8BAA8B,WAAW,YAAY,IAAI,WAAW,IAAI;oBACpF,YAAY;oBACZ,OAAO;gBACT;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,2DAA2D;IAC3D,CAAA,GAAA,+IAAA,CAAA,yBAAsB,AAAD,EAAE,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QACxC,MAAM,YAAY,OAAO,gBAAgB,OAAO,QAAQ;QACxD,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,IAAI,CAAC,OAAO;YACV,kBAAkB,EAAE;YACpB,aAAa;YACb;QACF;QAEA,aAAa;QAEb,IAAI;YACF,IAAI,cAAc;gBAChB,MAAM,QAAQ,aAAa,OAAO,MAAuB,EAAE;gBAE3D,wCAAwC;gBACxC,MAAM,kBAAkB,MAAM,IAAI,CAAC,CAAC,OAClC,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO;gBAGjD,IAAI,iBAAiB;oBACnB,QAAQ,IAAI,CAAC;oBACb,aAAa,UAAU;oBACvB,kBAAkB,EAAE;gBACtB,OAAO;oBACL,kBAAkB;gBACpB;gBAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,iBAAiB,EAAE,WAAW;YACrE,OAAO;gBACL,kBAAkB,EAAE;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC,WAAW;YAC5D,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAc;KAAM;IAExB,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,sCAAsC;YAChD;YACA,aAAa,OAAO,MAAM;YAC1B,cAAc,cAAc,gBAAgB,cAAc,QAAQ;YAClE,uBAAuB,CAAC,CAAC;QAC3B;QAEA,IAAI,CAAC,cAAc;YACjB,uDAAuD;YACvD,MAAM,QAAQ,WAAW;gBACvB,IAAI,OAAO,MAAM,KAAK,GAAG;oBACvB,2CAA2C;oBAC3C,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBAAE,OAAO,QAAQ,CAAC;oBAAmB,EAAE,OAAM,CAAE;oBACnD,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,OAAO,MAAM,GAAG,KAAK,CAAC,cAAc;oBAC7C,wCAAwC;oBACxC,QAAQ,GAAG,CAAC;oBACZ,MAAM,WAAW;oBAEjB,IAAI,CAAC,UAAU;wBACb,qDAAqD;wBACrD,QAAQ,GAAG,CAAC,4CAA4C,MAAM,CAAC,EAAE,CAAC,YAAY,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI;wBAChG,YAAY,MAAM,CAAC,EAAE;oBACvB;gBACF;YACF,GAAG,OAAO,iBAAiB;YAE3B,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAc,OAAO,MAAM;QAAE;QAAc;QAAQ;QAAa;KAAkB;IAGtF,4EAA4E;IAC5E,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,sDAAsD;YACtD,IAAI,CAAC,MAAM;gBACT,QAAQ,GAAG,CAAC;gBACZ,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA,OAAO,MAAM,sCAAsC;YACrD;YAEA,QAAQ,GAAG,CAAC;YAEZ,mEAAmE;YACnE,QAAQ,GAAG,CAAC;YAEZ,uDAAuD;YACvD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YAEA,OAAO,MAAM,sCAAsC;QAEnD;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2BA,GACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,mDAAmD;YAChE,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA,OAAO,MAAM,+CAA+C;QAC9D;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,QAAQ,GAAG,CAAC;QAEZ,8CAA8C;QAC9C,IAAI,gBAAgB,MAAM,kBAAkB;QAE5C,kEAAkE;QAClE,MAAM,WAAW;YAAC;eAAkB;SAAe;QACnD,kBAAkB;QAElB,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,yCAAyC;YACzC,aAAa,OAAO,CAAC;YACrB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,iCAAiC,EAAE,cAAc,gBAAgB,cAAc,MAAM;YAE7H,qDAAqD;YACrD,IAAI,MAAM;gBACR,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,MAAM,SAAS,MAAM,SAAS;oBAC9B,QAAQ,GAAG,CAAC,sCAAsC;oBAElD,wCAAwC;oBACxC,MAAM,YAAY;wBAAE,GAAG,aAAa;wBAAE,IAAI;oBAAO;oBACjD,MAAM,eAAe;wBAAC;2BAAc;qBAAe;oBACnD,kBAAkB;oBAClB,aAAa,OAAO,CAAC;oBAErB,MAAM;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;oBACX;gBACF,EAAE,OAAO,gBAAgB;oBACvB,QAAQ,KAAK,CAAC,8DAA8D;oBAC5E,MAAM;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YAEvD,mCAAmC;YACnC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YAEA,gDAAgD;YAChD,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,sDAAsD;IACtD,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,aAAa,UAAU;YACvB,kBAAkB,EAAE;YACpB,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,uCAAuC,EAAE,cAAc,gBAAgB,cAAc,KAAK,CAAC,CAAC;YAC5G;YACA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,cAAc,gBAAgB,cAAc,MAAM;QACnG,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,MAAM,eAAe,eAAe,GAAG,CAAC,CAAC,OACvC,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;YAE7C,kBAAkB;YAElB,mCAAmC;YACnC,MAAM,YAAY,KAAK,SAAS,CAAC;YACjC,MAAM,UAAU,IAAI,OAAO,MAAM,YAAY;YAE7C,IAAI,UAAU,MAAM,GAAG,SAAS;gBAC9B,iCAAiC;gBACjC,MAAM,eAAe,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,qBAAqB;gBACvF,aAAa,OAAO,CAAC;gBACrB,kBAAkB;gBAElB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,aAAa,OAAO,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,cAAc,gBAAgB,cAAc,MAAM;QAC7F,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,eAAY;QAAsC,SAAS;;0BAC1D,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO,cAAc,sCAAsC;0CAE1D,4BACC,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;yDAE1B,8OAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG7B,8OAAC;gCAAK,WAAU;0CACb,cAAc,oBAAoB;;;;;;;;;;;;kCAIvC,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAO,WAAU;;sDAChD,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAiC,KAAI;oDAAO,gBAAa;;;;;;8DAC1E,8OAAC,kIAAA,CAAA,iBAAc;8DAAC,cAAA,8OAAC,kMAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sDAEvB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAM;;kDACzB,8OAAC,4IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;4CACP,IAAI,cAAc;gDAChB,MAAM,UAAU,0BAA0B;gDAC1C,IAAI,SAAS;oDACX,kBAAkB;oDAClB,MAAM;wDACJ,OAAO;wDACP,aAAa,CAAC,wBAAwB,EAAE,cAAc,gBAAgB,cAAc,KAAK,CAAC,CAAC;oDAC7F;gDACF,OAAO;oDACL,MAAM;wDACJ,OAAO;wDACP,aAAa;oDACf;gDACF;4CACF,OAAO;gDACL,MAAM;oDACJ,SAAS;oDACT,OAAO;oDACP,aAAa;gDACf;4CACF;wCACF;kDACD;;;;;;;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,aAAa,6BACZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;uCAGL,CAAC,6BACH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;oCAGhD,OAAO,MAAM,GAAG,kBACf,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,IAAM,YAAY;gDAC3B,SAAQ;0DAEP,MAAM,YAAY,IAAI,MAAM,IAAI;+CAJ5B,MAAM,EAAE;;;;;;;;;6DASnB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,cAAc,IAAM,OAAO,QAAQ,CAAC;wCAAmB,SAAS,IAAM,OAAO,QAAQ,CAAC;wCAAmB,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAmB;;;;;;;;;;;qDAMnK,8OAAC;gCAAI,WAAU;0CAeZ,8BACC,8OAAC,sJAAA,CAAA,kBAAe;oCACd,cAAc;wCACZ,cAAc,aAAa,YAAY;wCACvC,cAAc,aAAa,YAAY,IAAI;wCAC3C,UAAU,aAAa,QAAQ,IAAI;wCACnC,aAAa,aAAa,WAAW,IAAI;wCACzC,aAAa,aAAa,WAAW,IAAI;wCACzC,aAAa,aAAa,WAAW,IAAI;wCACzC,eAAe,aAAa,aAAa,IAAI;wCAC7C,YAAY,aAAa,UAAU,IAAI;wCACvC,aAAa,aAAa,WAAW,IAAI;wCACzC,oFAAoF;wCACpF,UAAU,MAAM,OAAO,CAAC,AAAC,aAAqB,QAAQ,IAClD,AAAC,aAAqB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,IAAI,EAAE,IAAI,CAAC,QAC5D,AAAC,aAAqB,QAAQ,IAAI;wCACtC,gBAAgB,aAAa,cAAc,IAAI;wCAC/C,aAAa,aAAa,WAAW,IAAI;wCACzC,uBAAuB,aAAa,qBAAqB,IAAI;wCAC7D,aAAa;4CACX,OAAO,aAAa,YAAY,IAAI;4CACpC,OAAO,aAAa,YAAY,IAAI;4CACpC,SAAS,aAAa,cAAc,IAAI;wCAC1C;wCACA,aAAa;4CACX,UAAU,aAAa,WAAW,IAAI;4CACtC,WAAW,aAAa,YAAY,IAAI;4CACxC,SAAS,aAAa,UAAU,IAAI;4CACpC,UAAU,aAAa,WAAW,IAAI;wCACxC;wCACA,cAAc,aAAa,YAAY,IAAI;wCAC3C,aAAa,aAAa,WAAW,IAAI;wCACzC,iBAAiB,aAAa,eAAe,IAAI;wCACjD,gBAAgB,aAAa,cAAc,IAAI,EAAE;oCACnD;oCACA,OAAO;oCACP,iBAAiB;oCACjB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAvJd,cAAc,MAAM;;;;;AAkK3C;AAEA,SAAS;IACP,qBACE,8OAAC,0JAAA,CAAA,qBAAkB;;0BACjB,8OAAC;;;;;0BACD,8OAAC,0JAAA,CAAA,uBAAoB;;;;;;;;;;;AAG3B;uCAEe", "debugId": null}}]}