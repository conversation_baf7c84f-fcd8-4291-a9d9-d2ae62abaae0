module.exports = {

"[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Capabilities Configuration
 * Defines what each model version can do
 */ __turbopack_context__.s({
    "capabilityMatrix": (()=>capabilityMatrix),
    "featureAvailability": (()=>featureAvailability),
    "getCapabilityLevel": (()=>getCapabilityLevel),
    "getMaxQualityForPlatform": (()=>getMaxQualityForPlatform),
    "getModelsByFeature": (()=>getModelsByFeature),
    "getPlatformCapabilities": (()=>getPlatformCapabilities),
    "getSupportedAspectRatios": (()=>getSupportedAspectRatios),
    "hasCapability": (()=>hasCapability),
    "hasFeature": (()=>hasFeature),
    "modelCapabilities": (()=>modelCapabilities),
    "platformCapabilities": (()=>platformCapabilities)
});
const modelCapabilities = {
    'revo-1.0': {
        // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: false,
        aspectRatios: [
            '1:1'
        ],
        maxQuality: 9,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        perfectTextRendering: true,
        highResolution: true // NEW: 2048x2048 support
    },
    'revo-1.5': {
        // Enhanced model with advanced features
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16'
        ],
        maxQuality: 8,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true // Real-time context and trends
    },
    'revo-2.0': {
        // Premium Next-Gen AI model
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16',
            '4:3',
            '3:4'
        ],
        maxQuality: 10,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        characterConsistency: true,
        intelligentEditing: true,
        multimodalReasoning: true // NEW: Advanced visual context understanding
    }
};
const capabilityMatrix = {
    contentGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    designGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    videoGeneration: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'none'
    },
    artifactSupport: {
        'revo-1.0': 'none',
        'revo-1.5': 'full',
        'revo-2.0': 'premium'
    },
    brandConsistency: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'advanced',
        'revo-2.0': 'perfect'
    },
    characterConsistency: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    },
    intelligentEditing: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    }
};
const featureAvailability = {
    // Content features
    hashtagGeneration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    catchyWords: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    subheadlines: [
        'revo-1.5',
        'revo-2.0'
    ],
    callToAction: [
        'revo-1.5',
        'revo-2.0'
    ],
    contentVariants: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Design features
    logoIntegration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    brandColors: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    designExamples: [
        'revo-1.5',
        'revo-2.0'
    ],
    textOverlay: [
        'revo-1.5',
        'revo-2.0'
    ],
    multipleAspectRatios: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Advanced features
    realTimeContext: [
        'revo-1.5',
        'revo-2.0'
    ],
    trendingTopics: [
        'revo-1.5',
        'revo-2.0'
    ],
    marketIntelligence: [
        'revo-1.5',
        'revo-2.0'
    ],
    competitorAnalysis: [
        'revo-2.0'
    ],
    // Revo 2.0 exclusive features
    characterConsistency: [
        'revo-2.0'
    ],
    intelligentEditing: [
        'revo-2.0'
    ],
    inpainting: [
        'revo-2.0'
    ],
    outpainting: [
        'revo-2.0'
    ],
    multimodalReasoning: [
        'revo-2.0'
    ],
    // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)
    perfectTextRendering: [
        'revo-1.0',
        'revo-2.0'
    ],
    highResolution: [
        'revo-1.0',
        'revo-2.0'
    ],
    // Artifact features
    artifactReference: [
        'revo-1.5'
    ],
    exactUseArtifacts: [
        'revo-1.5'
    ],
    textOverlayArtifacts: [
        'revo-1.5'
    ]
};
const platformCapabilities = {
    Instagram: {
        'revo-1.0': {
            aspectRatios: [
                '1:1'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'hashtags'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '1:1',
                '9:16'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'hashtags',
                'stories',
                'reels-ready'
            ]
        }
    },
    Facebook: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'page-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'page-posts',
                'stories'
            ]
        }
    },
    Twitter: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'tweets'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'tweets',
                'threads'
            ]
        }
    },
    LinkedIn: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'professional-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'professional-posts',
                'articles'
            ]
        }
    }
};
function hasCapability(modelId, capability) {
    return modelCapabilities[modelId][capability];
}
function getCapabilityLevel(modelId, capability) {
    return capabilityMatrix[capability][modelId];
}
function hasFeature(modelId, feature) {
    return featureAvailability[feature].includes(modelId);
}
function getModelsByFeature(feature) {
    return [
        ...featureAvailability[feature]
    ];
}
function getPlatformCapabilities(modelId, platform) {
    return platformCapabilities[platform]?.[modelId] || null;
}
function getMaxQualityForPlatform(modelId, platform) {
    const platformCaps = getPlatformCapabilities(modelId, platform);
    return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;
}
function getSupportedAspectRatios(modelId, platform) {
    if (platform) {
        const platformCaps = getPlatformCapabilities(modelId, platform);
        return platformCaps?.aspectRatios ? [
            ...platformCaps.aspectRatios
        ] : [
            ...modelCapabilities[modelId].aspectRatios
        ];
    }
    return [
        ...modelCapabilities[modelId].aspectRatios
    ];
}
}}),
"[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Pricing Configuration
 * Defines credit costs and pricing tiers for each model
 */ __turbopack_context__.s({
    "creditPackages": (()=>creditPackages),
    "getAllPricing": (()=>getAllPricing),
    "getCheapestModel": (()=>getCheapestModel),
    "getModelPricing": (()=>getModelPricing),
    "getModelsByTier": (()=>getModelsByTier),
    "getMostExpensiveModel": (()=>getMostExpensiveModel),
    "modelPricing": (()=>modelPricing),
    "pricingDisplay": (()=>pricingDisplay),
    "pricingTiers": (()=>pricingTiers),
    "usageCalculations": (()=>usageCalculations)
});
const modelPricing = {
    'revo-1.0': {
        creditsPerGeneration: 1.5,
        creditsPerDesign: 1.5,
        creditsPerVideo: 0,
        tier: 'enhanced' // Upgraded from basic
    },
    'revo-1.5': {
        creditsPerGeneration: 2,
        creditsPerDesign: 2,
        creditsPerVideo: 0,
        tier: 'premium'
    },
    'revo-2.0': {
        creditsPerGeneration: 5,
        creditsPerDesign: 5,
        creditsPerVideo: 0,
        tier: 'premium'
    }
};
const pricingTiers = {
    basic: {
        name: 'Basic',
        description: 'Reliable and cost-effective',
        maxCreditsPerGeneration: 2,
        features: [
            'Standard quality generation',
            'Basic brand consistency',
            'Core platform support',
            'Standard processing speed'
        ],
        recommendedFor: [
            'Small businesses',
            'Personal brands',
            'Budget-conscious users',
            'Basic content needs'
        ]
    },
    premium: {
        name: 'Premium',
        description: 'Enhanced features and quality',
        maxCreditsPerGeneration: 10,
        features: [
            'Enhanced quality generation',
            'Advanced brand consistency',
            'Full platform support',
            'Artifact integration',
            'Real-time context',
            'Trending topics',
            'Multiple aspect ratios'
        ],
        recommendedFor: [
            'Growing businesses',
            'Marketing agencies',
            'Content creators',
            'Professional brands'
        ]
    },
    enterprise: {
        name: 'Enterprise',
        description: 'Maximum quality and features',
        maxCreditsPerGeneration: 20,
        features: [
            'Premium quality generation',
            '4K resolution support',
            'Perfect text rendering',
            'Advanced style controls',
            'Priority processing',
            'Dedicated support',
            'Custom integrations'
        ],
        recommendedFor: [
            'Large enterprises',
            'Premium brands',
            'High-volume users',
            'Quality-focused campaigns'
        ]
    }
};
const creditPackages = {
    starter: {
        name: 'Starter Pack',
        credits: 50,
        price: 9.99,
        pricePerCredit: 0.20,
        bestFor: 'revo-1.0',
        estimatedGenerations: {
            'revo-1.0': 50,
            'revo-1.5': 25,
            'imagen-4': 5
        }
    },
    professional: {
        name: 'Professional Pack',
        credits: 200,
        price: 29.99,
        pricePerCredit: 0.15,
        bestFor: 'revo-1.5',
        estimatedGenerations: {
            'revo-1.0': 200,
            'revo-1.5': 100,
            'imagen-4': 20
        }
    },
    business: {
        name: 'Business Pack',
        credits: 500,
        price: 59.99,
        pricePerCredit: 0.12,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 500,
            'revo-1.5': 250,
            'imagen-4': 50
        }
    },
    enterprise: {
        name: 'Enterprise Pack',
        credits: 1000,
        price: 99.99,
        pricePerCredit: 0.10,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 1000,
            'revo-1.5': 500,
            'revo-2.0': 200,
            'imagen-4': 100
        }
    }
};
const usageCalculations = {
    // Calculate cost for a specific generation request
    calculateGenerationCost (modelId, type = 'content') {
        const pricing = modelPricing[modelId];
        switch(type){
            case 'content':
                return pricing.creditsPerGeneration;
            case 'design':
                return pricing.creditsPerDesign;
            case 'video':
                return pricing.creditsPerVideo || 0;
            default:
                return pricing.creditsPerGeneration;
        }
    },
    // Calculate total cost for multiple generations
    calculateBatchCost (requests) {
        return requests.reduce((total, request)=>{
            return total + this.calculateGenerationCost(request.modelId, request.type);
        }, 0);
    },
    // Estimate monthly cost based on usage patterns
    estimateMonthlyCost (usage) {
        const pricing = modelPricing[usage.modelId];
        const dailyCost = usage.generationsPerDay * pricing.creditsPerGeneration + usage.designsPerDay * pricing.creditsPerDesign + (usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0);
        const monthlyCost = dailyCost * 30;
        // Recommend package based on monthly cost
        let recommendedPackage = 'starter';
        if (monthlyCost > 400) recommendedPackage = 'enterprise';
        else if (monthlyCost > 150) recommendedPackage = 'business';
        else if (monthlyCost > 50) recommendedPackage = 'professional';
        return {
            dailyCost,
            monthlyCost,
            recommendedPackage
        };
    },
    // Check if user has enough credits for a request
    canAfford (userCredits, modelId, type = 'content') {
        const cost = this.calculateGenerationCost(modelId, type);
        return userCredits >= cost;
    },
    // Get the best model within budget
    getBestModelForBudget (availableCredits, type = 'content') {
        const affordableModels = [];
        for (const [modelId, pricing] of Object.entries(modelPricing)){
            const cost = type === 'content' ? pricing.creditsPerGeneration : type === 'design' ? pricing.creditsPerDesign : pricing.creditsPerVideo || 0;
            if (cost <= availableCredits && cost > 0) {
                affordableModels.push(modelId);
            }
        }
        // Sort by quality (higher credit cost usually means higher quality)
        return affordableModels.sort((a, b)=>{
            const costA = this.calculateGenerationCost(a, type);
            const costB = this.calculateGenerationCost(b, type);
            return costB - costA; // Descending order (highest quality first)
        });
    }
};
const pricingDisplay = {
    // Format credits for display
    formatCredits (credits) {
        if (credits >= 1000) {
            return `${(credits / 1000).toFixed(1)}K`;
        }
        return credits.toString();
    },
    // Format price for display
    formatPrice (price) {
        return `$${price.toFixed(2)}`;
    },
    // Get pricing tier info
    getTierInfo (modelId) {
        const pricing = modelPricing[modelId];
        return pricingTiers[pricing.tier];
    },
    // Get cost comparison between models
    compareCosts (modelA, modelB) {
        const costA = modelPricing[modelA].creditsPerGeneration;
        const costB = modelPricing[modelB].creditsPerGeneration;
        const difference = Math.abs(costA - costB);
        const percentDifference = (difference / Math.min(costA, costB) * 100).toFixed(0);
        return {
            cheaper: costA < costB ? modelA : modelB,
            moreExpensive: costA > costB ? modelA : modelB,
            difference,
            percentDifference: `${percentDifference}%`,
            ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`
        };
    },
    // Get value proposition for each model
    getValueProposition (modelId) {
        const pricing = modelPricing[modelId];
        const tierInfo = pricingTiers[pricing.tier];
        return {
            model: modelId,
            tier: pricing.tier,
            creditsPerGeneration: pricing.creditsPerGeneration,
            valueScore: tierInfo.features.length / pricing.creditsPerGeneration,
            description: tierInfo.description,
            bestFor: tierInfo.recommendedFor
        };
    }
};
function getModelPricing(modelId) {
    return modelPricing[modelId];
}
function getAllPricing() {
    return modelPricing;
}
function getModelsByTier(tier) {
    return Object.entries(modelPricing).filter(([_, pricing])=>pricing.tier === tier).map(([modelId])=>modelId);
}
function getCheapestModel() {
    return Object.entries(modelPricing).reduce((cheapest, [modelId, pricing])=>{
        const currentCheapest = modelPricing[cheapest];
        return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ? modelId : cheapest;
    }, 'revo-1.0');
}
function getMostExpensiveModel() {
    return Object.entries(modelPricing).reduce((mostExpensive, [modelId, pricing])=>{
        const currentMostExpensive = modelPricing[mostExpensive];
        return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ? modelId : mostExpensive;
    }, 'revo-1.0');
}
}}),
"[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Configurations
 * Centralized configuration for all Revo model versions
 */ __turbopack_context__.s({
    "compareModels": (()=>compareModels),
    "getAllModelConfigs": (()=>getAllModelConfigs),
    "getLatestModels": (()=>getLatestModels),
    "getModelConfig": (()=>getModelConfig),
    "getModelForBudget": (()=>getModelForBudget),
    "getModelsByStatus": (()=>getModelsByStatus),
    "getModelsByTier": (()=>getModelsByTier),
    "getRecommendedModel": (()=>getRecommendedModel),
    "modelConfigs": (()=>modelConfigs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)");
;
;
// Base configurations for different AI services
const baseConfigs = {
    'gemini-2.0': {
        aiService: 'gemini-2.0',
        fallbackServices: [
            'gemini-2.5',
            'openai'
        ],
        maxRetries: 3,
        timeout: 30000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 85,
            enhancementLevel: 5
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 2048,
            topP: 0.9,
            topK: 40
        }
    },
    'gemini-2.5': {
        aiService: 'gemini-2.5',
        fallbackServices: [
            'gemini-2.0',
            'openai'
        ],
        maxRetries: 2,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 90,
            enhancementLevel: 7
        },
        promptSettings: {
            temperature: 0.8,
            maxTokens: 4096,
            topP: 0.95,
            topK: 50
        }
    },
    'openai': {
        aiService: 'openai',
        fallbackServices: [
            'gemini-2.5',
            'gemini-2.0'
        ],
        maxRetries: 3,
        timeout: 35000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 88,
            enhancementLevel: 6
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 3000,
            topP: 0.9
        }
    },
    'gemini-2.5-flash-image': {
        aiService: 'gemini-2.5-flash-image',
        fallbackServices: [
            'imagen-4',
            'gemini-2.5'
        ],
        maxRetries: 3,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '2048x2048',
            compressionLevel: 92,
            enhancementLevel: 9
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 2048,
            topP: 0.9,
            topK: 40
        }
    }
};
const modelConfigs = {
    'revo-1.0': {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        version: '1.0.0',
        description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',
        longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',
        icon: 'Zap',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.0'],
        features: [
            'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',
            '1:1 Images with High Resolution',
            'Core Features',
            'Proven Performance',
            'Multi-platform Support',
            'Enhanced Brand Consistency',
            'Perfect Text Rendering',
            'High-Resolution Output (2048x2048)'
        ],
        releaseDate: '2024-01-15',
        lastUpdated: '2025-01-27'
    },
    'revo-1.5': {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        version: '1.5.0',
        description: 'Enhanced Model - Advanced Features',
        longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',
        icon: 'Sparkles',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.5'],
        config: {
            ...baseConfigs['gemini-2.5'],
            qualitySettings: {
                ...baseConfigs['gemini-2.5'].qualitySettings,
                enhancementLevel: 8
            }
        },
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.5'],
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations',
            'Professional Templates',
            'Advanced Brand Integration',
            'Real-time Context',
            'Trending Topics Integration'
        ],
        releaseDate: '2024-06-20',
        lastUpdated: '2024-12-15'
    },
    'revo-2.0': {
        id: 'revo-2.0',
        name: 'Revo 2.0',
        version: '2.0.0',
        description: 'Next-Gen Model - Advanced AI with native image generation',
        longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',
        icon: 'Rocket',
        badge: 'Next-Gen',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-2.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-2.0'],
        features: [
            'Next-Gen AI Engine',
            'Native Image Generation',
            'Character Consistency',
            'Intelligent Editing',
            'Inpainting & Outpainting',
            'Multimodal Reasoning',
            'All Aspect Ratios',
            'Perfect Brand Consistency'
        ],
        releaseDate: '2025-01-27',
        lastUpdated: '2025-01-27'
    }
};
function getModelConfig(modelId) {
    const config = modelConfigs[modelId];
    if (!config) {
        throw new Error(`Model configuration not found for: ${modelId}`);
    }
    return config;
}
function getAllModelConfigs() {
    return Object.values(modelConfigs);
}
function getModelsByStatus(status) {
    return getAllModelConfigs().filter((model)=>model.status === status);
}
function getModelsByTier(tier) {
    return getAllModelConfigs().filter((model)=>model.pricing.tier === tier);
}
function getLatestModels() {
    return getAllModelConfigs().sort((a, b)=>new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()).slice(0, 3);
}
function getRecommendedModel() {
    // Return Revo 1.5 as the recommended balanced option
    return modelConfigs['revo-1.5'];
}
function getModelForBudget(maxCredits) {
    return getAllModelConfigs().filter((model)=>model.pricing.creditsPerGeneration <= maxCredits).sort((a, b)=>a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);
}
function compareModels(modelA, modelB) {
    const configA = getModelConfig(modelA);
    const configB = getModelConfig(modelB);
    return {
        quality: {
            a: configA.capabilities.maxQuality,
            b: configB.capabilities.maxQuality,
            winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB
        },
        cost: {
            a: configA.pricing.creditsPerGeneration,
            b: configB.pricing.creditsPerGeneration,
            winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB
        },
        features: {
            a: configA.features.length,
            b: configB.features.length,
            winner: configA.features.length > configB.features.length ? modelA : modelB
        },
        status: {
            a: configA.status,
            b: configB.status,
            recommendation: configA.status === 'stable' || configB.status === 'stable' ? configA.status === 'stable' ? modelA : modelB : modelA
        }
    };
}
}}),
"[project]/src/ai/models/versions/revo-1.0/config.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Configuration
 * Model-specific configuration and constants
 */ __turbopack_context__.s({
    "getPerformanceBenchmark": (()=>getPerformanceBenchmark),
    "getPromptTemplate": (()=>getPromptTemplate),
    "getRevo10Config": (()=>getRevo10Config),
    "isFeatureEnabled": (()=>isFeatureEnabled),
    "revo10Config": (()=>revo10Config),
    "revo10Constants": (()=>revo10Constants),
    "revo10Metrics": (()=>revo10Metrics),
    "revo10Prompts": (()=>revo10Prompts),
    "revo10Validation": (()=>revo10Validation),
    "shouldAlert": (()=>shouldAlert),
    "validateRequest": (()=>validateRequest)
});
const revo10Config = {
    aiService: 'gemini-2.5-flash-image-preview',
    fallbackServices: [
        'gemini-2.5',
        'gemini-2.0',
        'openai'
    ],
    maxRetries: 3,
    timeout: 45000,
    qualitySettings: {
        imageResolution: '2048x2048',
        compressionLevel: 92,
        enhancementLevel: 9 // Upgraded from 7 (maximum enhancement)
    },
    promptSettings: {
        temperature: 0.7,
        maxTokens: 2048,
        topP: 0.9,
        topK: 40 // Increased from 30 for more variety
    }
};
const revo10Constants = {
    // Model identification
    MODEL_ID: 'revo-1.0',
    MODEL_NAME: 'Revo 1.0',
    MODEL_VERSION: '1.0.0',
    // Capabilities
    SUPPORTED_ASPECT_RATIOS: [
        '1:1'
    ],
    SUPPORTED_PLATFORMS: [
        'Instagram',
        'Facebook',
        'Twitter',
        'LinkedIn'
    ],
    MAX_QUALITY_SCORE: 9.0,
    // Performance targets
    TARGET_PROCESSING_TIME: 30000,
    TARGET_SUCCESS_RATE: 0.97,
    TARGET_QUALITY_SCORE: 8.5,
    // Resource limits
    MAX_CONTENT_LENGTH: 2000,
    MAX_HASHTAGS: 15,
    MAX_IMAGE_SIZE: 2048,
    // Feature flags
    FEATURES: {
        ARTIFACTS_SUPPORT: false,
        REAL_TIME_CONTEXT: true,
        TRENDING_TOPICS: true,
        MULTIPLE_ASPECT_RATIOS: false,
        VIDEO_GENERATION: false,
        ADVANCED_PROMPTING: true,
        ENHANCED_DESIGN: true,
        PERFECT_TEXT_RENDERING: true,
        HIGH_RESOLUTION: true,
        NATIVE_IMAGE_GENERATION: true // NEW: Direct image generation capability
    },
    // Pricing
    CREDITS_PER_GENERATION: 1.5,
    CREDITS_PER_DESIGN: 1.5,
    TIER: 'enhanced' // Upgraded from basic
};
const revo10Prompts = {
    // Content generation prompts
    CONTENT_SYSTEM_PROMPT: `You are an elite social media content strategist for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering.
Your expertise spans viral content creation, brand storytelling, and audience engagement optimization.

Core competencies:
- Craft scroll-stopping, engagement-driving captions
- Create strategic hashtag combinations for maximum reach
- Develop brand-consistent content that converts
- Optimize content for platform-specific algorithms
- Generate compelling headlines and calls-to-action
- Integrate local relevance and cultural context
- Drive meaningful audience interaction and community building
- Leverage trending topics and industry insights
- Create content that balances professionalism with personality`,
    CONTENT_USER_PROMPT_TEMPLATE: `Generate social media content for:
Business: {businessName}
Type: {businessType}
Platform: {platform}
Tone: {writingTone}
Location: {location}

Brand Information:
- Primary Color: {primaryColor}
- Visual Style: {visualStyle}
- Target Audience: {targetAudience}
- Services: {services}
- Key Features: {keyFeatures}
- Competitive Advantages: {competitiveAdvantages}
- Content Themes: {contentThemes}

Requirements:
- Create engaging, professional content that reflects the business's unique value proposition
- Incorporate services and key features naturally into the content
- Highlight competitive advantages when relevant
- Include relevant hashtags (5-15) that align with content themes
- Generate catchy words for the image that capture the brand essence
- Ensure platform-appropriate formatting and tone
- Maintain brand consistency with colors and visual style
- Use only clean, readable text (no special characters, symbols, or garbled text)
- Generate content in proper English with correct spelling and grammar
- Avoid any corrupted or unreadable character sequences
- Make the content location-specific and culturally relevant when appropriate`,
    // Design generation prompts
    DESIGN_SYSTEM_PROMPT: `You are an elite visual designer and creative director for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for professional-grade design generation.
Your expertise spans advanced composition, typography, color theory, and modern design trends. You create designs that surpass Canva quality.

CORE DESIGN PHILOSOPHY:
- Create visually stunning, professional designs that command attention
- Apply advanced design principles and composition rules
- Use sophisticated typography and color harmony
- Implement modern design trends and visual techniques
- Ensure every element serves a purpose and enhances the message
- Generate designs that convert viewers into customers

ADVANCED DESIGN PRINCIPLES:
**COMPOSITION & VISUAL HIERARCHY:**
- Apply Rule of Thirds: Position key elements along grid lines/intersections
- Create clear visual hierarchy using size, contrast, and positioning
- Establish strong focal points that draw the eye immediately
- Use negative space strategically for breathing room and emphasis
- Balance elements with sophisticated asymmetrical composition
- Guide viewer's eye through design with leading lines and flow

**TYPOGRAPHY EXCELLENCE:**
- Establish clear typographic hierarchy (Primary headline, secondary, body)
- Use maximum 2-3 font families with strong contrast
- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)
- Apply proper letter spacing, line height, and alignment
- Scale typography for platform and viewing distance
- Use typography as a design element, not just information delivery

**COLOR THEORY & HARMONY:**
- Apply color psychology appropriate to business type and message
- Use complementary colors for high contrast and attention
- Apply analogous colors for harmony and cohesion
- Implement triadic color schemes for vibrant, balanced designs
- Use 60-30-10 rule: 60% dominant, 30% secondary, 10% accent
- Ensure sufficient contrast between text and background

**MODERN DESIGN TRENDS:**
- Embrace minimalism with purposeful white space
- Use bold, geometric shapes and clean lines
- Apply subtle gradients and depth effects
- Incorporate authentic, diverse imagery when appropriate
- Use consistent border radius and spacing
- Apply subtle shadows and depth for modern dimensionality`,
    DESIGN_USER_PROMPT_TEMPLATE: `Create a professional-grade 2048x2048 social media design that surpasses Canva quality for:

BUSINESS CONTEXT:
- Business: {businessName}
- Industry: {businessType}
- Platform: {platform}
- Visual Style: {visualStyle}
- Target Message: {imageText}

BRAND IDENTITY SYSTEM:
- Primary Color: {primaryColor} (60% usage - dominant color)
- Accent Color: {accentColor} (30% usage - secondary elements)
- Background: {backgroundColor} (10% usage - highlights and details)
- Logo Integration: {logoInstruction}

PLATFORM-SPECIFIC OPTIMIZATION FOR {platform}:
${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ''}
${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ''}
${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ''}
${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ''}

BUSINESS TYPE DESIGN DNA FOR {businessType}:
Apply industry-specific design principles and visual language appropriate for this business type.

ADVANCED COMPOSITION REQUIREMENTS:
- Apply Rule of Thirds for element placement
- Create strong focal point with {imageText} as primary message
- Use sophisticated asymmetrical balance
- Implement clear visual hierarchy: Headline → Supporting elements → CTA
- Strategic negative space for premium feel
- Leading lines to guide eye flow

TYPOGRAPHY SPECIFICATIONS:
- Primary headline: Bold, attention-grabbing, high contrast
- Secondary text: Supporting, readable, complementary
- Ensure 4.5:1 contrast ratio minimum
- Professional font pairing (max 2-3 families)
- Proper spacing and alignment

COLOR IMPLEMENTATION:
- Use {primaryColor} as dominant (60%)
- {accentColor} for secondary elements (30%)
- {backgroundColor} for highlights (10%)
- Apply color psychology for {businessType}
- Ensure accessibility and contrast

MODERN DESIGN ELEMENTS:
- Subtle gradients and depth effects
- Clean geometric shapes
- Consistent border radius
- Professional shadows and lighting
- Premium visual texture and finish

QUALITY STANDARDS:
- Professional agency-level quality
- Better than Canva templates
- Print-ready resolution and clarity
- Perfect text rendering
- Sophisticated visual appeal
- Commercial-grade finish`,
    // Error messages
    ERROR_MESSAGES: {
        GENERATION_FAILED: 'Revo 1.0 content generation failed. Please try again.',
        DESIGN_FAILED: 'Revo 1.0 design generation failed. Please try again.',
        INVALID_REQUEST: 'Invalid request for Revo 1.0. Please check your parameters.',
        SERVICE_UNAVAILABLE: 'Revo 1.0 service is temporarily unavailable.',
        TIMEOUT: 'Revo 1.0 generation timed out. Please try again.',
        QUOTA_EXCEEDED: 'Revo 1.0 usage quota exceeded. Please upgrade your plan.'
    }
};
const revo10Validation = {
    // Content validation
    content: {
        minLength: 10,
        maxLength: 2000,
        requiredFields: [
            'businessType',
            'platform',
            'businessName'
        ],
        supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS
    },
    // Design validation
    design: {
        requiredFields: [
            'businessType',
            'platform',
            'visualStyle',
            'imageText'
        ],
        supportedAspectRatios: revo10Constants.SUPPORTED_ASPECT_RATIOS,
        maxImageTextLength: 200,
        supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS
    },
    // Brand profile validation
    brandProfile: {
        requiredFields: [
            'businessType',
            'businessName'
        ],
        optionalFields: [
            'location',
            'writingTone',
            'visualStyle',
            'primaryColor',
            'accentColor',
            'backgroundColor',
            'logoDataUrl',
            'targetAudience'
        ]
    }
};
const revo10Metrics = {
    // Expected performance benchmarks
    BENCHMARKS: {
        processingTime: {
            target: 30000,
            acceptable: 40000,
            maximum: 60000 // 60 seconds (upgraded from 45s)
        },
        qualityScore: {
            minimum: 7.0,
            target: 8.5,
            maximum: 9.0 // Upgraded from 7.5
        },
        successRate: {
            minimum: 0.95,
            target: 0.97,
            maximum: 0.99 // Upgraded from 98%
        }
    },
    // Monitoring thresholds
    ALERTS: {
        processingTimeHigh: 45000,
        qualityScoreLow: 7.5,
        successRateLow: 0.95,
        errorRateHigh: 0.05 // Alert if error rate exceeds 5% (upgraded from 8%)
    }
};
function getRevo10Config() {
    return revo10Config;
}
function isFeatureEnabled(feature) {
    return revo10Constants.FEATURES[feature];
}
function getPromptTemplate(type, templateName) {
    if (type === 'content') {
        return revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE;
    } else if (type === 'design') {
        return revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE;
    }
    throw new Error(`Unknown prompt template: ${type}/${templateName}`);
}
function validateRequest(type, request) {
    const errors = [];
    const validation = type === 'content' ? revo10Validation.content : revo10Validation.design;
    // Check required fields
    for (const field of validation.requiredFields){
        if (!request[field]) {
            errors.push(`Missing required field: ${field}`);
        }
    }
    // Check platform support
    if (request.platform && !validation.supportedPlatforms.includes(request.platform)) {
        errors.push(`Unsupported platform: ${request.platform}`);
    }
    // Design-specific validation
    if (type === 'design') {
        if (request.imageText && request.imageText.length > revo10Validation.design.maxImageTextLength) {
            errors.push(`Image text too long (max ${revo10Validation.design.maxImageTextLength} characters)`);
        }
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
function getPerformanceBenchmark(metric) {
    return revo10Metrics.BENCHMARKS[metric];
}
function shouldAlert(metric, value) {
    const alerts = revo10Metrics.ALERTS;
    switch(metric){
        case 'processingTime':
            return value > alerts.processingTimeHigh;
        case 'qualityScore':
            return value < alerts.qualityScoreLow;
        case 'successRate':
            return value < alerts.successRateLow;
        case 'errorRate':
            return value > alerts.errorRateHigh;
        default:
            return false;
    }
}
}}),
"[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 - Enhanced AI Service with Gemini 2.5 Flash Image Preview
 * Upgraded from Gemini 2.0 to provide enhanced quality and perfect text rendering
 */ __turbopack_context__.s({
    "checkRevo10Health": (()=>checkRevo10Health),
    "generateRevo10Content": (()=>generateRevo10Content),
    "generateRevo10Design": (()=>generateRevo10Design),
    "generateRevo10Image": (()=>generateRevo10Image),
    "getRevo10ServiceInfo": (()=>getRevo10ServiceInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/config.ts [app-rsc] (ecmascript)");
;
;
// Advanced features integration (simplified for now)
// TODO: Import advanced features from Revo 1.5 when available
// Helper functions for advanced design generation
function getBusinessDesignDNA(businessType) {
    const designDNA = {
        'restaurant': 'Warm, appetizing colors (reds, oranges, warm yellows). High-quality food photography. Cozy, inviting atmosphere. Emphasis on freshness and quality.',
        'technology': 'Clean, modern aesthetics. Blue and tech-forward color schemes. Geometric shapes. Innovation and reliability focus. Professional typography.',
        'healthcare': 'Clean, trustworthy design. Calming blues and greens. Professional imagery. Focus on care and expertise. Accessible design principles.',
        'fitness': 'Dynamic, energetic design. Bold colors and strong contrasts. Action-oriented imagery. Motivational messaging. Strong, athletic typography.',
        'finance': 'Professional, trustworthy design. Conservative color palette. Clean lines. Security and stability focus. Authoritative typography.',
        'education': 'Approachable, inspiring design. Bright, optimistic colors. Clear information hierarchy. Growth and learning focus. Readable typography.',
        'retail': 'Attractive, commercial design. Brand-focused colors. Product-centric imagery. Sales and value focus. Eye-catching typography.',
        'real estate': 'Luxurious, aspirational design. Sophisticated color palette. High-quality property imagery. Trust and expertise focus. Elegant typography.',
        'default': 'Professional, modern design. Balanced color scheme. Clean, contemporary aesthetics. Quality and reliability focus. Professional typography.'
    };
    return designDNA[businessType.toLowerCase()] || designDNA['default'];
}
function getPlatformOptimization(platform) {
    const optimizations = {
        'instagram': `
- Mobile-first design with bold, clear elements
- High contrast colors that pop on small screens
- Text minimum 24px equivalent for readability
- Center important elements for square crop compatibility
- Thumb-stopping power for fast scroll feeds
- Logo: Bottom right corner or naturally integrated`,
        'linkedin': `
- Professional, business-appropriate aesthetics
- Corporate design standards and clean look
- Clear value proposition for business audience
- Professional photography and imagery
- Thought leadership positioning
- Logo: Prominent placement for brand authority`,
        'facebook': `
- Desktop and mobile viewing optimization
- Engagement and shareability focus
- Clear value proposition in visual hierarchy
- Authentic, relatable imagery
- Community-focused design elements
- Logo: Top left or bottom right corner`,
        'twitter': `
- Rapid consumption and high engagement design
- Bold, contrasting colors for timeline visibility
- Minimal, impactful text elements
- Trending visual styles integration
- Real-time relevance
- Logo: Small, subtle placement`,
        'default': `
- Cross-platform compatibility
- Universal appeal and accessibility
- Balanced design for multiple contexts
- Professional appearance across devices
- Logo: Flexible placement based on composition`
    };
    return optimizations[platform.toLowerCase()] || optimizations['default'];
}
// Advanced real-time context gathering for Revo 1.0 (simplified version)
async function gatherRealTimeContext(businessType, location, platform) {
    console.log('🌐 Revo 1.0: Gathering enhanced context data...');
    const context = {
        trends: [],
        weather: null,
        events: [],
        news: [],
        timeContext: {
            dayOfWeek: new Date().toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            month: new Date().toLocaleDateString('en-US', {
                month: 'long'
            }),
            season: getSeason(),
            timeOfDay: getTimeOfDay()
        }
    };
    try {
        // Generate contextual trends based on business type and location
        console.log('📈 Generating contextual trends...');
        context.trends = generateContextualTrends(businessType, location);
        console.log(`✅ Generated ${context.trends.length} contextual trends`);
        // Generate weather-appropriate content suggestions
        console.log('🌤️ Generating weather context...');
        context.weather = generateWeatherContext(location);
        console.log(`✅ Weather context: ${context.weather.condition}`);
        // Generate local business opportunities
        console.log('🎪 Generating local opportunities...');
        context.events = generateLocalOpportunities(businessType, location);
        console.log(`✅ Found ${context.events.length} business opportunities`);
        console.log('✅ Enhanced context gathered successfully');
        return context;
    } catch (error) {
        console.error('❌ Error gathering enhanced context:', error);
        return context; // Return partial context
    }
}
// Advanced design enhancement functions
function shouldIncludePeopleInDesign(businessType, location, visualStyle) {
    const peopleBusinessTypes = [
        'restaurant',
        'fitness',
        'healthcare',
        'education',
        'retail',
        'hospitality',
        'beauty',
        'wellness',
        'consulting',
        'coaching',
        'real estate',
        'finance',
        'technology',
        'marketing',
        'events',
        'photography',
        'fashion'
    ];
    return peopleBusinessTypes.some((type)=>businessType.toLowerCase().includes(type) || visualStyle === 'lifestyle' || visualStyle === 'authentic');
}
function getLocalCulturalContext(location) {
    const culturalContexts = {
        'kenya': 'Kenyan culture with vibrant colors, traditional patterns, modern African aesthetics, diverse ethnic representation (Kikuyu, Luo, Luhya, Kalenjin), urban Nairobi style mixed with traditional elements',
        'nigeria': 'Nigerian culture with bold Ankara patterns, diverse ethnic representation, modern Lagos urban style, traditional and contemporary fusion',
        'south africa': 'South African rainbow nation diversity, modern Cape Town/Johannesburg aesthetics, traditional and contemporary blend',
        'ghana': 'Ghanaian Kente patterns, warm earth tones, modern Accra style, traditional craftsmanship meets contemporary design',
        'uganda': 'Ugandan cultural diversity, vibrant textiles, modern Kampala urban style, traditional meets modern aesthetics',
        'tanzania': 'Tanzanian Maasai influences, Swahili coastal culture, modern Dar es Salaam style, traditional patterns with contemporary flair',
        'default': 'Diverse, inclusive representation with modern professional aesthetics, cultural sensitivity, and authentic human connections'
    };
    const locationKey = location.toLowerCase();
    for (const [key, context] of Object.entries(culturalContexts)){
        if (locationKey.includes(key)) {
            return context;
        }
    }
    return culturalContexts['default'];
}
function getDesignVariations(seed) {
    const variations = [
        {
            style: 'Modern Minimalist',
            layout: 'Clean geometric layout with plenty of white space, single focal point, minimal text overlay',
            composition: 'Centered composition with asymmetrical elements, bold typography hierarchy',
            mood: 'Professional, clean, sophisticated',
            elements: 'Subtle gradients, clean lines, modern sans-serif fonts, minimal color palette'
        },
        {
            style: 'Dynamic Action',
            layout: 'Diagonal composition with movement, multiple focal points, energetic flow',
            composition: 'Rule of thirds with dynamic angles, overlapping elements, motion blur effects',
            mood: 'Energetic, exciting, forward-moving',
            elements: 'Bold colors, dynamic shapes, action-oriented imagery, strong directional lines'
        },
        {
            style: 'Lifestyle Authentic',
            layout: 'Natural, candid composition with real-world settings, human-centered design',
            composition: 'Environmental context, natural lighting, authentic moments captured',
            mood: 'Warm, relatable, trustworthy, human',
            elements: 'Natural lighting, authentic people, real environments, warm color tones'
        },
        {
            style: 'Corporate Professional',
            layout: 'Structured grid layout, balanced composition, formal presentation',
            composition: 'Symmetrical balance, clear hierarchy, professional spacing',
            mood: 'Trustworthy, established, reliable, premium',
            elements: 'Corporate colors, professional imagery, clean typography, structured layout'
        },
        {
            style: 'Creative Artistic',
            layout: 'Artistic composition with creative elements, unique perspectives, artistic flair',
            composition: 'Creative angles, artistic overlays, unique visual treatments',
            mood: 'Creative, innovative, unique, inspiring',
            elements: 'Artistic effects, creative typography, unique color combinations, artistic imagery'
        },
        {
            style: 'Tech Innovation',
            layout: 'Futuristic design with tech elements, digital aesthetics, modern interfaces',
            composition: 'Digital grid systems, tech-inspired layouts, modern UI elements',
            mood: 'Innovative, cutting-edge, digital, forward-thinking',
            elements: 'Digital effects, tech imagery, modern interfaces, futuristic elements'
        },
        {
            style: 'Cultural Heritage',
            layout: 'Traditional patterns mixed with modern design, cultural elements integrated',
            composition: 'Cultural motifs, traditional-modern fusion, heritage-inspired layouts',
            mood: 'Cultural, authentic, heritage-proud, modern-traditional',
            elements: 'Traditional patterns, cultural colors, heritage imagery, modern interpretation'
        },
        {
            style: 'Luxury Premium',
            layout: 'Elegant, sophisticated layout with premium materials and finishes',
            composition: 'Luxurious spacing, premium typography, elegant proportions',
            mood: 'Luxurious, premium, exclusive, sophisticated',
            elements: 'Premium materials, elegant typography, sophisticated colors, luxury imagery'
        }
    ];
    return variations[seed % variations.length];
}
function getAdvancedPeopleInstructions(businessType, location) {
    const culturalContext = getLocalCulturalContext(location);
    return `
**ADVANCED PEOPLE INTEGRATION:**
- Include diverse, authentic people with PERFECT FACIAL FEATURES
- Complete faces, symmetrical features, natural expressions, professional poses
- Faces fully visible, well-lit, anatomically correct with no deformations
- Cultural Context: ${culturalContext}
- Show people in varied, engaging settings:
  * Professional environments (modern offices, studios, workshops)
  * Lifestyle settings (contemporary homes, trendy cafes, outdoor spaces)
  * Industry-specific contexts (${businessType} environments)
  * Cultural celebrations and modern community gatherings
  * Urban settings (co-working spaces, tech hubs, modern city life)
  * Traditional meets modern (cultural heritage with contemporary life)
- Ensure representation reflects local demographics and cultural values
- Show real people in natural, engaging situations that vary by design
- People should be actively engaged with the business/service context
- Use authentic expressions of joy, confidence, success, and community
- Include intergenerational representation when appropriate
- Show modern African/local fashion and styling
- Ensure people are central to the story, not just decorative elements`;
}
// Helper functions for context generation
function getSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'Spring';
    if (month >= 5 && month <= 7) return 'Summer';
    if (month >= 8 && month <= 10) return 'Fall';
    return 'Winter';
}
function getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'Morning';
    if (hour >= 12 && hour < 17) return 'Afternoon';
    if (hour >= 17 && hour < 21) return 'Evening';
    return 'Night';
}
function generateContextualTrends(businessType, location) {
    const trends = [
        {
            topic: `${businessType} innovation trends`,
            category: 'Industry',
            relevance: 'high'
        },
        {
            topic: `${location} business growth`,
            category: 'Local',
            relevance: 'high'
        },
        {
            topic: 'Digital transformation',
            category: 'Technology',
            relevance: 'medium'
        },
        {
            topic: 'Customer experience optimization',
            category: 'Business',
            relevance: 'high'
        },
        {
            topic: 'Sustainable business practices',
            category: 'Trends',
            relevance: 'medium'
        }
    ];
    return trends.slice(0, 3);
}
function generateWeatherContext(location) {
    // Simplified weather context based on location and season
    const season = getSeason();
    const contexts = {
        'Spring': {
            condition: 'Fresh and energizing',
            business_impact: 'New beginnings, growth opportunities',
            content_opportunities: 'Renewal, fresh starts, growth themes'
        },
        'Summer': {
            condition: 'Bright and active',
            business_impact: 'High energy, outdoor activities',
            content_opportunities: 'Vibrant colors, active lifestyle, summer solutions'
        },
        'Fall': {
            condition: 'Cozy and productive',
            business_impact: 'Planning, preparation, harvest',
            content_opportunities: 'Preparation, results, autumn themes'
        },
        'Winter': {
            condition: 'Focused and strategic',
            business_impact: 'Planning, reflection, indoor focus',
            content_opportunities: 'Planning, strategy, winter solutions'
        }
    };
    return {
        temperature: '22',
        condition: contexts[season].condition,
        business_impact: contexts[season].business_impact,
        content_opportunities: contexts[season].content_opportunities
    };
}
function generateLocalOpportunities(businessType, location) {
    const opportunities = [
        {
            name: `${location} Business Expo`,
            venue: 'Local Convention Center',
            relevance: 'networking'
        },
        {
            name: `${businessType} Innovation Summit`,
            venue: 'Business District',
            relevance: 'industry'
        },
        {
            name: 'Local Entrepreneur Meetup',
            venue: 'Community Center',
            relevance: 'community'
        }
    ];
    return opportunities.slice(0, 2);
}
// Get API keys (supporting both server-side and client-side)
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;
if (!apiKey) {
    console.error("❌ No Google AI API key found for Revo 1.0");
    console.error("Available env vars:", {
        server: {
            GEMINI_API_KEY: !!process.env.GEMINI_API_KEY,
            GOOGLE_API_KEY: !!process.env.GOOGLE_API_KEY,
            GOOGLE_GENAI_API_KEY: !!process.env.GOOGLE_GENAI_API_KEY
        },
        client: {
            NEXT_PUBLIC_GEMINI_API_KEY: !!process.env.NEXT_PUBLIC_GEMINI_API_KEY,
            NEXT_PUBLIC_GOOGLE_API_KEY: !!process.env.NEXT_PUBLIC_GOOGLE_API_KEY,
            NEXT_PUBLIC_GOOGLE_GENAI_API_KEY: !!process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY
        }
    });
}
// Initialize Google GenAI client with Revo 1.0 configuration
const ai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](apiKey);
// Revo 1.0 uses Gemini 2.5 Flash Image Preview
const REVO_1_0_MODEL = 'gemini-2.5-flash-image-preview';
async function generateRevo10Content(input) {
    try {
        console.log('🚀 Revo 1.0: Starting enhanced content generation with real-time context...');
        // Gather real-time context data
        const realTimeContext = await gatherRealTimeContext(input.businessType, input.location, input.platform);
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build the content generation prompt with enhanced brand context
        const contentPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Prompts"].CONTENT_USER_PROMPT_TEMPLATE.replace('{businessName}', input.businessName).replace('{businessType}', input.businessType).replace('{platform}', input.platform).replace('{writingTone}', input.writingTone).replace('{location}', input.location).replace('{primaryColor}', input.primaryColor || '#3B82F6').replace('{visualStyle}', input.visualStyle || 'modern').replace('{targetAudience}', input.targetAudience).replace('{services}', input.services || '').replace('{keyFeatures}', input.keyFeatures || '').replace('{competitiveAdvantages}', input.competitiveAdvantages || '').replace('{contentThemes}', input.contentThemes.join(', ') || 'general business content');
        console.log('📝 Revo 1.0: Generating content with enhanced AI capabilities...');
        // Generate enhanced caption using advanced copywriting techniques
        const enhancedCaptionPrompt = `You are an elite social media strategist and copywriting expert with deep expertise in the ${input.businessType} industry.
Your mission is to create scroll-stopping content that maximizes engagement, drives conversions, and builds authentic connections.

COMPREHENSIVE BUSINESS INTELLIGENCE:
- Business Name: ${input.businessName}
- Industry: ${input.businessType}
- Location: ${input.location}
- Brand Voice: ${input.writingTone}
- Visual Style: ${input.visualStyle || 'modern'}
- Primary Color: ${input.primaryColor || '#3B82F6'}
- Target Audience: ${input.targetAudience}
- Services Offered: ${input.services || 'Professional services'}
- Key Features: ${input.keyFeatures || 'Quality and reliability'}
- Competitive Advantages: ${input.competitiveAdvantages || 'Unique value proposition'}
- Content Themes: ${input.contentThemes.join(', ') || 'Business excellence'}
- Platform: ${input.platform}
- Day: ${input.dayOfWeek}
- Date: ${input.currentDate}

BRAND IDENTITY INTEGRATION:
- Use business name "${input.businessName}" naturally in content
- Reflect ${input.businessType} industry expertise
- Incorporate ${input.location} local relevance
- Match ${input.writingTone} brand voice consistently
- Highlight unique services and competitive advantages
- Appeal to ${input.targetAudience} specifically

ADVANCED COPYWRITING FRAMEWORKS TO USE:
1. **AIDA Framework**: Attention → Interest → Desire → Action
2. **PAS Framework**: Problem → Agitation → Solution
3. **Storytelling Elements**: Character, conflict, resolution
4. **Social Proof Integration**: Success stories, testimonials

PSYCHOLOGICAL TRIGGERS TO IMPLEMENT:
✅ **Curiosity Gaps**: Intriguing questions that demand answers
✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy
✅ **Authority**: Expert insights, industry knowledge
✅ **Reciprocity**: Valuable tips, free insights
✅ **Social Proof**: Customer success, popularity indicators

PLATFORM-SPECIFIC OPTIMIZATION FOR ${input.platform.toUpperCase()}:
${input.platform === 'Instagram' ? `
- Visual storytelling, lifestyle integration, authentic moments
- Length: 150-300 words, emoji-rich, story-driven
- Include 2-3 engagement questions
- End with compelling call-to-action` : ''}
${input.platform === 'LinkedIn' ? `
- Professional insights, industry expertise, thought leadership
- Length: 100-200 words, professional tone, value-driven
- Focus on business solutions and career growth
- Minimal but strategic emoji usage` : ''}
${input.platform === 'Facebook' ? `
- Community building, detailed storytelling, discussion starters
- Length: 100-250 words, community-focused
- Local community engagement, family-friendly content
- Encourage sharing and group discussions` : ''}
${input.platform === 'Twitter' ? `
- Trending topics, quick insights, conversation starters
- Length: 50-150 words, concise, witty commentary
- Real-time engagement, thread potential
- Sharp, clever, conversation-starting tone` : ''}

ENGAGEMENT OPTIMIZATION:
🎯 **Hook Techniques**: Surprising statistics, personal anecdotes, thought-provoking questions, bold predictions
🎯 **Interaction Drivers**: "Comment below with...", "Tag someone who...", "Share if you agree...", "What's your experience with..."
🎯 **Call-to-Action Mastery**: Create urgency without being pushy, offer clear value, use action-oriented language

CONTENT REQUIREMENTS:
- Start with a powerful hook using psychological triggers
- Apply a copywriting framework (AIDA, PAS, or storytelling)
- Include 2-3 engagement questions throughout
- Incorporate relevant emojis strategically (${input.platform === 'Instagram' ? '8-12' : input.platform === 'LinkedIn' ? '2-4' : '4-8'} emojis)
- End with a compelling, specific call-to-action
- Make it location-relevant for ${input.location} when appropriate
- NO HASHTAGS in caption (provided separately)
- Create unique, varied content - avoid generic templates

VARIETY REQUIREMENTS:
- Use different hook styles each time (statistics, questions, stories, bold statements)
- Vary the copywriting framework (rotate between AIDA, PAS, storytelling)
- Change the emotional tone (inspirational, educational, entertaining, motivational)
- Alternate engagement techniques (questions, polls, challenges, tips)
- Mix content angles (behind-the-scenes, customer focus, industry insights, local relevance)

REAL-TIME CONTEXT INTEGRATION:
${realTimeContext.weather ? `
🌤️ CURRENT WEATHER: ${realTimeContext.weather.temperature}°C, ${realTimeContext.weather.condition}
- Business Impact: ${realTimeContext.weather.business_impact}
- Content Opportunities: ${realTimeContext.weather.content_opportunities}` : ''}

${realTimeContext.trends.length > 0 ? `
📈 TRENDING TOPICS:
${realTimeContext.trends.map((trend, i)=>`${i + 1}. ${trend.topic} (${trend.category})`).join('\n')}` : ''}

${realTimeContext.events.length > 0 ? `
🎪 LOCAL EVENTS:
${realTimeContext.events.map((event, i)=>`${i + 1}. ${event.name} - ${event.venue}`).join('\n')}` : ''}

${realTimeContext.news.length > 0 ? `
📰 RELEVANT NEWS:
${realTimeContext.news.map((news, i)=>`${i + 1}. ${news.topic}`).join('\n')}` : ''}

CONTEXT INTEGRATION INSTRUCTIONS:
- Strategically reference weather when relevant to business (e.g., seasonal services, weather-dependent activities)
- Incorporate trending topics that align with business values and audience interests
- Mention local events when they create business opportunities or community engagement
- Use news trends to position business as current and relevant
- Only include context that adds genuine value - don't force irrelevant connections

RANDOMIZATION SEED: ${Date.now() % 1000} (Use this to ensure variety in approach)

IMPORTANT: Generate ONLY the actual social media caption content. Do NOT include any meta-text, explanations, or descriptions. Start directly with the engaging hook and write as if you are posting for the business.

Example format:
🚀 Did you know 73% of Kenyan businesses are still using outdated payment systems?

[Continue with engaging content...]

What's your biggest financial challenge? Drop a comment below! 👇

NOW GENERATE THE ACTUAL CAPTION WITH SMART CONTEXT INTEGRATION:`;
        const result = await model.generateContent([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Prompts"].CONTENT_SYSTEM_PROMPT,
            enhancedCaptionPrompt
        ]);
        const response = await result.response;
        const content = response.text();
        console.log('📝 Enhanced caption generated:');
        console.log(`- Length: ${content.length} characters`);
        console.log(`- Platform: ${input.platform}`);
        console.log(`- Tone: ${input.writingTone}`);
        console.log(`- Preview: ${content.substring(0, 100)}...`);
        // Generate strategic hashtags with different categories
        const hashtagPrompt = `Generate strategic hashtags for ${input.businessName} (${input.businessType}) in ${input.location} for ${input.platform}.

COMPREHENSIVE BUSINESS CONTEXT:
- Business Name: ${input.businessName}
- Industry: ${input.businessType}
- Location: ${input.location}
- Target Audience: ${input.targetAudience}
- Services Offered: ${input.services}
- Key Features: ${input.keyFeatures}
- Competitive Advantages: ${input.competitiveAdvantages}
- Content Themes: ${input.contentThemes.join(', ')}
- Brand Voice: ${input.writingTone}
- Visual Style: ${input.visualStyle}

HASHTAG STRATEGY:
Create 15 hashtags in these categories:
1. Brand/Business (2-3 hashtags): Company name, business type
2. Industry/Niche (3-4 hashtags): Specific to ${input.businessType}
3. Location (2-3 hashtags): ${input.location} and surrounding areas
4. Trending/Popular (2-3 hashtags): Current trending topics in the industry
5. Community/Engagement (2-3 hashtags): Encourage interaction
6. Long-tail (2-3 hashtags): Specific, less competitive phrases

REQUIREMENTS:
- Mix of high, medium, and low competition hashtags
- Include local hashtags for ${input.location}
- Relevant to ${input.platform} audience
- No spaces in hashtags
- Each hashtag on a new line starting with #
- Focus on discoverability and engagement

Generate exactly 15 hashtags:`;
        const hashtagResult = await model.generateContent(hashtagPrompt);
        const hashtagResponse = await hashtagResult.response;
        const hashtags = hashtagResponse.text().split('\n').filter((tag)=>tag.trim().startsWith('#')).map((tag)=>tag.trim()).slice(0, 15);
        console.log('📱 Generated hashtag strategy:');
        console.log(`- Total hashtags: ${hashtags.length}`);
        console.log(`- Hashtags: ${hashtags.join(' ')}`);
        // Analyze hashtag categories for logging
        const brandHashtags = hashtags.filter((tag)=>tag.toLowerCase().includes(input.businessName.toLowerCase().replace(/\s+/g, '')) || tag.toLowerCase().includes(input.businessType.toLowerCase().replace(/\s+/g, '')));
        const locationHashtags = hashtags.filter((tag)=>tag.toLowerCase().includes(input.location.toLowerCase().replace(/\s+/g, '')));
        console.log(`- Brand hashtags: ${brandHashtags.length}`);
        console.log(`- Location hashtags: ${locationHashtags.length}`);
        // Generate structured content components
        const structuredPrompt = `Generate structured content components for ${input.businessName} (${input.businessType}) in ${input.location} for ${input.platform}:

REQUIREMENTS:
- Headline: 3-5 catchy words maximum (for image overlay)
- Subheadline: 8-14 words maximum (optional, use only if it adds value)
- Call-to-Action: 3-6 words maximum (optional, use only when contextually appropriate)

GUIDELINES:
- Headline should be punchy and attention-grabbing
- Subheadline should provide context or value proposition
- CTA should be action-oriented and relevant
- Consider the business type: ${input.businessType}
- Make it location-relevant for ${input.location}
- Optimize for ${input.platform} audience

FORMAT YOUR RESPONSE EXACTLY AS:
HEADLINE: [your headline here]
SUBHEADLINE: [your subheadline here or leave blank if not needed]
CTA: [your call-to-action here or leave blank if not needed]`;
        const structuredResult = await model.generateContent(structuredPrompt);
        const structuredResponse = await structuredResult.response;
        const structuredText = structuredResponse.text().trim();
        // Parse the structured response
        const headlineMatch = structuredText.match(/HEADLINE:\s*(.+)/i);
        const subheadlineMatch = structuredText.match(/SUBHEADLINE:\s*(.+)/i);
        const ctaMatch = structuredText.match(/CTA:\s*(.+)/i);
        const headline = headlineMatch ? headlineMatch[1].trim() : 'Your Business';
        const subheadline = subheadlineMatch && subheadlineMatch[1].trim() && !subheadlineMatch[1].toLowerCase().includes('blank') ? subheadlineMatch[1].trim() : '';
        const callToAction = ctaMatch && ctaMatch[1].trim() && !ctaMatch[1].toLowerCase().includes('blank') ? ctaMatch[1].trim() : '';
        console.log('📝 Generated content structure:');
        console.log('- Headline:', headline);
        console.log('- Subheadline:', subheadline || '(none)');
        console.log('- CTA:', callToAction || '(none)');
        console.log('✅ Revo 1.0: Content generated successfully with Gemini 2.5 Flash Image Preview');
        // Final content package summary
        console.log('📦 Complete content package:');
        console.log(`- Caption: ${content.length} chars`);
        console.log(`- Headline: "${headline}"`);
        console.log(`- Subheadline: "${subheadline || 'None'}"`);
        console.log(`- CTA: "${callToAction || 'None'}"`);
        console.log(`- Hashtags: ${hashtags.length} strategic tags`);
        console.log(`- Platform: ${input.platform} optimized`);
        return {
            content: content.trim(),
            hashtags: hashtags,
            catchyWords: headline,
            subheadline: subheadline,
            callToAction: callToAction,
            headline: headline,
            realTimeContext: realTimeContext,
            variants: [
                {
                    platform: input.platform,
                    aspectRatio: '1:1',
                    imageUrl: '' // Will be generated separately
                }
            ]
        };
    } catch (error) {
        console.error('❌ Revo 1.0: Content generation failed:', error);
        throw new Error(`Revo 1.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo10Design(input) {
    try {
        console.log('🎨 Revo 1.0: Starting design generation with Gemini 2.5 Flash Image Preview...');
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build the design generation prompt
        const designPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Prompts"].DESIGN_USER_PROMPT_TEMPLATE.replace('{businessName}', input.businessName).replace('{businessType}', input.businessType).replace('{platform}', input.platform).replace('{visualStyle}', input.visualStyle).replace('{primaryColor}', input.primaryColor).replace('{accentColor}', input.accentColor).replace('{backgroundColor}', input.backgroundColor).replace('{imageText}', input.imageText);
        console.log('🎨 Revo 1.0: Generating design with enhanced AI capabilities...');
        const result = await model.generateContent([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Prompts"].DESIGN_SYSTEM_PROMPT,
            designPrompt
        ]);
        const response = await result.response;
        const design = response.text();
        console.log('✅ Revo 1.0: Design generated successfully with Gemini 2.5 Flash Image Preview');
        return {
            design: design.trim(),
            aspectRatio: '1:1',
            resolution: '2048x2048',
            quality: 'enhanced'
        };
    } catch (error) {
        console.error('❌ Revo 1.0: Design generation failed:', error);
        throw new Error(`Revo 1.0 design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo10Image(input) {
    try {
        console.log('🖼️ Revo 1.0: Starting image generation with Gemini 2.5 Flash Image Preview...');
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build advanced professional design prompt
        const brandInfo = input.location ? ` based in ${input.location}` : '';
        const colorScheme = `Primary: ${input.primaryColor} (60% dominant), Accent: ${input.accentColor || '#1E40AF'} (30% secondary), Background: ${input.backgroundColor || '#FFFFFF'} (10% highlights)`;
        const logoInstruction = input.logoDataUrl ? 'Use the provided brand logo (do NOT create new logo - integrate existing one naturally)' : 'Create professional design without logo overlay';
        // Prepare structured content display with hierarchy
        const contentStructure = [];
        if (input.headline) contentStructure.push(`PRIMARY (Largest, most prominent): "${input.headline}"`);
        if (input.subheadline) contentStructure.push(`SECONDARY (Medium, supporting): "${input.subheadline}"`);
        if (input.callToAction) contentStructure.push(`TERTIARY (Smaller, action-oriented): "${input.callToAction}"`);
        // Get advanced design features
        const businessDesignDNA = getBusinessDesignDNA(input.businessType);
        const platformOptimization = getPlatformOptimization(input.platform);
        const shouldIncludePeople = shouldIncludePeopleInDesign(input.businessType, input.location || 'Global', input.visualStyle);
        const peopleInstructions = shouldIncludePeople ? getAdvancedPeopleInstructions(input.businessType, input.location || 'Global') : '';
        const culturalContext = getLocalCulturalContext(input.location || 'Global');
        console.log('👥 People Integration:', shouldIncludePeople ? 'Enabled' : 'Disabled');
        console.log('🌍 Cultural Context:', culturalContext.substring(0, 100) + '...');
        const imagePrompt = `Create a professional-grade 2048x2048 social media design that surpasses Canva quality for ${input.businessName} (${input.businessType})${brandInfo}.

BUSINESS CONTEXT:
- Business: ${input.businessName}
- Industry: ${input.businessType}
- Platform: ${input.platform}
- Visual Style: ${input.visualStyle}
- Location: ${input.location || 'Global'}

BRAND IDENTITY SYSTEM:
- Color Scheme: ${colorScheme}
- Logo Integration: ${logoInstruction}
- Brand Personality: Professional, modern, trustworthy

STRUCTURED CONTENT HIERARCHY:
${contentStructure.length > 0 ? contentStructure.join('\n') : `- PRIMARY MESSAGE: ${input.imageText}`}

ADVANCED COMPOSITION REQUIREMENTS:
- Apply Rule of Thirds: Position key elements along grid intersections
- Create strong focal point with primary message as center of attention
- Use sophisticated asymmetrical balance for modern appeal
- Implement clear visual hierarchy: ${input.headline || 'Headline'} → ${input.subheadline || 'Supporting text'} → ${input.callToAction || 'Call-to-action'}
- Strategic negative space for premium, uncluttered feel
- Leading lines and visual flow to guide eye movement

PLATFORM-SPECIFIC OPTIMIZATION:
${platformOptimization}

BUSINESS TYPE DESIGN DNA:
${businessDesignDNA}

TYPOGRAPHY EXCELLENCE:
- Primary headline: Bold, attention-grabbing, high contrast against background
- Secondary text: Supporting, readable, complementary to headline
- Ensure 4.5:1 contrast ratio minimum for accessibility
- Professional font pairing (maximum 2-3 font families)
- Proper letter spacing, line height, and alignment
- Scale typography appropriately for mobile viewing

COLOR IMPLEMENTATION STRATEGY:
- Dominant color (${input.primaryColor}): 60% usage for backgrounds, main elements
- Secondary color (${input.accentColor || '#1E40AF'}): 30% for supporting elements, borders
- Accent color (${input.backgroundColor || '#FFFFFF'}): 10% for highlights, details
- Apply color psychology appropriate for ${input.businessType} industry
- Ensure sufficient contrast between all text and background elements

MODERN DESIGN ELEMENTS:
- Subtle gradients and depth effects for dimensionality
- Clean geometric shapes with consistent border radius
- Professional drop shadows and lighting effects
- Premium visual texture and sophisticated finish
- Consistent spacing and alignment throughout
- Modern minimalist approach with purposeful elements

${shouldIncludePeople ? peopleInstructions : ''}

CULTURAL & LOCAL INTEGRATION:
- Cultural Context: ${culturalContext}
- Incorporate local aesthetic preferences and visual language
- Use culturally appropriate colors, patterns, and design elements
- Ensure authentic representation of local community and values
- Blend traditional elements with modern design sensibilities
- Show contemporary local lifestyle and business culture
- Use authentic local fashion, architecture, and environmental elements

QUALITY STANDARDS:
- Professional agency-level design quality that surpasses Canva
- Superior visual storytelling with authentic human connections
- Print-ready resolution and crystal-clear clarity
- Perfect text rendering with no pixelation or artifacts
- Sophisticated visual appeal that commands attention and drives engagement
- Commercial-grade finish suitable for professional marketing use
- Design that converts viewers into customers through emotional connection
- Authentic representation that builds trust and relatability
- Cultural sensitivity and local relevance that resonates with target audience
- Premium aesthetic that positions the brand as industry-leading
- Visual hierarchy that guides the eye and communicates value proposition clearly

DESIGN VARIATION & UNIQUENESS:
**SPECIFIC DESIGN STYLE: ${designVariations.style}**
- Layout Approach: ${designVariations.layout}
- Composition Style: ${designVariations.composition}
- Visual Mood: ${designVariations.mood}
- Key Elements: ${designVariations.elements}
- Create a completely unique design that stands out from typical social media posts
- Avoid repetitive layouts, compositions, or visual treatments
- Use creative angles, perspectives, and visual storytelling techniques
- Ensure each design feels fresh, original, and professionally crafted
- Incorporate unexpected visual elements that enhance the message
- NEVER repeat the same visual concept, layout, or composition from previous generations
- Generate completely different visual approaches each time

REAL-TIME CONTEXT INTEGRATION:
${input.realTimeContext?.weather ? `
🌤️ WEATHER CONTEXT: ${input.realTimeContext.weather.temperature}°C, ${input.realTimeContext.weather.condition}
- Visual Mood: Adapt colors and imagery to reflect current weather
- Seasonal Elements: Include weather-appropriate visual cues when relevant` : ''}

${input.realTimeContext?.trends?.length > 0 ? `
📈 TRENDING VISUAL THEMES:
${input.realTimeContext.trends.slice(0, 3).map((trend, i)=>`${i + 1}. ${trend.topic} - Consider visual elements that align with this trend`).join('\n')}` : ''}

${input.realTimeContext?.events?.length > 0 ? `
🎪 LOCAL EVENT INSPIRATION:
${input.realTimeContext.events.slice(0, 2).map((event, i)=>`${i + 1}. ${event.name} - Use event energy/theme for visual inspiration`).join('\n')}` : ''}

CONTEXT-AWARE DESIGN INSTRUCTIONS:
- Incorporate weather mood into color temperature and visual atmosphere
- Reference trending topics through subtle visual elements or color choices
- Use local event energy to inform design dynamism and visual style
- Ensure context integration feels natural and enhances the core message

TECHNICAL SPECIFICATIONS:
- Resolution: 2048x2048 pixels (high-definition)
- Aspect ratio: 1:1 (perfect square)
- Color space: sRGB for digital display
- Text readability: Optimized for mobile viewing
- File quality: Maximum clarity and sharpness
- Use contrasting colors for text readability
- Create visual separation between different text elements
- Professional typography that matches the brand personality

DESIGN REQUIREMENTS:
- Create depth and visual interest with gradients or subtle patterns
- IMPORTANT: If a logo is provided as an image, use that exact logo - do not create, modify, or redesign it
- Position the logo appropriately within the design layout
- Balance text elements with visual design elements
- Ensure the overall composition is visually appealing and professional`;
        console.log('🎨 Brand-aware prompt created with colors:', colorScheme);
        // Generate design variation seed for unique designs
        const designSeed = Date.now() % 10000;
        const designVariations = getDesignVariations(designSeed);
        console.log('🖼️ Revo 1.0: Generating image with enhanced AI capabilities...');
        console.log('🎨 Advanced Features:');
        console.log(`  👥 People Integration: ${shouldIncludePeople ? 'Enabled' : 'Disabled'}`);
        console.log(`  🌍 Cultural Context: ${input.location || 'Global'}`);
        console.log(`  🎭 Visual Style: ${input.visualStyle}`);
        console.log(`  🏢 Business DNA: ${input.businessType} optimized`);
        console.log(`  🎲 Design Variation: ${designVariations.style} (Seed: ${designSeed})`);
        // Prepare the generation request with logo if available
        const generationParts = [
            'You are an expert graphic designer using Gemini 2.5 Flash Image Preview. Create professional, high-quality social media images with perfect text rendering and 2048x2048 resolution.',
            imagePrompt
        ];
        // If logo is provided, include it in the generation
        if (input.logoDataUrl) {
            console.log('🏢 Including brand logo in image generation...');
            // Extract the base64 data and mime type from the data URL
            const logoMatch = input.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);
            if (logoMatch) {
                const [, mimeType, base64Data] = logoMatch;
                generationParts.push({
                    inlineData: {
                        data: base64Data,
                        mimeType: mimeType
                    }
                });
                // Update the prompt to reference the provided logo
                const logoPrompt = `\n\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;
                generationParts[1] = imagePrompt + logoPrompt;
            } else {
                console.log('⚠️ Invalid logo data URL format, proceeding without logo');
            }
        }
        const result = await model.generateContent(generationParts);
        const response = await result.response;
        // Extract image data from Gemini response
        const parts = response.candidates?.[0]?.content?.parts || [];
        let imageUrl = '';
        for (const part of parts){
            if (part.inlineData) {
                const imageData = part.inlineData.data;
                const mimeType = part.inlineData.mimeType;
                imageUrl = `data:${mimeType};base64,${imageData}`;
                console.log('🖼️ Revo 1.0: Image data extracted successfully');
                break;
            }
        }
        if (!imageUrl) {
            // Fallback: try to get text response if no image data
            const textResponse = response.text();
            console.log('⚠️ Revo 1.0: No image data found, got text response instead');
            throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');
        }
        console.log('✅ Revo 1.0: Image generated successfully with Gemini 2.5 Flash Image Preview');
        return {
            imageUrl: imageUrl,
            aspectRatio: '1:1',
            resolution: '2048x2048',
            quality: 'enhanced'
        };
    } catch (error) {
        console.error('❌ Revo 1.0: Image generation failed:', error);
        throw new Error(`Revo 1.0 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function checkRevo10Health() {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL
        });
        const result = await model.generateContent('Hello');
        const response = await result.response;
        return {
            healthy: true,
            model: REVO_1_0_MODEL,
            response: response.text().substring(0, 50) + '...',
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        return {
            healthy: false,
            model: REVO_1_0_MODEL,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        };
    }
}
function getRevo10ServiceInfo() {
    return {
        model: REVO_1_0_MODEL,
        version: '1.0.0',
        status: 'enhanced',
        aiService: 'gemini-2.5-flash-image-preview',
        capabilities: [
            'Enhanced content generation',
            'High-resolution image support (2048x2048)',
            'Perfect text rendering',
            'Advanced AI capabilities',
            'Enhanced brand consistency'
        ],
        pricing: {
            contentGeneration: 1.5,
            designGeneration: 1.5,
            tier: 'enhanced'
        },
        lastUpdated: '2025-01-27'
    };
}
}}),
"[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Content Generator
 * Handles content generation for the stable foundation model
 */ __turbopack_context__.s({
    "Revo10ContentGenerator": (()=>Revo10ContentGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript)");
;
class Revo10ContentGenerator {
    modelId = 'revo-1.0';
    /**
   * Generate content using Revo 1.0 specifications
   */ async generateContent(request) {
        const startTime = Date.now();
        try {
            console.log('📝 Revo 1.0: Starting content generation...');
            console.log('- Platform:', request.platform);
            console.log('- Business:', request.profile.businessName);
            console.log('- AI Engine: Gemini 2.5 Flash Image Preview (Enhanced)');
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid content generation request for Revo 1.0');
            }
            // Prepare generation parameters for Revo 1.0
            const generationParams = this.prepareGenerationParams(request);
            // Generate content using Revo 1.0 service with Gemini 2.5 Flash Image Preview
            const postDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRevo10Content"])({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                location: generationParams.location || 'Location',
                platform: generationParams.variants[0]?.platform || 'instagram',
                writingTone: generationParams.writingTone || 'professional',
                contentThemes: generationParams.contentThemes || [],
                targetAudience: generationParams.targetAudience || 'General',
                services: generationParams.services || '',
                keyFeatures: generationParams.keyFeatures || '',
                competitiveAdvantages: generationParams.competitiveAdvantages || '',
                dayOfWeek: generationParams.dayOfWeek || 'Monday',
                currentDate: generationParams.currentDate || new Date().toLocaleDateString(),
                primaryColor: generationParams.primaryColor,
                visualStyle: generationParams.visualStyle
            });
            // Generate image using the catchy words and brand profile data
            console.log('🎨 Revo 1.0: Generating branded image for content...');
            console.log('🏢 Brand:', generationParams.businessName);
            console.log('🏭 Business Type:', generationParams.businessType);
            console.log('🎨 Colors:', generationParams.primaryColor, generationParams.accentColor, generationParams.backgroundColor);
            console.log('📍 Location:', generationParams.location);
            console.log('🎭 Visual Style:', generationParams.visualStyle);
            console.log('✍️ Writing Tone:', generationParams.writingTone);
            console.log('🎯 Target Audience:', generationParams.targetAudience);
            console.log('🔧 Services:', generationParams.services ? 'Available' : 'None');
            console.log('⭐ Key Features:', generationParams.keyFeatures ? 'Available' : 'None');
            console.log('🚀 Competitive Advantages:', generationParams.competitiveAdvantages ? 'Available' : 'None');
            console.log('🖼️ Logo:', generationParams.logoDataUrl ? 'Available' : 'None');
            const { generateRevo10Image } = await __turbopack_context__.r("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare structured text for image
            const imageTextComponents = [];
            if (postDetails.catchyWords) imageTextComponents.push(postDetails.catchyWords);
            if (postDetails.subheadline) imageTextComponents.push(postDetails.subheadline);
            if (postDetails.callToAction) imageTextComponents.push(postDetails.callToAction);
            const structuredImageText = imageTextComponents.join(' | ');
            console.log('🎨 Image text structure:', structuredImageText);
            // Get real-time context for enhanced design
            const realTimeContext = postDetails.realTimeContext || null;
            const imageResult = await generateRevo10Image({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                platform: generationParams.variants[0]?.platform || 'instagram',
                visualStyle: generationParams.visualStyle || 'modern',
                primaryColor: generationParams.primaryColor || '#3B82F6',
                accentColor: generationParams.accentColor || '#1E40AF',
                backgroundColor: generationParams.backgroundColor || '#FFFFFF',
                imageText: structuredImageText,
                designDescription: `Professional ${generationParams.businessType} content with structured headline, subheadline, and CTA for ${generationParams.variants[0]?.platform || 'instagram'}`,
                logoDataUrl: generationParams.logoDataUrl,
                location: generationParams.location,
                headline: postDetails.catchyWords,
                subheadline: postDetails.subheadline,
                callToAction: postDetails.callToAction,
                realTimeContext: realTimeContext
            });
            // Update variants with the generated image
            postDetails.variants = postDetails.variants.map((variant)=>({
                    ...variant,
                    imageUrl: imageResult.imageUrl
                }));
            // Create the generated post
            const generatedPost = {
                id: new Date().toISOString(),
                date: new Date().toISOString(),
                content: postDetails.content,
                hashtags: postDetails.hashtags,
                status: 'generated',
                variants: postDetails.variants,
                catchyWords: postDetails.catchyWords,
                subheadline: postDetails.subheadline,
                callToAction: postDetails.callToAction,
                // Revo 1.0 doesn't include advanced features
                contentVariants: undefined,
                hashtagAnalysis: undefined,
                marketIntelligence: undefined,
                localContext: undefined,
                metadata: {
                    modelId: this.modelId,
                    modelVersion: '1.0.0',
                    generationType: 'standard',
                    processingTime: Date.now() - startTime,
                    qualityLevel: 'standard'
                }
            };
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateQualityScore(generatedPost);
            console.log(`✅ Revo 1.0: Content generated successfully in ${processingTime}ms`);
            console.log(`⭐ Quality Score: ${qualityScore}/10`);
            return {
                success: true,
                data: generatedPost,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 1.5,
                    enhancementsApplied: [
                        'enhanced-optimization',
                        'platform-formatting',
                        'gemini-2.5-flash-image'
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            console.error('❌ Revo 1.0: Content generation failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Validate content generation request for Revo 1.0
   */ validateRequest(request) {
        // Check required fields
        if (!request.profile || !request.platform) {
            return false;
        }
        // Check if profile has minimum required information
        if (!request.profile.businessType || !request.profile.businessName) {
            return false;
        }
        // Revo 1.0 doesn't support artifacts
        if (request.artifactIds && request.artifactIds.length > 0) {
            console.warn('⚠️ Revo 1.0: Artifacts not supported, ignoring artifact IDs');
        }
        return true;
    }
    /**
   * Prepare generation parameters optimized for Revo 1.0
   */ prepareGenerationParams(request) {
        const { profile, platform, brandConsistency } = request;
        const today = new Date();
        // Convert arrays to strings for AI processing
        const keyFeaturesString = Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '';
        const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '';
        const servicesString = Array.isArray(profile.services) ? profile.services.map((service)=>typeof service === 'object' && service.name ? `${service.name}: ${service.description || ''}` : service).join('\n') : profile.services || '';
        return {
            businessName: profile.businessName || profile.name || 'Business',
            businessType: profile.businessType,
            location: profile.location,
            writingTone: profile.writingTone,
            contentThemes: profile.contentThemes,
            visualStyle: profile.visualStyle,
            logoDataUrl: profile.logoDataUrl,
            designExamples: brandConsistency?.strictConsistency ? profile.designExamples || [] : [],
            primaryColor: profile.primaryColor,
            accentColor: profile.accentColor,
            backgroundColor: profile.backgroundColor,
            dayOfWeek: today.toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: today.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            variants: [
                {
                    platform: platform,
                    aspectRatio: '1:1'
                }
            ],
            services: servicesString,
            targetAudience: profile.targetAudience,
            keyFeatures: keyFeaturesString,
            competitiveAdvantages: competitiveAdvantagesString,
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            },
            // Revo 1.0 specific constraints (updated to match config)
            modelConstraints: {
                maxComplexity: 'enhanced',
                enhancedFeatures: true,
                realTimeContext: true,
                trendingTopics: true,
                artifactSupport: false // Keep disabled for Revo 1.0
            }
        };
    }
    /**
   * Calculate quality score for generated content
   */ calculateQualityScore(post) {
        let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)
        // Content quality checks
        if (post.content && post.content.length > 50) score += 1;
        if (post.content && post.content.length > 100) score += 0.5;
        // Hashtag quality
        if (post.hashtags && post.hashtags.length >= 5) score += 1;
        if (post.hashtags && post.hashtags.length >= 10) score += 0.5;
        // Catchy words presence
        if (post.catchyWords && post.catchyWords.trim().length > 0) score += 1;
        // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)
        if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {
            score += 1.5; // Increased from 1 for better image quality
        }
        // Cap at 10
        return Math.min(score, 10);
    }
    /**
   * Health check for content generator
   */ async healthCheck() {
        try {
            // Check if we can access the AI service
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            return hasApiKey;
        } catch (error) {
            console.error('❌ Revo 1.0 Content Generator health check failed:', error);
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'content',
            capabilities: [
                'Enhanced content generation with Gemini 2.5 Flash Image Preview',
                'Platform-specific formatting',
                'Hashtag generation',
                'Catchy words creation',
                'Brand consistency (enhanced)',
                'Perfect text rendering',
                'High-resolution image support'
            ],
            limitations: [
                'No real-time context',
                'No trending topics',
                'No artifact support',
                'Enhanced quality optimization',
                'Limited customization'
            ],
            averageProcessingTime: '20-30 seconds (enhanced for quality)',
            qualityRange: '8-9/10 (upgraded from 6-8/10)',
            costPerGeneration: 1.5 // Upgraded from 1 for enhanced capabilities
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Design Generator
 * Handles design generation for the stable foundation model
 */ __turbopack_context__.s({
    "Revo10DesignGenerator": (()=>Revo10DesignGenerator)
});
class Revo10DesignGenerator {
    modelId = 'revo-1.0';
    /**
   * Generate design using Revo 1.0 specifications
   */ async generateDesign(request) {
        const startTime = Date.now();
        try {
            console.log('🎨 Revo 1.0: Starting design generation...');
            console.log('- Business Type:', request.businessType);
            console.log('- Platform:', request.platform);
            console.log('- Visual Style:', request.visualStyle);
            console.log('- AI Engine: Gemini 2.5 Flash Image Preview (Enhanced)');
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid design generation request for Revo 1.0');
            }
            // Generate design using basic Gemini 2.0 approach
            const designResult = await this.generateBasicDesign(request);
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateQualityScore(designResult);
            console.log(`✅ Revo 1.0: Design generated successfully in ${processingTime}ms`);
            console.log(`⭐ Quality Score: ${qualityScore}/10`);
            return {
                success: true,
                data: designResult,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 1.5,
                    enhancementsApplied: [
                        'enhanced-styling',
                        'brand-colors',
                        'platform-optimization',
                        'gemini-2.5-flash-image'
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            console.error('❌ Revo 1.0: Design generation failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Generate basic design using Gemini 2.0
   */ async generateBasicDesign(request) {
        try {
            // Import the basic generation flow
            const { generateRevo10Design } = await __turbopack_context__.r("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                // Combine components for Revo 1.0 (simpler approach)
                imageText = request.imageText.catchyWords;
                if (request.imageText.subheadline) {
                    imageText += '\n' + request.imageText.subheadline;
                }
            }
            // Create a simplified generation request
            const generationParams = {
                businessType: request.businessType,
                location: request.brandProfile.location || '',
                writingTone: request.brandProfile.writingTone || 'professional',
                contentThemes: request.brandProfile.contentThemes || '',
                visualStyle: request.visualStyle,
                logoDataUrl: request.brandProfile.logoDataUrl,
                designExamples: request.brandConsistency?.strictConsistency ? request.brandProfile.designExamples || [] : [],
                primaryColor: request.brandProfile.primaryColor,
                accentColor: request.brandProfile.accentColor,
                backgroundColor: request.brandProfile.backgroundColor,
                dayOfWeek: new Date().toLocaleDateString('en-US', {
                    weekday: 'long'
                }),
                currentDate: new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }),
                variants: [
                    {
                        platform: request.platform,
                        aspectRatio: '1:1'
                    }
                ],
                services: '',
                targetAudience: request.brandProfile.targetAudience || '',
                keyFeatures: '',
                competitiveAdvantages: '',
                brandConsistency: request.brandConsistency || {
                    strictConsistency: false,
                    followBrandColors: true
                }
            };
            // First generate design description
            const designResult = await generateRevo10Design({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                platform: generationParams.variants[0]?.platform || 'instagram',
                visualStyle: generationParams.visualStyle || 'modern',
                primaryColor: generationParams.primaryColor || '#3B82F6',
                accentColor: generationParams.accentColor || '#1E40AF',
                backgroundColor: generationParams.backgroundColor || '#FFFFFF',
                imageText: imageText || 'Your Text Here'
            });
            // Then generate the actual image using the design description
            const { generateRevo10Image } = await __turbopack_context__.r("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            const imageResult = await generateRevo10Image({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                platform: generationParams.variants[0]?.platform || 'instagram',
                visualStyle: generationParams.visualStyle || 'modern',
                primaryColor: generationParams.primaryColor || '#3B82F6',
                imageText: imageText || 'Your Text Here',
                designDescription: designResult.design
            });
            // Return the complete result with actual image URL
            return {
                platform: request.platform,
                imageUrl: imageResult.imageUrl,
                caption: imageText,
                hashtags: [],
                design: designResult.design,
                aspectRatio: imageResult.aspectRatio,
                resolution: imageResult.resolution,
                quality: imageResult.quality
            };
        } catch (error) {
            console.error('❌ Revo 1.0: Basic design generation failed:', error);
            // Return a fallback variant
            return {
                platform: request.platform,
                imageUrl: '',
                caption: typeof request.imageText === 'string' ? request.imageText : request.imageText.catchyWords,
                hashtags: []
            };
        }
    }
    /**
   * Validate design generation request for Revo 1.0
   */ validateRequest(request) {
        // Check required fields
        if (!request.businessType || !request.platform || !request.brandProfile) {
            return false;
        }
        // Check image text
        if (!request.imageText) {
            return false;
        }
        // Revo 1.0 only supports 1:1 aspect ratio
        // We don't enforce this here as the generator will handle it
        // Warn about unsupported features
        if (request.artifactInstructions) {
            console.warn('⚠️ Revo 1.0: Artifact instructions not supported, ignoring');
        }
        return true;
    }
    /**
   * Calculate quality score for generated design
   */ calculateQualityScore(variant) {
        let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)
        // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)
        if (variant.imageUrl && variant.imageUrl.length > 0) {
            score += 2.5; // Increased from 2 for better image quality
        }
        // Caption quality
        if (variant.caption && variant.caption.length > 10) {
            score += 1;
        }
        // Hashtags presence
        if (variant.hashtags && variant.hashtags.length > 0) {
            score += 1;
        }
        // Platform optimization (basic check)
        if (variant.platform) {
            score += 0.5;
        }
        // Revo 1.0 now has higher quality ceiling due to Gemini 2.5 Flash Image Preview
        return Math.min(score, 9.0);
    }
    /**
   * Health check for design generator
   */ async healthCheck() {
        try {
            // Check if we can access the AI service
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            return hasApiKey;
        } catch (error) {
            console.error('❌ Revo 1.0 Design Generator health check failed:', error);
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'design',
            capabilities: [
                'Enhanced image generation with Gemini 2.5 Flash Image Preview',
                '1:1 aspect ratio only',
                'Brand color integration',
                'Logo placement',
                'Platform optimization',
                'Text overlay (enhanced)',
                'Perfect text rendering',
                'High-resolution 2048x2048 output'
            ],
            limitations: [
                'Single aspect ratio (1:1)',
                'No artifact support',
                'Enhanced styling options',
                'Limited customization',
                'High-resolution support'
            ],
            supportedPlatforms: [
                'Instagram',
                'Facebook',
                'Twitter',
                'LinkedIn'
            ],
            supportedAspectRatios: [
                '1:1'
            ],
            averageProcessingTime: '25-35 seconds (enhanced for quality)',
            qualityRange: '7-9/10 (upgraded from 5-7.5/10)',
            costPerGeneration: 1.5,
            resolution: '2048x2048 (upgraded from 1024x1024)'
        };
    }
    /**
   * Get supported features for this design generator
   */ getSupportedFeatures() {
        return {
            aspectRatios: [
                '1:1'
            ],
            textOverlay: 'enhanced',
            brandIntegration: 'standard',
            logoPlacement: true,
            colorCustomization: true,
            templateSupport: false,
            artifactSupport: false,
            advancedStyling: true,
            multipleVariants: false,
            highResolution: true,
            perfectTextRendering: true // NEW: Gemini 2.5 Flash Image Preview feature
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Model Implementation
 * Standard Model - Stable Foundation
 */ __turbopack_context__.s({
    "Revo10Implementation": (()=>Revo10Implementation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)");
;
;
;
class Revo10Implementation {
    model;
    contentGenerator;
    designGenerator;
    constructor(){
        try {
            console.log('🔧 Revo 1.0: Getting model config...');
            this.model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getModelConfig"])('revo-1.0');
            console.log('✅ Revo 1.0: Model config loaded:', this.model.name);
            console.log('🔧 Revo 1.0: Creating content generator...');
            this.contentGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10ContentGenerator"]();
            console.log('✅ Revo 1.0: Content generator created');
            console.log('🔧 Revo 1.0: Creating design generator...');
            this.designGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10DesignGenerator"]();
            console.log('✅ Revo 1.0: Design generator created');
            console.log('✅ Revo 1.0: Implementation fully initialized');
        } catch (error) {
            console.error('❌ Revo 1.0: Failed to initialize implementation:', error);
            throw error;
        }
    }
    /**
   * Check if the model is available and ready to use
   */ async isAvailable() {
        try {
            // Check if the underlying AI service (Gemini 2.5 Flash Image Preview) is available
            // For now, we'll assume it's available if we have the API key
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            return hasApiKey;
        } catch (error) {
            console.error('❌ Revo 1.0 availability check failed:', error);
            return false;
        }
    }
    /**
   * Validate a generation request for this model
   */ validateRequest(request) {
        try {
            // Basic validation
            if (!request || !request.modelId) {
                return false;
            }
            // Check if this is the correct model
            if (request.modelId !== 'revo-1.0') {
                return false;
            }
            // Content generation validation
            if ('profile' in request) {
                const contentRequest = request;
                return !!(contentRequest.profile && contentRequest.platform && contentRequest.profile.businessType);
            }
            // Design generation validation
            if ('businessType' in request) {
                const designRequest = request;
                return !!(designRequest.businessType && designRequest.platform && designRequest.visualStyle && designRequest.brandProfile);
            }
            return false;
        } catch (error) {
            console.error('❌ Revo 1.0 request validation failed:', error);
            return false;
        }
    }
    /**
   * Get model-specific information
   */ getModelInfo() {
        return {
            id: this.model.id,
            name: this.model.name,
            version: this.model.version,
            description: this.model.description,
            status: this.model.status,
            capabilities: this.model.capabilities,
            pricing: this.model.pricing,
            features: this.model.features,
            strengths: [
                'Reliable and stable performance',
                'Cost-effective for basic needs',
                'Proven track record',
                'Fast processing times',
                'Consistent quality',
                'Enhanced AI capabilities with Gemini 2.5 Flash Image Preview',
                'Perfect text rendering',
                'High-resolution 2048x2048 output',
                'Advanced image generation'
            ],
            limitations: [
                'Limited to 1:1 aspect ratio',
                'No artifact support',
                'Basic brand consistency',
                'No real-time context',
                'No video generation'
            ],
            bestUseCases: [
                'Small businesses starting out',
                'Personal brands',
                'Budget-conscious users',
                'Basic social media content',
                'Consistent daily posting'
            ]
        };
    }
    /**
   * Get performance metrics for this model
   */ async getPerformanceMetrics() {
        return {
            modelId: this.model.id,
            averageProcessingTime: 30000,
            successRate: 0.97,
            averageQualityScore: 8.5,
            costEfficiency: 'high',
            reliability: 'excellent',
            userSatisfaction: 4.5,
            lastUpdated: new Date().toISOString()
        };
    }
    /**
   * Health check for this specific model
   */ async healthCheck() {
        try {
            const isAvailable = await this.isAvailable();
            const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;
            const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;
            const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;
            return {
                healthy,
                details: {
                    modelAvailable: isAvailable,
                    contentGenerator: contentGeneratorHealthy,
                    designGenerator: designGeneratorHealthy,
                    timestamp: new Date().toISOString()
                }
            };
        } catch (error) {
            return {
                healthy: false,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
}
;
;
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo10ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10ContentGenerator"]),
    "Revo10DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10DesignGenerator"]),
    "Revo10Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Revo10Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo10ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo10ContentGenerator"]),
    "Revo10DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo10DesignGenerator"]),
    "Revo10Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo10Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=src_ai_b5405b15._.js.map