{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/capabilities.ts"], "sourcesContent": ["/**\n * Model Capabilities Configuration\n * Defines what each model version can do\n */\n\nimport type { ModelCapabilities, RevoModelId } from '../types/model-types';\nimport type { Platform } from '@/lib/types';\n\n// Define capabilities for each model version\nexport const modelCapabilities: Record<RevoModelId, ModelCapabilities> = {\n  'revo-1.0': {\n    // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Not supported in 1.0\n    enhancedFeatures: true, // Upgraded from false\n    artifactSupport: false, // Basic model doesn't support artifacts\n    aspectRatios: ['1:1'], // Only square images\n    maxQuality: 9, // Upgraded from 7 for Gemini 2.5 Flash Image Preview\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Enhanced brand consistency\n    realTimeContext: true, // Now enabled for better context\n    perfectTextRendering: true, // NEW: Gemini 2.5 Flash Image Preview feature\n    highResolution: true // NEW: 2048x2048 support\n  },\n\n  'revo-1.5': {\n    // Enhanced model with advanced features\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Video coming in 2.0\n    enhancedFeatures: true,\n    artifactSupport: true, // Full artifact support\n    aspectRatios: ['1:1', '16:9', '9:16'], // Multiple aspect ratios\n    maxQuality: 8, // Superior quality\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Advanced brand consistency\n    realTimeContext: true // Real-time context and trends\n  },\n\n\n\n  'revo-2.0': {\n    // Premium Next-Gen AI model\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Focus on premium image generation\n    enhancedFeatures: true,\n    artifactSupport: true, // Premium artifact support\n    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4'], // All aspect ratios\n    maxQuality: 10, // Maximum quality with native image generation\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Perfect brand consistency with character consistency\n    realTimeContext: true, // Premium real-time features\n    characterConsistency: true, // NEW: Maintain character consistency across images\n    intelligentEditing: true, // NEW: Inpainting, outpainting, targeted edits\n    multimodalReasoning: true // NEW: Advanced visual context understanding\n  }\n};\n\n// Capability comparison matrix\nexport const capabilityMatrix = {\n  contentGeneration: {\n    'revo-1.0': 'enhanced', // Upgraded from standard\n    'revo-1.5': 'enhanced',\n    'revo-2.0': 'premium'\n  },\n  designGeneration: {\n    'revo-1.0': 'enhanced', // Upgraded from basic\n    'revo-1.5': 'enhanced',\n    'revo-2.0': 'premium'\n  },\n  videoGeneration: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'none'\n  },\n  artifactSupport: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'full',\n    'revo-2.0': 'premium'\n  },\n  brandConsistency: {\n    'revo-1.0': 'enhanced', // Upgraded from basic\n    'revo-1.5': 'advanced',\n    'revo-2.0': 'perfect'\n  },\n  characterConsistency: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'advanced'\n  },\n  intelligentEditing: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'advanced'\n  }\n} as const;\n\n// Feature availability by model\nexport const featureAvailability = {\n  // Content features\n  hashtagGeneration: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  catchyWords: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  subheadlines: ['revo-1.5', 'revo-2.0'],\n  callToAction: ['revo-1.5', 'revo-2.0'],\n  contentVariants: ['revo-1.5', 'revo-2.0'],\n\n  // Design features\n  logoIntegration: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  brandColors: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  designExamples: ['revo-1.5', 'revo-2.0'],\n  textOverlay: ['revo-1.5', 'revo-2.0'],\n  multipleAspectRatios: ['revo-1.5', 'revo-2.0'],\n\n  // Advanced features\n  realTimeContext: ['revo-1.5', 'revo-2.0'],\n  trendingTopics: ['revo-1.5', 'revo-2.0'],\n  marketIntelligence: ['revo-1.5', 'revo-2.0'],\n  competitorAnalysis: ['revo-2.0'],\n\n  // Revo 2.0 exclusive features\n  characterConsistency: ['revo-2.0'],\n  intelligentEditing: ['revo-2.0'],\n  inpainting: ['revo-2.0'],\n  outpainting: ['revo-2.0'],\n  multimodalReasoning: ['revo-2.0'],\n\n  // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)\n  perfectTextRendering: ['revo-1.0', 'revo-2.0'],\n  highResolution: ['revo-1.0', 'revo-2.0'],\n\n  // Artifact features\n  artifactReference: ['revo-1.5'],\n  exactUseArtifacts: ['revo-1.5'],\n  textOverlayArtifacts: ['revo-1.5']\n} as const;\n\n// Platform-specific capabilities\nexport const platformCapabilities = {\n  Instagram: {\n    'revo-1.0': {\n      aspectRatios: ['1:1'],\n      maxQuality: 7,\n      features: ['basic-design', 'hashtags']\n    },\n    'revo-1.5': {\n      aspectRatios: ['1:1', '9:16'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'hashtags', 'stories', 'reels-ready']\n    }\n  },\n  Facebook: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'page-posts']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'page-posts', 'stories']\n    }\n  },\n  Twitter: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'tweets']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'tweets', 'threads']\n    }\n  },\n  LinkedIn: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'professional-posts']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'professional-posts', 'articles']\n    }\n  }\n} as const;\n\n// Utility functions\nexport function hasCapability(modelId: RevoModelId, capability: keyof ModelCapabilities): boolean {\n  return modelCapabilities[modelId][capability] as boolean;\n}\n\nexport function getCapabilityLevel(modelId: RevoModelId, capability: keyof typeof capabilityMatrix): string {\n  return capabilityMatrix[capability][modelId];\n}\n\nexport function hasFeature(modelId: RevoModelId, feature: keyof typeof featureAvailability): boolean {\n  return featureAvailability[feature].includes(modelId);\n}\n\nexport function getModelsByFeature(feature: keyof typeof featureAvailability): RevoModelId[] {\n  return [...featureAvailability[feature]] as RevoModelId[];\n}\n\nexport function getPlatformCapabilities(modelId: RevoModelId, platform: Platform) {\n  return platformCapabilities[platform]?.[modelId] || null;\n}\n\nexport function getMaxQualityForPlatform(modelId: RevoModelId, platform: Platform): number {\n  const platformCaps = getPlatformCapabilities(modelId, platform);\n  return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;\n}\n\nexport function getSupportedAspectRatios(modelId: RevoModelId, platform?: Platform): string[] {\n  if (platform) {\n    const platformCaps = getPlatformCapabilities(modelId, platform);\n    return platformCaps?.aspectRatios ? [...platformCaps.aspectRatios] : [...modelCapabilities[modelId].aspectRatios];\n  }\n  return [...modelCapabilities[modelId].aspectRatios];\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAMM,MAAM,oBAA4D;IACvE,YAAY;QACV,yEAAyE;QACzE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;SAAM;QACrB,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;QACtB,gBAAgB,KAAK,yBAAyB;IAChD;IAEA,YAAY;QACV,wCAAwC;QACxC,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAQ;SAAO;QACrC,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB,KAAK,+BAA+B;IACvD;IAIA,YAAY;QACV,4BAA4B;QAC5B,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;SAAM;QACnD,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;QACtB,oBAAoB;QACpB,qBAAqB,KAAK,6CAA6C;IACzE;AACF;AAGO,MAAM,mBAAmB;IAC9B,mBAAmB;QACjB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,kBAAkB;QAChB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,iBAAiB;QACf,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,iBAAiB;QACf,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,kBAAkB;QAChB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,sBAAsB;QACpB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,oBAAoB;QAClB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,mBAAmB;IACnB,mBAAmB;QAAC;QAAY;QAAY;KAAW;IACvD,aAAa;QAAC;QAAY;QAAY;KAAW;IACjD,cAAc;QAAC;QAAY;KAAW;IACtC,cAAc;QAAC;QAAY;KAAW;IACtC,iBAAiB;QAAC;QAAY;KAAW;IAEzC,kBAAkB;IAClB,iBAAiB;QAAC;QAAY;QAAY;KAAW;IACrD,aAAa;QAAC;QAAY;QAAY;KAAW;IACjD,gBAAgB;QAAC;QAAY;KAAW;IACxC,aAAa;QAAC;QAAY;KAAW;IACrC,sBAAsB;QAAC;QAAY;KAAW;IAE9C,oBAAoB;IACpB,iBAAiB;QAAC;QAAY;KAAW;IACzC,gBAAgB;QAAC;QAAY;KAAW;IACxC,oBAAoB;QAAC;QAAY;KAAW;IAC5C,oBAAoB;QAAC;KAAW;IAEhC,8BAA8B;IAC9B,sBAAsB;QAAC;KAAW;IAClC,oBAAoB;QAAC;KAAW;IAChC,YAAY;QAAC;KAAW;IACxB,aAAa;QAAC;KAAW;IACzB,qBAAqB;QAAC;KAAW;IAEjC,uEAAuE;IACvE,sBAAsB;QAAC;QAAY;KAAW;IAC9C,gBAAgB;QAAC;QAAY;KAAW;IAExC,oBAAoB;IACpB,mBAAmB;QAAC;KAAW;IAC/B,mBAAmB;QAAC;KAAW;IAC/B,sBAAsB;QAAC;KAAW;AACpC;AAGO,MAAM,uBAAuB;IAClC,WAAW;QACT,YAAY;YACV,cAAc;gBAAC;aAAM;YACrB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAW;QACxC;QACA,YAAY;YACV,cAAc;gBAAC;gBAAO;aAAO;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAY;gBAAW;aAAc;QACrE;IACF;IACA,UAAU;QACR,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAa;QAC1C;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAc;aAAU;QACxD;IACF;IACA,SAAS;QACP,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAS;QACtC;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAU;aAAU;QACpD;IACF;IACA,UAAU;QACR,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAqB;QAClD;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAsB;aAAW;QACjE;IACF;AACF;AAGO,SAAS,cAAc,OAAoB,EAAE,UAAmC;IACrF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,WAAW;AAC/C;AAEO,SAAS,mBAAmB,OAAoB,EAAE,UAAyC;IAChG,OAAO,gBAAgB,CAAC,WAAW,CAAC,QAAQ;AAC9C;AAEO,SAAS,WAAW,OAAoB,EAAE,OAAyC;IACxF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/C;AAEO,SAAS,mBAAmB,OAAyC;IAC1E,OAAO;WAAI,mBAAmB,CAAC,QAAQ;KAAC;AAC1C;AAEO,SAAS,wBAAwB,OAAoB,EAAE,QAAkB;IAC9E,OAAO,oBAAoB,CAAC,SAAS,EAAE,CAAC,QAAQ,IAAI;AACtD;AAEO,SAAS,yBAAyB,OAAoB,EAAE,QAAkB;IAC/E,MAAM,eAAe,wBAAwB,SAAS;IACtD,OAAO,cAAc,cAAc,iBAAiB,CAAC,QAAQ,CAAC,UAAU;AAC1E;AAEO,SAAS,yBAAyB,OAAoB,EAAE,QAAmB;IAChF,IAAI,UAAU;QACZ,MAAM,eAAe,wBAAwB,SAAS;QACtD,OAAO,cAAc,eAAe;eAAI,aAAa,YAAY;SAAC,GAAG;eAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY;SAAC;IACnH;IACA,OAAO;WAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY;KAAC;AACrD", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/pricing.ts"], "sourcesContent": ["/**\n * Model Pricing Configuration\n * Defines credit costs and pricing tiers for each model\n */\n\nimport type { ModelPricing, RevoModelId } from '../types/model-types';\n\n// Pricing configuration for each model\nexport const modelPricing: Record<RevoModelId, ModelPricing> = {\n  'revo-1.0': {\n    creditsPerGeneration: 1.5, // Slightly increased from 1 for enhanced capabilities\n    creditsPerDesign: 1.5, // Slightly increased from 1 for enhanced capabilities\n    creditsPerVideo: 0, // Video not supported\n    tier: 'enhanced' // Upgraded from basic\n  },\n\n  'revo-1.5': {\n    creditsPerGeneration: 2,\n    creditsPerDesign: 2,\n    creditsPerVideo: 0, // Video not supported yet\n    tier: 'premium'\n  },\n\n\n\n  'revo-2.0': {\n    creditsPerGeneration: 5,\n    creditsPerDesign: 5,\n    creditsPerVideo: 0, // Focus on premium image generation\n    tier: 'premium'\n  }\n};\n\n// Pricing tiers and their characteristics\nexport const pricingTiers = {\n  basic: {\n    name: 'Basic',\n    description: 'Reliable and cost-effective',\n    maxCreditsPerGeneration: 2,\n    features: [\n      'Standard quality generation',\n      'Basic brand consistency',\n      'Core platform support',\n      'Standard processing speed'\n    ],\n    recommendedFor: [\n      'Small businesses',\n      'Personal brands',\n      'Budget-conscious users',\n      'Basic content needs'\n    ]\n  },\n  premium: {\n    name: 'Premium',\n    description: 'Enhanced features and quality',\n    maxCreditsPerGeneration: 10,\n    features: [\n      'Enhanced quality generation',\n      'Advanced brand consistency',\n      'Full platform support',\n      'Artifact integration',\n      'Real-time context',\n      'Trending topics',\n      'Multiple aspect ratios'\n    ],\n    recommendedFor: [\n      'Growing businesses',\n      'Marketing agencies',\n      'Content creators',\n      'Professional brands'\n    ]\n  },\n  enterprise: {\n    name: 'Enterprise',\n    description: 'Maximum quality and features',\n    maxCreditsPerGeneration: 20,\n    features: [\n      'Premium quality generation',\n      '4K resolution support',\n      'Perfect text rendering',\n      'Advanced style controls',\n      'Priority processing',\n      'Dedicated support',\n      'Custom integrations'\n    ],\n    recommendedFor: [\n      'Large enterprises',\n      'Premium brands',\n      'High-volume users',\n      'Quality-focused campaigns'\n    ]\n  }\n} as const;\n\n// Credit packages and their values\nexport const creditPackages = {\n  starter: {\n    name: 'Starter Pack',\n    credits: 50,\n    price: 9.99,\n    pricePerCredit: 0.20,\n    bestFor: 'revo-1.0',\n    estimatedGenerations: {\n      'revo-1.0': 50,\n      'revo-1.5': 25,\n      'imagen-4': 5\n    }\n  },\n  professional: {\n    name: 'Professional Pack',\n    credits: 200,\n    price: 29.99,\n    pricePerCredit: 0.15,\n    bestFor: 'revo-1.5',\n    estimatedGenerations: {\n      'revo-1.0': 200,\n      'revo-1.5': 100,\n      'imagen-4': 20\n    }\n  },\n  business: {\n    name: 'Business Pack',\n    credits: 500,\n    price: 59.99,\n    pricePerCredit: 0.12,\n    bestFor: 'imagen-4',\n    estimatedGenerations: {\n      'revo-1.0': 500,\n      'revo-1.5': 250,\n      'imagen-4': 50\n    }\n  },\n  enterprise: {\n    name: 'Enterprise Pack',\n    credits: 1000,\n    price: 99.99,\n    pricePerCredit: 0.10,\n    bestFor: 'imagen-4',\n    estimatedGenerations: {\n      'revo-1.0': 1000,\n      'revo-1.5': 500,\n      'revo-2.0': 200,\n      'imagen-4': 100\n    }\n  }\n} as const;\n\n// Usage-based pricing calculations\nexport const usageCalculations = {\n  // Calculate cost for a specific generation request\n  calculateGenerationCost(modelId: RevoModelId, type: 'content' | 'design' | 'video' = 'content'): number {\n    const pricing = modelPricing[modelId];\n\n    switch (type) {\n      case 'content':\n        return pricing.creditsPerGeneration;\n      case 'design':\n        return pricing.creditsPerDesign;\n      case 'video':\n        return pricing.creditsPerVideo || 0;\n      default:\n        return pricing.creditsPerGeneration;\n    }\n  },\n\n  // Calculate total cost for multiple generations\n  calculateBatchCost(requests: { modelId: RevoModelId; type: 'content' | 'design' | 'video' }[]): number {\n    return requests.reduce((total, request) => {\n      return total + this.calculateGenerationCost(request.modelId, request.type);\n    }, 0);\n  },\n\n  // Estimate monthly cost based on usage patterns\n  estimateMonthlyCost(usage: {\n    modelId: RevoModelId;\n    generationsPerDay: number;\n    designsPerDay: number;\n    videosPerDay?: number;\n  }): {\n    dailyCost: number;\n    monthlyCost: number;\n    recommendedPackage: keyof typeof creditPackages;\n  } {\n    const pricing = modelPricing[usage.modelId];\n\n    const dailyCost =\n      (usage.generationsPerDay * pricing.creditsPerGeneration) +\n      (usage.designsPerDay * pricing.creditsPerDesign) +\n      ((usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0));\n\n    const monthlyCost = dailyCost * 30;\n\n    // Recommend package based on monthly cost\n    let recommendedPackage: keyof typeof creditPackages = 'starter';\n    if (monthlyCost > 400) recommendedPackage = 'enterprise';\n    else if (monthlyCost > 150) recommendedPackage = 'business';\n    else if (monthlyCost > 50) recommendedPackage = 'professional';\n\n    return {\n      dailyCost,\n      monthlyCost,\n      recommendedPackage\n    };\n  },\n\n  // Check if user has enough credits for a request\n  canAfford(userCredits: number, modelId: RevoModelId, type: 'content' | 'design' | 'video' = 'content'): boolean {\n    const cost = this.calculateGenerationCost(modelId, type);\n    return userCredits >= cost;\n  },\n\n  // Get the best model within budget\n  getBestModelForBudget(availableCredits: number, type: 'content' | 'design' | 'video' = 'content'): RevoModelId[] {\n    const affordableModels: RevoModelId[] = [];\n\n    for (const [modelId, pricing] of Object.entries(modelPricing)) {\n      const cost = type === 'content' ? pricing.creditsPerGeneration :\n        type === 'design' ? pricing.creditsPerDesign :\n          pricing.creditsPerVideo || 0;\n\n      if (cost <= availableCredits && cost > 0) {\n        affordableModels.push(modelId as RevoModelId);\n      }\n    }\n\n    // Sort by quality (higher credit cost usually means higher quality)\n    return affordableModels.sort((a, b) => {\n      const costA = this.calculateGenerationCost(a, type);\n      const costB = this.calculateGenerationCost(b, type);\n      return costB - costA; // Descending order (highest quality first)\n    });\n  }\n};\n\n// Pricing display utilities\nexport const pricingDisplay = {\n  // Format credits for display\n  formatCredits(credits: number): string {\n    if (credits >= 1000) {\n      return `${(credits / 1000).toFixed(1)}K`;\n    }\n    return credits.toString();\n  },\n\n  // Format price for display\n  formatPrice(price: number): string {\n    return `$${price.toFixed(2)}`;\n  },\n\n  // Get pricing tier info\n  getTierInfo(modelId: RevoModelId) {\n    const pricing = modelPricing[modelId];\n    return pricingTiers[pricing.tier];\n  },\n\n  // Get cost comparison between models\n  compareCosts(modelA: RevoModelId, modelB: RevoModelId) {\n    const costA = modelPricing[modelA].creditsPerGeneration;\n    const costB = modelPricing[modelB].creditsPerGeneration;\n\n    const difference = Math.abs(costA - costB);\n    const percentDifference = ((difference / Math.min(costA, costB)) * 100).toFixed(0);\n\n    return {\n      cheaper: costA < costB ? modelA : modelB,\n      moreExpensive: costA > costB ? modelA : modelB,\n      difference,\n      percentDifference: `${percentDifference}%`,\n      ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`\n    };\n  },\n\n  // Get value proposition for each model\n  getValueProposition(modelId: RevoModelId) {\n    const pricing = modelPricing[modelId];\n    const tierInfo = pricingTiers[pricing.tier];\n\n    return {\n      model: modelId,\n      tier: pricing.tier,\n      creditsPerGeneration: pricing.creditsPerGeneration,\n      valueScore: tierInfo.features.length / pricing.creditsPerGeneration, // Features per credit\n      description: tierInfo.description,\n      bestFor: tierInfo.recommendedFor\n    };\n  }\n};\n\n// Export utility functions\nexport function getModelPricing(modelId: RevoModelId): ModelPricing {\n  return modelPricing[modelId];\n}\n\nexport function getAllPricing(): Record<RevoModelId, ModelPricing> {\n  return modelPricing;\n}\n\nexport function getModelsByTier(tier: 'basic' | 'premium' | 'enterprise'): RevoModelId[] {\n  return Object.entries(modelPricing)\n    .filter(([_, pricing]) => pricing.tier === tier)\n    .map(([modelId]) => modelId as RevoModelId);\n}\n\nexport function getCheapestModel(): RevoModelId {\n  return Object.entries(modelPricing)\n    .reduce((cheapest, [modelId, pricing]) => {\n      const currentCheapest = modelPricing[cheapest as RevoModelId];\n      return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ?\n        modelId as RevoModelId : cheapest as RevoModelId;\n    }, 'revo-1.0' as RevoModelId);\n}\n\nexport function getMostExpensiveModel(): RevoModelId {\n  return Object.entries(modelPricing)\n    .reduce((mostExpensive, [modelId, pricing]) => {\n      const currentMostExpensive = modelPricing[mostExpensive as RevoModelId];\n      return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ?\n        modelId as RevoModelId : mostExpensive as RevoModelId;\n    }, 'revo-1.0' as RevoModelId);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAKM,MAAM,eAAkD;IAC7D,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM,WAAW,sBAAsB;IACzC;IAEA,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM;IACR;IAIA,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,OAAO;QACL,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B,SAAS;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,UAAU;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,mDAAmD;IACnD,yBAAwB,OAAoB,EAAE,OAAuC,SAAS;QAC5F,MAAM,UAAU,YAAY,CAAC,QAAQ;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,oBAAoB;YACrC,KAAK;gBACH,OAAO,QAAQ,gBAAgB;YACjC,KAAK;gBACH,OAAO,QAAQ,eAAe,IAAI;YACpC;gBACE,OAAO,QAAQ,oBAAoB;QACvC;IACF;IAEA,gDAAgD;IAChD,oBAAmB,QAA0E;QAC3F,OAAO,SAAS,MAAM,CAAC,CAAC,OAAO;YAC7B,OAAO,QAAQ,IAAI,CAAC,uBAAuB,CAAC,QAAQ,OAAO,EAAE,QAAQ,IAAI;QAC3E,GAAG;IACL;IAEA,gDAAgD;IAChD,qBAAoB,KAKnB;QAKC,MAAM,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC;QAE3C,MAAM,YACJ,AAAC,MAAM,iBAAiB,GAAG,QAAQ,oBAAoB,GACtD,MAAM,aAAa,GAAG,QAAQ,gBAAgB,GAC9C,CAAC,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,eAAe,IAAI,CAAC;QAE5D,MAAM,cAAc,YAAY;QAEhC,0CAA0C;QAC1C,IAAI,qBAAkD;QACtD,IAAI,cAAc,KAAK,qBAAqB;aACvC,IAAI,cAAc,KAAK,qBAAqB;aAC5C,IAAI,cAAc,IAAI,qBAAqB;QAEhD,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,iDAAiD;IACjD,WAAU,WAAmB,EAAE,OAAoB,EAAE,OAAuC,SAAS;QACnG,MAAM,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS;QACnD,OAAO,eAAe;IACxB;IAEA,mCAAmC;IACnC,uBAAsB,gBAAwB,EAAE,OAAuC,SAAS;QAC9F,MAAM,mBAAkC,EAAE;QAE1C,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,cAAe;YAC7D,MAAM,OAAO,SAAS,YAAY,QAAQ,oBAAoB,GAC5D,SAAS,WAAW,QAAQ,gBAAgB,GAC1C,QAAQ,eAAe,IAAI;YAE/B,IAAI,QAAQ,oBAAoB,OAAO,GAAG;gBACxC,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA,oEAAoE;QACpE,OAAO,iBAAiB,IAAI,CAAC,CAAC,GAAG;YAC/B,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG;YAC9C,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG;YAC9C,OAAO,QAAQ,OAAO,2CAA2C;QACnE;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,6BAA6B;IAC7B,eAAc,OAAe;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO,GAAG,CAAC,UAAU,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C;QACA,OAAO,QAAQ,QAAQ;IACzB;IAEA,2BAA2B;IAC3B,aAAY,KAAa;QACvB,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;IAC/B;IAEA,wBAAwB;IACxB,aAAY,OAAoB;QAC9B,MAAM,UAAU,YAAY,CAAC,QAAQ;QACrC,OAAO,YAAY,CAAC,QAAQ,IAAI,CAAC;IACnC;IAEA,qCAAqC;IACrC,cAAa,MAAmB,EAAE,MAAmB;QACnD,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,oBAAoB;QACvD,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,oBAAoB;QAEvD,MAAM,aAAa,KAAK,GAAG,CAAC,QAAQ;QACpC,MAAM,oBAAoB,CAAC,AAAC,aAAa,KAAK,GAAG,CAAC,OAAO,SAAU,GAAG,EAAE,OAAO,CAAC;QAEhF,OAAO;YACL,SAAS,QAAQ,QAAQ,SAAS;YAClC,eAAe,QAAQ,QAAQ,SAAS;YACxC;YACA,mBAAmB,GAAG,kBAAkB,CAAC,CAAC;YAC1C,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,GAAG,CAAC,OAAO,QAAQ;QAC9D;IACF;IAEA,uCAAuC;IACvC,qBAAoB,OAAoB;QACtC,MAAM,UAAU,YAAY,CAAC,QAAQ;QACrC,MAAM,WAAW,YAAY,CAAC,QAAQ,IAAI,CAAC;QAE3C,OAAO;YACL,OAAO;YACP,MAAM,QAAQ,IAAI;YAClB,sBAAsB,QAAQ,oBAAoB;YAClD,YAAY,SAAS,QAAQ,CAAC,MAAM,GAAG,QAAQ,oBAAoB;YACnE,aAAa,SAAS,WAAW;YACjC,SAAS,SAAS,cAAc;QAClC;IACF;AACF;AAGO,SAAS,gBAAgB,OAAoB;IAClD,OAAO,YAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAwC;IACtE,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAK,QAAQ,IAAI,KAAK,MAC1C,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;AACxB;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,UAAU,CAAC,SAAS,QAAQ;QACnC,MAAM,kBAAkB,YAAY,CAAC,SAAwB;QAC7D,OAAO,QAAQ,oBAAoB,GAAG,gBAAgB,oBAAoB,GACxE,UAAyB;IAC7B,GAAG;AACP;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,eAAe,CAAC,SAAS,QAAQ;QACxC,MAAM,uBAAuB,YAAY,CAAC,cAA6B;QACvE,OAAO,QAAQ,oBAAoB,GAAG,qBAAqB,oBAAoB,GAC7E,UAAyB;IAC7B,GAAG;AACP", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/model-configs.ts"], "sourcesContent": ["/**\n * Model Configurations\n * Centralized configuration for all Revo model versions\n */\n\nimport type { RevoModel, RevoModelId } from '../types/model-types';\nimport { modelCapabilities } from './capabilities';\nimport { modelPricing } from './pricing';\n\n// Base configurations for different AI services\nconst baseConfigs = {\n  'gemini-2.0': {\n    aiService: 'gemini-2.0' as const,\n    fallbackServices: ['gemini-2.5', 'openai'],\n    maxRetries: 3,\n    timeout: 30000,\n    qualitySettings: {\n      imageResolution: '1024x1024',\n      compressionLevel: 85,\n      enhancementLevel: 5\n    },\n    promptSettings: {\n      temperature: 0.7,\n      maxTokens: 2048,\n      topP: 0.9,\n      topK: 40\n    }\n  },\n  'gemini-2.5': {\n    aiService: 'gemini-2.5' as const,\n    fallbackServices: ['gemini-2.0', 'openai'],\n    maxRetries: 2,\n    timeout: 45000,\n    qualitySettings: {\n      imageResolution: '1024x1024',\n      compressionLevel: 90,\n      enhancementLevel: 7\n    },\n    promptSettings: {\n      temperature: 0.8,\n      maxTokens: 4096,\n      topP: 0.95,\n      topK: 50\n    }\n  },\n  'openai': {\n    aiService: 'openai' as const,\n    fallbackServices: ['gemini-2.5', 'gemini-2.0'],\n    maxRetries: 3,\n    timeout: 35000,\n    qualitySettings: {\n      imageResolution: '1024x1024',\n      compressionLevel: 88,\n      enhancementLevel: 6\n    },\n    promptSettings: {\n      temperature: 0.7,\n      maxTokens: 3000,\n      topP: 0.9\n    }\n  },\n  'gemini-2.5-flash-image': {\n    aiService: 'gemini-2.5-flash-image' as const,\n    fallbackServices: ['imagen-4', 'gemini-2.5'],\n    maxRetries: 3,\n    timeout: 45000,\n    qualitySettings: {\n      imageResolution: '2048x2048',\n      compressionLevel: 92,\n      enhancementLevel: 9\n    },\n    promptSettings: {\n      temperature: 0.7,\n      maxTokens: 2048,\n      topP: 0.9,\n      topK: 40\n    }\n  }\n};\n\n// Model definitions\nexport const modelConfigs: Record<RevoModelId, RevoModel> = {\n  'revo-1.0': {\n    id: 'revo-1.0',\n    name: 'Revo 1.0',\n    version: '1.0.0',\n    description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',\n    longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',\n    icon: 'Zap',\n    badge: 'Enhanced',\n    badgeVariant: 'default',\n    status: 'enhanced',\n    capabilities: modelCapabilities['revo-1.0'],\n    config: baseConfigs['gemini-2.5-flash-image'],\n    pricing: modelPricing['revo-1.0'],\n    features: [\n      'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',\n      '1:1 Images with High Resolution',\n      'Core Features',\n      'Proven Performance',\n      'Multi-platform Support',\n      'Enhanced Brand Consistency',\n      'Perfect Text Rendering',\n      'High-Resolution Output (2048x2048)'\n    ],\n    releaseDate: '2024-01-15',\n    lastUpdated: '2025-01-27'\n  },\n\n  'revo-1.5': {\n    id: 'revo-1.5',\n    name: 'Revo 1.5',\n    version: '1.5.0',\n    description: 'Enhanced Model - Advanced Features',\n    longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',\n    icon: 'Sparkles',\n    badge: 'Enhanced',\n    badgeVariant: 'default',\n    status: 'enhanced',\n    capabilities: modelCapabilities['revo-1.5'],\n    config: {\n      ...baseConfigs['gemini-2.5'],\n      qualitySettings: {\n        ...baseConfigs['gemini-2.5'].qualitySettings,\n        enhancementLevel: 8\n      }\n    },\n    pricing: modelPricing['revo-1.5'],\n    features: [\n      'Advanced AI Engine',\n      'Superior Quality',\n      'Enhanced Design',\n      'Smart Optimizations',\n      'Professional Templates',\n      'Advanced Brand Integration',\n      'Real-time Context',\n      'Trending Topics Integration'\n    ],\n    releaseDate: '2024-06-20',\n    lastUpdated: '2024-12-15'\n  },\n\n\n\n  'revo-2.0': {\n    id: 'revo-2.0',\n    name: 'Revo 2.0',\n    version: '2.0.0',\n    description: 'Next-Gen Model - Advanced AI with native image generation',\n    longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',\n    icon: 'Rocket',\n    badge: 'Next-Gen',\n    badgeVariant: 'default',\n    status: 'enhanced',\n    capabilities: modelCapabilities['revo-2.0'],\n    config: baseConfigs['gemini-2.5-flash-image'],\n    pricing: modelPricing['revo-2.0'],\n    features: [\n      'Next-Gen AI Engine',\n      'Native Image Generation',\n      'Character Consistency',\n      'Intelligent Editing',\n      'Inpainting & Outpainting',\n      'Multimodal Reasoning',\n      'All Aspect Ratios',\n      'Perfect Brand Consistency'\n    ],\n    releaseDate: '2025-01-27',\n    lastUpdated: '2025-01-27'\n  }\n};\n\n// Helper functions\nexport function getModelConfig(modelId: RevoModelId): RevoModel {\n  const config = modelConfigs[modelId];\n  if (!config) {\n    throw new Error(`Model configuration not found for: ${modelId}`);\n  }\n  return config;\n}\n\nexport function getAllModelConfigs(): RevoModel[] {\n  return Object.values(modelConfigs);\n}\n\nexport function getModelsByStatus(status: RevoModel['status']): RevoModel[] {\n  return getAllModelConfigs().filter(model => model.status === status);\n}\n\nexport function getModelsByTier(tier: 'basic' | 'premium' | 'enterprise'): RevoModel[] {\n  return getAllModelConfigs().filter(model => model.pricing.tier === tier);\n}\n\nexport function getLatestModels(): RevoModel[] {\n  return getAllModelConfigs()\n    .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())\n    .slice(0, 3);\n}\n\nexport function getRecommendedModel(): RevoModel {\n  // Return Revo 1.5 as the recommended balanced option\n  return modelConfigs['revo-1.5'];\n}\n\nexport function getModelForBudget(maxCredits: number): RevoModel[] {\n  return getAllModelConfigs()\n    .filter(model => model.pricing.creditsPerGeneration <= maxCredits)\n    .sort((a, b) => a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);\n}\n\n// Model comparison utilities\nexport function compareModels(modelA: RevoModelId, modelB: RevoModelId) {\n  const configA = getModelConfig(modelA);\n  const configB = getModelConfig(modelB);\n\n  return {\n    quality: {\n      a: configA.capabilities.maxQuality,\n      b: configB.capabilities.maxQuality,\n      winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB\n    },\n    cost: {\n      a: configA.pricing.creditsPerGeneration,\n      b: configB.pricing.creditsPerGeneration,\n      winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB\n    },\n    features: {\n      a: configA.features.length,\n      b: configB.features.length,\n      winner: configA.features.length > configB.features.length ? modelA : modelB\n    },\n    status: {\n      a: configA.status,\n      b: configB.status,\n      recommendation: configA.status === 'stable' || configB.status === 'stable' ?\n        (configA.status === 'stable' ? modelA : modelB) : modelA\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAGD;AACA;;;AAEA,gDAAgD;AAChD,MAAM,cAAc;IAClB,cAAc;QACZ,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAS;QAC1C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,cAAc;QACZ,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAS;QAC1C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,UAAU;QACR,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAa;QAC9C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;QACR;IACF;IACA,0BAA0B;QACxB,WAAW;QACX,kBAAkB;YAAC;YAAY;SAAa;QAC5C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;AACF;AAGO,MAAM,eAA+C;IAC1D,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ,WAAW,CAAC,yBAAyB;QAC7C,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ;YACN,GAAG,WAAW,CAAC,aAAa;YAC5B,iBAAiB;gBACf,GAAG,WAAW,CAAC,aAAa,CAAC,eAAe;gBAC5C,kBAAkB;YACpB;QACF;QACA,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;IAIA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ,WAAW,CAAC,yBAAyB;QAC7C,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;AACF;AAGO,SAAS,eAAe,OAAoB;IACjD,MAAM,SAAS,YAAY,CAAC,QAAQ;IACpC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS;IACjE;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAEO,SAAS,kBAAkB,MAA2B;IAC3D,OAAO,qBAAqB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;AAC/D;AAEO,SAAS,gBAAgB,IAAwC;IACtE,OAAO,qBAAqB,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,IAAI,KAAK;AACrE;AAEO,SAAS;IACd,OAAO,qBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,IAClF,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,qDAAqD;IACrD,OAAO,YAAY,CAAC,WAAW;AACjC;AAEO,SAAS,kBAAkB,UAAkB;IAClD,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,oBAAoB,IAAI,YACtD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,oBAAoB,GAAG,EAAE,OAAO,CAAC,oBAAoB;AACnF;AAGO,SAAS,cAAc,MAAmB,EAAE,MAAmB;IACpE,MAAM,UAAU,eAAe;IAC/B,MAAM,UAAU,eAAe;IAE/B,OAAO;QACL,SAAS;YACP,GAAG,QAAQ,YAAY,CAAC,UAAU;YAClC,GAAG,QAAQ,YAAY,CAAC,UAAU;YAClC,QAAQ,QAAQ,YAAY,CAAC,UAAU,GAAG,QAAQ,YAAY,CAAC,UAAU,GAAG,SAAS;QACvF;QACA,MAAM;YACJ,GAAG,QAAQ,OAAO,CAAC,oBAAoB;YACvC,GAAG,QAAQ,OAAO,CAAC,oBAAoB;YACvC,QAAQ,QAAQ,OAAO,CAAC,oBAAoB,GAAG,QAAQ,OAAO,CAAC,oBAAoB,GAAG,SAAS;QACjG;QACA,UAAU;YACR,GAAG,QAAQ,QAAQ,CAAC,MAAM;YAC1B,GAAG,QAAQ,QAAQ,CAAC,MAAM;YAC1B,QAAQ,QAAQ,QAAQ,CAAC,MAAM,GAAG,QAAQ,QAAQ,CAAC,MAAM,GAAG,SAAS;QACvE;QACA,QAAQ;YACN,GAAG,QAAQ,MAAM;YACjB,GAAG,QAAQ,MAAM;YACjB,gBAAgB,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,KAAK,WAC/D,QAAQ,MAAM,KAAK,WAAW,SAAS,SAAU;QACtD;IACF;AACF", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/config.ts"], "sourcesContent": ["/**\n * Revo 1.0 Configuration\n * Model-specific configuration and constants\n */\n\nimport type { ModelConfig } from '../../types/model-types';\n\n// Revo 1.0 specific configuration\nexport const revo10Config: ModelConfig = {\n  aiService: 'gemini-2.5-flash-image-preview',\n  fallbackServices: ['gemini-2.5', 'gemini-2.0', 'openai'],\n  maxRetries: 3,\n  timeout: 45000, // 45 seconds (increased for better quality)\n  qualitySettings: {\n    imageResolution: '2048x2048', // Upgraded from 1024x1024\n    compressionLevel: 92, // Upgraded from 85\n    enhancementLevel: 9 // Upgraded from 7 (maximum enhancement)\n  },\n  promptSettings: {\n    temperature: 0.7, // Increased from 0.5 for more creative output\n    maxTokens: 2048,\n    topP: 0.9, // Increased from 0.8 for better quality\n    topK: 40 // Increased from 30 for more variety\n  }\n};\n\n// Revo 1.0 specific constants\nexport const revo10Constants = {\n  // Model identification\n  MODEL_ID: 'revo-1.0',\n  MODEL_NAME: 'Revo 1.0',\n  MODEL_VERSION: '1.0.0',\n\n  // Capabilities\n  SUPPORTED_ASPECT_RATIOS: ['1:1'],\n  SUPPORTED_PLATFORMS: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\n  MAX_QUALITY_SCORE: 9.0, // Upgraded from 7.5\n\n  // Performance targets\n  TARGET_PROCESSING_TIME: 30000, // 30 seconds (increased for better quality)\n  TARGET_SUCCESS_RATE: 0.97, // 97% (increased from 95%)\n  TARGET_QUALITY_SCORE: 8.5, // Upgraded from 7.0\n\n  // Resource limits\n  MAX_CONTENT_LENGTH: 2000,\n  MAX_HASHTAGS: 15,\n  MAX_IMAGE_SIZE: 2048, // Upgraded from 1024\n\n  // Feature flags\n  FEATURES: {\n    ARTIFACTS_SUPPORT: false,\n    REAL_TIME_CONTEXT: true,  // Enable for better context\n    TRENDING_TOPICS: true,    // Enable for better content\n    MULTIPLE_ASPECT_RATIOS: false,\n    VIDEO_GENERATION: false,\n    ADVANCED_PROMPTING: true, // Enable for better prompts\n    ENHANCED_DESIGN: true,    // Enable for better designs!\n    PERFECT_TEXT_RENDERING: true, // NEW: Gemini 2.5 Flash Image Preview feature\n    HIGH_RESOLUTION: true,    // NEW: 2048x2048 resolution support\n    NATIVE_IMAGE_GENERATION: true // NEW: Direct image generation capability\n  },\n\n  // Pricing\n  CREDITS_PER_GENERATION: 1.5, // Upgraded from 1 for enhanced capabilities\n  CREDITS_PER_DESIGN: 1.5, // Upgraded from 1 for enhanced capabilities\n  TIER: 'enhanced' // Upgraded from basic\n} as const;\n\n// Revo 1.0 specific prompts and templates\nexport const revo10Prompts = {\n  // Content generation prompts\n  CONTENT_SYSTEM_PROMPT: `You are an elite social media content strategist for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering.\nYour expertise spans viral content creation, brand storytelling, and audience engagement optimization.\n\nCore competencies:\n- Craft scroll-stopping, engagement-driving captions\n- Create strategic hashtag combinations for maximum reach\n- Develop brand-consistent content that converts\n- Optimize content for platform-specific algorithms\n- Generate compelling headlines and calls-to-action\n- Integrate local relevance and cultural context\n- Drive meaningful audience interaction and community building\n- Leverage trending topics and industry insights\n- Create content that balances professionalism with personality`,\n\n  CONTENT_USER_PROMPT_TEMPLATE: `Generate social media content for:\nBusiness: {businessName}\nType: {businessType}\nPlatform: {platform}\nTone: {writingTone}\nLocation: {location}\n\nBrand Information:\n- Primary Color: {primaryColor}\n- Visual Style: {visualStyle}\n- Target Audience: {targetAudience}\n- Services: {services}\n- Key Features: {keyFeatures}\n- Competitive Advantages: {competitiveAdvantages}\n- Content Themes: {contentThemes}\n\nRequirements:\n- Create engaging, professional content that reflects the business's unique value proposition\n- Incorporate services and key features naturally into the content\n- Highlight competitive advantages when relevant\n- Include relevant hashtags (5-15) that align with content themes\n- Generate catchy words for the image that capture the brand essence\n- Ensure platform-appropriate formatting and tone\n- Maintain brand consistency with colors and visual style\n- Use only clean, readable text (no special characters, symbols, or garbled text)\n- Generate content in proper English with correct spelling and grammar\n- Avoid any corrupted or unreadable character sequences\n- Make the content location-specific and culturally relevant when appropriate`,\n\n  // Design generation prompts\n  DESIGN_SYSTEM_PROMPT: `You are an elite visual designer and creative director for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for professional-grade design generation.\nYour expertise spans advanced composition, typography, color theory, and modern design trends. You create designs that surpass Canva quality.\n\nCORE DESIGN PHILOSOPHY:\n- Create visually stunning, professional designs that command attention\n- Apply advanced design principles and composition rules\n- Use sophisticated typography and color harmony\n- Implement modern design trends and visual techniques\n- Ensure every element serves a purpose and enhances the message\n- Generate designs that convert viewers into customers\n\nADVANCED DESIGN PRINCIPLES:\n**COMPOSITION & VISUAL HIERARCHY:**\n- Apply Rule of Thirds: Position key elements along grid lines/intersections\n- Create clear visual hierarchy using size, contrast, and positioning\n- Establish strong focal points that draw the eye immediately\n- Use negative space strategically for breathing room and emphasis\n- Balance elements with sophisticated asymmetrical composition\n- Guide viewer's eye through design with leading lines and flow\n\n**TYPOGRAPHY EXCELLENCE:**\n- Establish clear typographic hierarchy (Primary headline, secondary, body)\n- Use maximum 2-3 font families with strong contrast\n- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)\n- Apply proper letter spacing, line height, and alignment\n- Scale typography for platform and viewing distance\n- Use typography as a design element, not just information delivery\n\n**COLOR THEORY & HARMONY:**\n- Apply color psychology appropriate to business type and message\n- Use complementary colors for high contrast and attention\n- Apply analogous colors for harmony and cohesion\n- Implement triadic color schemes for vibrant, balanced designs\n- Use 60-30-10 rule: 60% dominant, 30% secondary, 10% accent\n- Ensure sufficient contrast between text and background\n\n**MODERN DESIGN TRENDS:**\n- Embrace minimalism with purposeful white space\n- Use bold, geometric shapes and clean lines\n- Apply subtle gradients and depth effects\n- Incorporate authentic, diverse imagery when appropriate\n- Use consistent border radius and spacing\n- Apply subtle shadows and depth for modern dimensionality`,\n\n  DESIGN_USER_PROMPT_TEMPLATE: `Create a professional-grade 2048x2048 social media design that surpasses Canva quality for:\n\nBUSINESS CONTEXT:\n- Business: {businessName}\n- Industry: {businessType}\n- Platform: {platform}\n- Visual Style: {visualStyle}\n- Target Message: {imageText}\n\nBRAND IDENTITY SYSTEM:\n- Primary Color: {primaryColor} (60% usage - dominant color)\n- Accent Color: {accentColor} (30% usage - secondary elements)\n- Background: {backgroundColor} (10% usage - highlights and details)\n- Logo Integration: {logoInstruction}\n\nPLATFORM-SPECIFIC OPTIMIZATION FOR {platform}:\n${'{platform}' === 'Instagram' ? `\n- Mobile-first design with bold, clear elements\n- High contrast colors that pop on small screens\n- Text minimum 24px equivalent for readability\n- Center important elements for square crop\n- Thumb-stopping power for fast scroll feeds\n- Logo: Bottom right or naturally integrated` : ''}\n${'{platform}' === 'LinkedIn' ? `\n- Professional, business-appropriate aesthetics\n- Corporate design standards and clean look\n- Clear value proposition for business audience\n- Professional photography and imagery\n- Thought leadership positioning\n- Logo: Prominent for brand authority` : ''}\n${'{platform}' === 'Facebook' ? `\n- Desktop and mobile viewing optimization\n- Engagement and shareability focus\n- Clear value proposition in hierarchy\n- Authentic, relatable imagery\n- Logo: Top left or bottom right` : ''}\n${'{platform}' === 'Twitter' ? `\n- Rapid consumption and high engagement\n- Bold, contrasting timeline colors\n- Minimal, impactful text\n- Trending visual styles\n- Logo: Small, subtle placement` : ''}\n\nBUSINESS TYPE DESIGN DNA FOR {businessType}:\nApply industry-specific design principles and visual language appropriate for this business type.\n\nADVANCED COMPOSITION REQUIREMENTS:\n- Apply Rule of Thirds for element placement\n- Create strong focal point with {imageText} as primary message\n- Use sophisticated asymmetrical balance\n- Implement clear visual hierarchy: Headline → Supporting elements → CTA\n- Strategic negative space for premium feel\n- Leading lines to guide eye flow\n\nTYPOGRAPHY SPECIFICATIONS:\n- Primary headline: Bold, attention-grabbing, high contrast\n- Secondary text: Supporting, readable, complementary\n- Ensure 4.5:1 contrast ratio minimum\n- Professional font pairing (max 2-3 families)\n- Proper spacing and alignment\n\nCOLOR IMPLEMENTATION:\n- Use {primaryColor} as dominant (60%)\n- {accentColor} for secondary elements (30%)\n- {backgroundColor} for highlights (10%)\n- Apply color psychology for {businessType}\n- Ensure accessibility and contrast\n\nMODERN DESIGN ELEMENTS:\n- Subtle gradients and depth effects\n- Clean geometric shapes\n- Consistent border radius\n- Professional shadows and lighting\n- Premium visual texture and finish\n\nQUALITY STANDARDS:\n- Professional agency-level quality\n- Better than Canva templates\n- Print-ready resolution and clarity\n- Perfect text rendering\n- Sophisticated visual appeal\n- Commercial-grade finish`,\n\n  // Error messages\n  ERROR_MESSAGES: {\n    GENERATION_FAILED: 'Revo 1.0 content generation failed. Please try again.',\n    DESIGN_FAILED: 'Revo 1.0 design generation failed. Please try again.',\n    INVALID_REQUEST: 'Invalid request for Revo 1.0. Please check your parameters.',\n    SERVICE_UNAVAILABLE: 'Revo 1.0 service is temporarily unavailable.',\n    TIMEOUT: 'Revo 1.0 generation timed out. Please try again.',\n    QUOTA_EXCEEDED: 'Revo 1.0 usage quota exceeded. Please upgrade your plan.'\n  }\n} as const;\n\n// Revo 1.0 validation rules\nexport const revo10Validation = {\n  // Content validation\n  content: {\n    minLength: 10,\n    maxLength: 2000,\n    requiredFields: ['businessType', 'platform', 'businessName'],\n    supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS\n  },\n\n  // Design validation\n  design: {\n    requiredFields: ['businessType', 'platform', 'visualStyle', 'imageText'],\n    supportedAspectRatios: revo10Constants.SUPPORTED_ASPECT_RATIOS,\n    maxImageTextLength: 200,\n    supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS\n  },\n\n  // Brand profile validation\n  brandProfile: {\n    requiredFields: ['businessType', 'businessName'],\n    optionalFields: [\n      'location', 'writingTone', 'visualStyle', 'primaryColor',\n      'accentColor', 'backgroundColor', 'logoDataUrl', 'targetAudience'\n    ]\n  }\n} as const;\n\n// Revo 1.0 performance metrics\nexport const revo10Metrics = {\n  // Expected performance benchmarks\n  BENCHMARKS: {\n    processingTime: {\n      target: 30000, // 30 seconds (upgraded from 20s)\n      acceptable: 40000, // 40 seconds (upgraded from 30s)\n      maximum: 60000 // 60 seconds (upgraded from 45s)\n    },\n    qualityScore: {\n      minimum: 7.0, // Upgraded from 5.0\n      target: 8.5, // Upgraded from 7.0\n      maximum: 9.0 // Upgraded from 7.5\n    },\n    successRate: {\n      minimum: 0.95, // Upgraded from 90%\n      target: 0.97, // Upgraded from 95%\n      maximum: 0.99 // Upgraded from 98%\n    }\n  },\n\n  // Monitoring thresholds\n  ALERTS: {\n    processingTimeHigh: 45000, // Alert if processing takes > 45s (upgraded from 35s)\n    qualityScoreLow: 7.5, // Alert if quality drops below 7.5 (upgraded from 6.0)\n    successRateLow: 0.95, // Alert if success rate drops below 95% (upgraded from 92%)\n    errorRateHigh: 0.05 // Alert if error rate exceeds 5% (upgraded from 8%)\n  }\n} as const;\n\n// Export utility functions\nexport function getRevo10Config(): ModelConfig {\n  return revo10Config;\n}\n\nexport function isFeatureEnabled(feature: keyof typeof revo10Constants.FEATURES): boolean {\n  return revo10Constants.FEATURES[feature];\n}\n\nexport function getPromptTemplate(type: 'content' | 'design', templateName: string): string {\n  if (type === 'content') {\n    return revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE;\n  } else if (type === 'design') {\n    return revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE;\n  }\n  throw new Error(`Unknown prompt template: ${type}/${templateName}`);\n}\n\nexport function validateRequest(type: 'content' | 'design', request: any): { valid: boolean; errors: string[] } {\n  const errors: string[] = [];\n  const validation = type === 'content' ? revo10Validation.content : revo10Validation.design;\n\n  // Check required fields\n  for (const field of validation.requiredFields) {\n    if (!request[field]) {\n      errors.push(`Missing required field: ${field}`);\n    }\n  }\n\n  // Check platform support\n  if (request.platform && !validation.supportedPlatforms.includes(request.platform)) {\n    errors.push(`Unsupported platform: ${request.platform}`);\n  }\n\n  // Design-specific validation\n  if (type === 'design') {\n    if (request.imageText && request.imageText.length > revo10Validation.design.maxImageTextLength) {\n      errors.push(`Image text too long (max ${revo10Validation.design.maxImageTextLength} characters)`);\n    }\n  }\n\n  return {\n    valid: errors.length === 0,\n    errors\n  };\n}\n\nexport function getPerformanceBenchmark(metric: string) {\n  return revo10Metrics.BENCHMARKS[metric as keyof typeof revo10Metrics.BENCHMARKS];\n}\n\nexport function shouldAlert(metric: string, value: number): boolean {\n  const alerts = revo10Metrics.ALERTS;\n\n  switch (metric) {\n    case 'processingTime':\n      return value > alerts.processingTimeHigh;\n    case 'qualityScore':\n      return value < alerts.qualityScoreLow;\n    case 'successRate':\n      return value < alerts.successRateLow;\n    case 'errorRate':\n      return value > alerts.errorRateHigh;\n    default:\n      return false;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAKM,MAAM,eAA4B;IACvC,WAAW;IACX,kBAAkB;QAAC;QAAc;QAAc;KAAS;IACxD,YAAY;IACZ,SAAS;IACT,iBAAiB;QACf,iBAAiB;QACjB,kBAAkB;QAClB,kBAAkB,EAAE,wCAAwC;IAC9D;IACA,gBAAgB;QACd,aAAa;QACb,WAAW;QACX,MAAM;QACN,MAAM,GAAG,qCAAqC;IAChD;AACF;AAGO,MAAM,kBAAkB;IAC7B,uBAAuB;IACvB,UAAU;IACV,YAAY;IACZ,eAAe;IAEf,eAAe;IACf,yBAAyB;QAAC;KAAM;IAChC,qBAAqB;QAAC;QAAa;QAAY;QAAW;KAAW;IACrE,mBAAmB;IAEnB,sBAAsB;IACtB,wBAAwB;IACxB,qBAAqB;IACrB,sBAAsB;IAEtB,kBAAkB;IAClB,oBAAoB;IACpB,cAAc;IACd,gBAAgB;IAEhB,gBAAgB;IAChB,UAAU;QACR,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QACjB,wBAAwB;QACxB,kBAAkB;QAClB,oBAAoB;QACpB,iBAAiB;QACjB,wBAAwB;QACxB,iBAAiB;QACjB,yBAAyB,KAAK,0CAA0C;IAC1E;IAEA,UAAU;IACV,wBAAwB;IACxB,oBAAoB;IACpB,MAAM,WAAW,sBAAsB;AACzC;AAGO,MAAM,gBAAgB;IAC3B,6BAA6B;IAC7B,uBAAuB,CAAC;;;;;;;;;;;;+DAYqC,CAAC;IAE9D,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;6EA2B4C,CAAC;IAE5E,4BAA4B;IAC5B,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DA0CiC,CAAC;IAEzD,6BAA6B,CAAC;;;;;;;;;;;;;;;;AAgBhC,EAAE,6EAM8C,GAAG;AACnD,EAAE,6EAMuC,GAAG;AAC5C,EAAE,6EAKkC,GAAG;AACvC,EAAE,6EAKiC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yBAwCb,CAAC;IAExB,iBAAiB;IACjB,gBAAgB;QACd,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,qBAAqB;QACrB,SAAS;QACT,gBAAgB;IAClB;AACF;AAGO,MAAM,mBAAmB;IAC9B,qBAAqB;IACrB,SAAS;QACP,WAAW;QACX,WAAW;QACX,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,oBAAoB,gBAAgB,mBAAmB;IACzD;IAEA,oBAAoB;IACpB,QAAQ;QACN,gBAAgB;YAAC;YAAgB;YAAY;YAAe;SAAY;QACxE,uBAAuB,gBAAgB,uBAAuB;QAC9D,oBAAoB;QACpB,oBAAoB,gBAAgB,mBAAmB;IACzD;IAEA,2BAA2B;IAC3B,cAAc;QACZ,gBAAgB;YAAC;YAAgB;SAAe;QAChD,gBAAgB;YACd;YAAY;YAAe;YAAe;YAC1C;YAAe;YAAmB;YAAe;SAClD;IACH;AACF;AAGO,MAAM,gBAAgB;IAC3B,kCAAkC;IAClC,YAAY;QACV,gBAAgB;YACd,QAAQ;YACR,YAAY;YACZ,SAAS,MAAM,iCAAiC;QAClD;QACA,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,SAAS,IAAI,oBAAoB;QACnC;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,SAAS,KAAK,oBAAoB;QACpC;IACF;IAEA,wBAAwB;IACxB,QAAQ;QACN,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe,KAAK,oDAAoD;IAC1E;AACF;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,iBAAiB,OAA8C;IAC7E,OAAO,gBAAgB,QAAQ,CAAC,QAAQ;AAC1C;AAEO,SAAS,kBAAkB,IAA0B,EAAE,YAAoB;IAChF,IAAI,SAAS,WAAW;QACtB,OAAO,cAAc,4BAA4B;IACnD,OAAO,IAAI,SAAS,UAAU;QAC5B,OAAO,cAAc,2BAA2B;IAClD;IACA,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,CAAC,EAAE,cAAc;AACpE;AAEO,SAAS,gBAAgB,IAA0B,EAAE,OAAY;IACtE,MAAM,SAAmB,EAAE;IAC3B,MAAM,aAAa,SAAS,YAAY,iBAAiB,OAAO,GAAG,iBAAiB,MAAM;IAE1F,wBAAwB;IACxB,KAAK,MAAM,SAAS,WAAW,cAAc,CAAE;QAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;QAChD;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,QAAQ,IAAI,CAAC,WAAW,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,QAAQ,GAAG;QACjF,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,QAAQ,EAAE;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,UAAU;QACrB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,iBAAiB,MAAM,CAAC,kBAAkB,EAAE;YAC9F,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,iBAAiB,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAClG;IACF;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAEO,SAAS,wBAAwB,MAAc;IACpD,OAAO,cAAc,UAAU,CAAC,OAAgD;AAClF;AAEO,SAAS,YAAY,MAAc,EAAE,KAAa;IACvD,MAAM,SAAS,cAAc,MAAM;IAEnC,OAAQ;QACN,KAAK;YACH,OAAO,QAAQ,OAAO,kBAAkB;QAC1C,KAAK;YACH,OAAO,QAAQ,OAAO,eAAe;QACvC,KAAK;YACH,OAAO,QAAQ,OAAO,cAAc;QACtC,KAAK;YACH,OAAO,QAAQ,OAAO,aAAa;QACrC;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-1.0-service.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 - Enhanced AI Service with Gemini 2.5 Flash Image Preview\r\n * Upgraded from Gemini 2.0 to provide enhanced quality and perfect text rendering\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport { BrandProfile } from '@/lib/types';\r\nimport { revo10Config, revo10Prompts } from './models/versions/revo-1.0/config';\r\n\r\n// Advanced features integration (simplified for now)\r\n// TODO: Import advanced features from Revo 1.5 when available\r\n\r\n// Helper functions for advanced design generation\r\nfunction getBusinessDesignDNA(businessType: string): string {\r\n  const designDNA: Record<string, string> = {\r\n    'restaurant': 'Warm, appetizing colors (reds, oranges, warm yellows). High-quality food photography. Cozy, inviting atmosphere. Emphasis on freshness and quality.',\r\n    'technology': 'Clean, modern aesthetics. Blue and tech-forward color schemes. Geometric shapes. Innovation and reliability focus. Professional typography.',\r\n    'healthcare': 'Clean, trustworthy design. Calming blues and greens. Professional imagery. Focus on care and expertise. Accessible design principles.',\r\n    'fitness': 'Dynamic, energetic design. Bold colors and strong contrasts. Action-oriented imagery. Motivational messaging. Strong, athletic typography.',\r\n    'finance': 'Professional, trustworthy design. Conservative color palette. Clean lines. Security and stability focus. Authoritative typography.',\r\n    'education': 'Approachable, inspiring design. Bright, optimistic colors. Clear information hierarchy. Growth and learning focus. Readable typography.',\r\n    'retail': 'Attractive, commercial design. Brand-focused colors. Product-centric imagery. Sales and value focus. Eye-catching typography.',\r\n    'real estate': 'Luxurious, aspirational design. Sophisticated color palette. High-quality property imagery. Trust and expertise focus. Elegant typography.',\r\n    'default': 'Professional, modern design. Balanced color scheme. Clean, contemporary aesthetics. Quality and reliability focus. Professional typography.'\r\n  };\r\n\r\n  return designDNA[businessType.toLowerCase()] || designDNA['default'];\r\n}\r\n\r\nfunction getPlatformOptimization(platform: string): string {\r\n  const optimizations: Record<string, string> = {\r\n    'instagram': `\r\n- Mobile-first design with bold, clear elements\r\n- High contrast colors that pop on small screens\r\n- Text minimum 24px equivalent for readability\r\n- Center important elements for square crop compatibility\r\n- Thumb-stopping power for fast scroll feeds\r\n- Logo: Bottom right corner or naturally integrated`,\r\n\r\n    'linkedin': `\r\n- Professional, business-appropriate aesthetics\r\n- Corporate design standards and clean look\r\n- Clear value proposition for business audience\r\n- Professional photography and imagery\r\n- Thought leadership positioning\r\n- Logo: Prominent placement for brand authority`,\r\n\r\n    'facebook': `\r\n- Desktop and mobile viewing optimization\r\n- Engagement and shareability focus\r\n- Clear value proposition in visual hierarchy\r\n- Authentic, relatable imagery\r\n- Community-focused design elements\r\n- Logo: Top left or bottom right corner`,\r\n\r\n    'twitter': `\r\n- Rapid consumption and high engagement design\r\n- Bold, contrasting colors for timeline visibility\r\n- Minimal, impactful text elements\r\n- Trending visual styles integration\r\n- Real-time relevance\r\n- Logo: Small, subtle placement`,\r\n\r\n    'default': `\r\n- Cross-platform compatibility\r\n- Universal appeal and accessibility\r\n- Balanced design for multiple contexts\r\n- Professional appearance across devices\r\n- Logo: Flexible placement based on composition`\r\n  };\r\n\r\n  return optimizations[platform.toLowerCase()] || optimizations['default'];\r\n}\r\n\r\n// Advanced real-time context gathering for Revo 1.0 (simplified version)\r\nasync function gatherRealTimeContext(businessType: string, location: string, platform: string) {\r\n  console.log('🌐 Revo 1.0: Gathering enhanced context data...');\r\n\r\n  const context: any = {\r\n    trends: [],\r\n    weather: null,\r\n    events: [],\r\n    news: [],\r\n    timeContext: {\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      month: new Date().toLocaleDateString('en-US', { month: 'long' }),\r\n      season: getSeason(),\r\n      timeOfDay: getTimeOfDay()\r\n    }\r\n  };\r\n\r\n  try {\r\n    // Generate contextual trends based on business type and location\r\n    console.log('📈 Generating contextual trends...');\r\n    context.trends = generateContextualTrends(businessType, location);\r\n    console.log(`✅ Generated ${context.trends.length} contextual trends`);\r\n\r\n    // Generate weather-appropriate content suggestions\r\n    console.log('🌤️ Generating weather context...');\r\n    context.weather = generateWeatherContext(location);\r\n    console.log(`✅ Weather context: ${context.weather.condition}`);\r\n\r\n    // Generate local business opportunities\r\n    console.log('🎪 Generating local opportunities...');\r\n    context.events = generateLocalOpportunities(businessType, location);\r\n    console.log(`✅ Found ${context.events.length} business opportunities`);\r\n\r\n    console.log('✅ Enhanced context gathered successfully');\r\n    return context;\r\n\r\n  } catch (error) {\r\n    console.error('❌ Error gathering enhanced context:', error);\r\n    return context; // Return partial context\r\n  }\r\n}\r\n\r\n// Advanced design enhancement functions\r\nfunction shouldIncludePeopleInDesign(businessType: string, location: string, visualStyle: string): boolean {\r\n  const peopleBusinessTypes = [\r\n    'restaurant', 'fitness', 'healthcare', 'education', 'retail', 'hospitality',\r\n    'beauty', 'wellness', 'consulting', 'coaching', 'real estate', 'finance',\r\n    'technology', 'marketing', 'events', 'photography', 'fashion'\r\n  ];\r\n\r\n  return peopleBusinessTypes.some(type =>\r\n    businessType.toLowerCase().includes(type) ||\r\n    visualStyle === 'lifestyle' ||\r\n    visualStyle === 'authentic'\r\n  );\r\n}\r\n\r\nfunction getLocalCulturalContext(location: string): string {\r\n  const culturalContexts: Record<string, string> = {\r\n    'kenya': 'Kenyan culture with vibrant colors, traditional patterns, modern African aesthetics, diverse ethnic representation (Kikuyu, Luo, Luhya, Kalenjin), urban Nairobi style mixed with traditional elements',\r\n    'nigeria': 'Nigerian culture with bold Ankara patterns, diverse ethnic representation, modern Lagos urban style, traditional and contemporary fusion',\r\n    'south africa': 'South African rainbow nation diversity, modern Cape Town/Johannesburg aesthetics, traditional and contemporary blend',\r\n    'ghana': 'Ghanaian Kente patterns, warm earth tones, modern Accra style, traditional craftsmanship meets contemporary design',\r\n    'uganda': 'Ugandan cultural diversity, vibrant textiles, modern Kampala urban style, traditional meets modern aesthetics',\r\n    'tanzania': 'Tanzanian Maasai influences, Swahili coastal culture, modern Dar es Salaam style, traditional patterns with contemporary flair',\r\n    'default': 'Diverse, inclusive representation with modern professional aesthetics, cultural sensitivity, and authentic human connections'\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  for (const [key, context] of Object.entries(culturalContexts)) {\r\n    if (locationKey.includes(key)) {\r\n      return context;\r\n    }\r\n  }\r\n  return culturalContexts['default'];\r\n}\r\n\r\nfunction getDesignVariations(seed: number) {\r\n  const variations = [\r\n    {\r\n      style: 'Modern Minimalist',\r\n      layout: 'Clean geometric layout with plenty of white space, single focal point, minimal text overlay',\r\n      composition: 'Centered composition with asymmetrical elements, bold typography hierarchy',\r\n      mood: 'Professional, clean, sophisticated',\r\n      elements: 'Subtle gradients, clean lines, modern sans-serif fonts, minimal color palette'\r\n    },\r\n    {\r\n      style: 'Dynamic Action',\r\n      layout: 'Diagonal composition with movement, multiple focal points, energetic flow',\r\n      composition: 'Rule of thirds with dynamic angles, overlapping elements, motion blur effects',\r\n      mood: 'Energetic, exciting, forward-moving',\r\n      elements: 'Bold colors, dynamic shapes, action-oriented imagery, strong directional lines'\r\n    },\r\n    {\r\n      style: 'Lifestyle Authentic',\r\n      layout: 'Natural, candid composition with real-world settings, human-centered design',\r\n      composition: 'Environmental context, natural lighting, authentic moments captured',\r\n      mood: 'Warm, relatable, trustworthy, human',\r\n      elements: 'Natural lighting, authentic people, real environments, warm color tones'\r\n    },\r\n    {\r\n      style: 'Corporate Professional',\r\n      layout: 'Structured grid layout, balanced composition, formal presentation',\r\n      composition: 'Symmetrical balance, clear hierarchy, professional spacing',\r\n      mood: 'Trustworthy, established, reliable, premium',\r\n      elements: 'Corporate colors, professional imagery, clean typography, structured layout'\r\n    },\r\n    {\r\n      style: 'Creative Artistic',\r\n      layout: 'Artistic composition with creative elements, unique perspectives, artistic flair',\r\n      composition: 'Creative angles, artistic overlays, unique visual treatments',\r\n      mood: 'Creative, innovative, unique, inspiring',\r\n      elements: 'Artistic effects, creative typography, unique color combinations, artistic imagery'\r\n    },\r\n    {\r\n      style: 'Tech Innovation',\r\n      layout: 'Futuristic design with tech elements, digital aesthetics, modern interfaces',\r\n      composition: 'Digital grid systems, tech-inspired layouts, modern UI elements',\r\n      mood: 'Innovative, cutting-edge, digital, forward-thinking',\r\n      elements: 'Digital effects, tech imagery, modern interfaces, futuristic elements'\r\n    },\r\n    {\r\n      style: 'Cultural Heritage',\r\n      layout: 'Traditional patterns mixed with modern design, cultural elements integrated',\r\n      composition: 'Cultural motifs, traditional-modern fusion, heritage-inspired layouts',\r\n      mood: 'Cultural, authentic, heritage-proud, modern-traditional',\r\n      elements: 'Traditional patterns, cultural colors, heritage imagery, modern interpretation'\r\n    },\r\n    {\r\n      style: 'Luxury Premium',\r\n      layout: 'Elegant, sophisticated layout with premium materials and finishes',\r\n      composition: 'Luxurious spacing, premium typography, elegant proportions',\r\n      mood: 'Luxurious, premium, exclusive, sophisticated',\r\n      elements: 'Premium materials, elegant typography, sophisticated colors, luxury imagery'\r\n    }\r\n  ];\r\n\r\n  return variations[seed % variations.length];\r\n}\r\n\r\nfunction getAdvancedPeopleInstructions(businessType: string, location: string): string {\r\n  const culturalContext = getLocalCulturalContext(location);\r\n\r\n  return `\r\n**ADVANCED PEOPLE INTEGRATION:**\r\n- Include diverse, authentic people with PERFECT FACIAL FEATURES\r\n- Complete faces, symmetrical features, natural expressions, professional poses\r\n- Faces fully visible, well-lit, anatomically correct with no deformations\r\n- Cultural Context: ${culturalContext}\r\n- Show people in varied, engaging settings:\r\n  * Professional environments (modern offices, studios, workshops)\r\n  * Lifestyle settings (contemporary homes, trendy cafes, outdoor spaces)\r\n  * Industry-specific contexts (${businessType} environments)\r\n  * Cultural celebrations and modern community gatherings\r\n  * Urban settings (co-working spaces, tech hubs, modern city life)\r\n  * Traditional meets modern (cultural heritage with contemporary life)\r\n- Ensure representation reflects local demographics and cultural values\r\n- Show real people in natural, engaging situations that vary by design\r\n- People should be actively engaged with the business/service context\r\n- Use authentic expressions of joy, confidence, success, and community\r\n- Include intergenerational representation when appropriate\r\n- Show modern African/local fashion and styling\r\n- Ensure people are central to the story, not just decorative elements`;\r\n}\r\n\r\n// Helper functions for context generation\r\nfunction getSeason(): string {\r\n  const month = new Date().getMonth();\r\n  if (month >= 2 && month <= 4) return 'Spring';\r\n  if (month >= 5 && month <= 7) return 'Summer';\r\n  if (month >= 8 && month <= 10) return 'Fall';\r\n  return 'Winter';\r\n}\r\n\r\nfunction getTimeOfDay(): string {\r\n  const hour = new Date().getHours();\r\n  if (hour >= 5 && hour < 12) return 'Morning';\r\n  if (hour >= 12 && hour < 17) return 'Afternoon';\r\n  if (hour >= 17 && hour < 21) return 'Evening';\r\n  return 'Night';\r\n}\r\n\r\nfunction generateContextualTrends(businessType: string, location: string): any[] {\r\n  const trends = [\r\n    { topic: `${businessType} innovation trends`, category: 'Industry', relevance: 'high' },\r\n    { topic: `${location} business growth`, category: 'Local', relevance: 'high' },\r\n    { topic: 'Digital transformation', category: 'Technology', relevance: 'medium' },\r\n    { topic: 'Customer experience optimization', category: 'Business', relevance: 'high' },\r\n    { topic: 'Sustainable business practices', category: 'Trends', relevance: 'medium' }\r\n  ];\r\n  return trends.slice(0, 3);\r\n}\r\n\r\nfunction generateWeatherContext(location: string): any {\r\n  // Simplified weather context based on location and season\r\n  const season = getSeason();\r\n  const contexts = {\r\n    'Spring': { condition: 'Fresh and energizing', business_impact: 'New beginnings, growth opportunities', content_opportunities: 'Renewal, fresh starts, growth themes' },\r\n    'Summer': { condition: 'Bright and active', business_impact: 'High energy, outdoor activities', content_opportunities: 'Vibrant colors, active lifestyle, summer solutions' },\r\n    'Fall': { condition: 'Cozy and productive', business_impact: 'Planning, preparation, harvest', content_opportunities: 'Preparation, results, autumn themes' },\r\n    'Winter': { condition: 'Focused and strategic', business_impact: 'Planning, reflection, indoor focus', content_opportunities: 'Planning, strategy, winter solutions' }\r\n  };\r\n\r\n  return {\r\n    temperature: '22',\r\n    condition: contexts[season as keyof typeof contexts].condition,\r\n    business_impact: contexts[season as keyof typeof contexts].business_impact,\r\n    content_opportunities: contexts[season as keyof typeof contexts].content_opportunities\r\n  };\r\n}\r\n\r\nfunction generateLocalOpportunities(businessType: string, location: string): any[] {\r\n  const opportunities = [\r\n    { name: `${location} Business Expo`, venue: 'Local Convention Center', relevance: 'networking' },\r\n    { name: `${businessType} Innovation Summit`, venue: 'Business District', relevance: 'industry' },\r\n    { name: 'Local Entrepreneur Meetup', venue: 'Community Center', relevance: 'community' }\r\n  ];\r\n  return opportunities.slice(0, 2);\r\n}\r\n\r\n// Get API keys (supporting both server-side and client-side)\r\nconst apiKey =\r\n  process.env.GEMINI_API_KEY ||\r\n  process.env.GOOGLE_API_KEY ||\r\n  process.env.GOOGLE_GENAI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GEMINI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n  console.error(\"❌ No Google AI API key found for Revo 1.0\");\r\n  console.error(\"Available env vars:\", {\r\n    server: {\r\n      GEMINI_API_KEY: !!process.env.GEMINI_API_KEY,\r\n      GOOGLE_API_KEY: !!process.env.GOOGLE_API_KEY,\r\n      GOOGLE_GENAI_API_KEY: !!process.env.GOOGLE_GENAI_API_KEY\r\n    },\r\n    client: {\r\n      NEXT_PUBLIC_GEMINI_API_KEY: !!process.env.NEXT_PUBLIC_GEMINI_API_KEY,\r\n      NEXT_PUBLIC_GOOGLE_API_KEY: !!process.env.NEXT_PUBLIC_GOOGLE_API_KEY,\r\n      NEXT_PUBLIC_GOOGLE_GENAI_API_KEY: !!process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY\r\n    }\r\n  });\r\n}\r\n\r\n// Initialize Google GenAI client with Revo 1.0 configuration\r\nconst ai = new GoogleGenerativeAI(apiKey);\r\n\r\n// Revo 1.0 uses Gemini 2.5 Flash Image Preview\r\nconst REVO_1_0_MODEL = 'gemini-2.5-flash-image-preview';\r\n\r\n/**\r\n * Generate content using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Content(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  writingTone: string;\r\n  contentThemes: string[];\r\n  targetAudience: string;\r\n  services: string;\r\n  keyFeatures: string;\r\n  competitiveAdvantages: string;\r\n  dayOfWeek: string;\r\n  currentDate: string;\r\n  primaryColor?: string;\r\n  visualStyle?: string;\r\n}) {\r\n  try {\r\n    console.log('🚀 Revo 1.0: Starting enhanced content generation with real-time context...');\r\n\r\n    // Gather real-time context data\r\n    const realTimeContext = await gatherRealTimeContext(input.businessType, input.location, input.platform);\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build the content generation prompt with enhanced brand context\r\n    const contentPrompt = revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE\r\n      .replace('{businessName}', input.businessName)\r\n      .replace('{businessType}', input.businessType)\r\n      .replace('{platform}', input.platform)\r\n      .replace('{writingTone}', input.writingTone)\r\n      .replace('{location}', input.location)\r\n      .replace('{primaryColor}', input.primaryColor || '#3B82F6')\r\n      .replace('{visualStyle}', input.visualStyle || 'modern')\r\n      .replace('{targetAudience}', input.targetAudience)\r\n      .replace('{services}', input.services || '')\r\n      .replace('{keyFeatures}', input.keyFeatures || '')\r\n      .replace('{competitiveAdvantages}', input.competitiveAdvantages || '')\r\n      .replace('{contentThemes}', input.contentThemes.join(', ') || 'general business content');\r\n\r\n    console.log('📝 Revo 1.0: Generating content with enhanced AI capabilities...');\r\n\r\n    // Generate enhanced caption using advanced copywriting techniques\r\n    const enhancedCaptionPrompt = `You are an elite social media strategist and copywriting expert with deep expertise in the ${input.businessType} industry.\r\nYour mission is to create scroll-stopping content that maximizes engagement, drives conversions, and builds authentic connections.\r\n\r\nCOMPREHENSIVE BUSINESS INTELLIGENCE:\r\n- Business Name: ${input.businessName}\r\n- Industry: ${input.businessType}\r\n- Location: ${input.location}\r\n- Brand Voice: ${input.writingTone}\r\n- Visual Style: ${input.visualStyle || 'modern'}\r\n- Primary Color: ${input.primaryColor || '#3B82F6'}\r\n- Target Audience: ${input.targetAudience}\r\n- Services Offered: ${input.services || 'Professional services'}\r\n- Key Features: ${input.keyFeatures || 'Quality and reliability'}\r\n- Competitive Advantages: ${input.competitiveAdvantages || 'Unique value proposition'}\r\n- Content Themes: ${input.contentThemes.join(', ') || 'Business excellence'}\r\n- Platform: ${input.platform}\r\n- Day: ${input.dayOfWeek}\r\n- Date: ${input.currentDate}\r\n\r\nBRAND IDENTITY INTEGRATION:\r\n- Use business name \"${input.businessName}\" naturally in content\r\n- Reflect ${input.businessType} industry expertise\r\n- Incorporate ${input.location} local relevance\r\n- Match ${input.writingTone} brand voice consistently\r\n- Highlight unique services and competitive advantages\r\n- Appeal to ${input.targetAudience} specifically\r\n\r\nADVANCED COPYWRITING FRAMEWORKS TO USE:\r\n1. **AIDA Framework**: Attention → Interest → Desire → Action\r\n2. **PAS Framework**: Problem → Agitation → Solution\r\n3. **Storytelling Elements**: Character, conflict, resolution\r\n4. **Social Proof Integration**: Success stories, testimonials\r\n\r\nPSYCHOLOGICAL TRIGGERS TO IMPLEMENT:\r\n✅ **Curiosity Gaps**: Intriguing questions that demand answers\r\n✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy\r\n✅ **Authority**: Expert insights, industry knowledge\r\n✅ **Reciprocity**: Valuable tips, free insights\r\n✅ **Social Proof**: Customer success, popularity indicators\r\n\r\nPLATFORM-SPECIFIC OPTIMIZATION FOR ${input.platform.toUpperCase()}:\r\n${input.platform === 'Instagram' ? `\r\n- Visual storytelling, lifestyle integration, authentic moments\r\n- Length: 150-300 words, emoji-rich, story-driven\r\n- Include 2-3 engagement questions\r\n- End with compelling call-to-action` : ''}\r\n${input.platform === 'LinkedIn' ? `\r\n- Professional insights, industry expertise, thought leadership\r\n- Length: 100-200 words, professional tone, value-driven\r\n- Focus on business solutions and career growth\r\n- Minimal but strategic emoji usage` : ''}\r\n${input.platform === 'Facebook' ? `\r\n- Community building, detailed storytelling, discussion starters\r\n- Length: 100-250 words, community-focused\r\n- Local community engagement, family-friendly content\r\n- Encourage sharing and group discussions` : ''}\r\n${input.platform === 'Twitter' ? `\r\n- Trending topics, quick insights, conversation starters\r\n- Length: 50-150 words, concise, witty commentary\r\n- Real-time engagement, thread potential\r\n- Sharp, clever, conversation-starting tone` : ''}\r\n\r\nENGAGEMENT OPTIMIZATION:\r\n🎯 **Hook Techniques**: Surprising statistics, personal anecdotes, thought-provoking questions, bold predictions\r\n🎯 **Interaction Drivers**: \"Comment below with...\", \"Tag someone who...\", \"Share if you agree...\", \"What's your experience with...\"\r\n🎯 **Call-to-Action Mastery**: Create urgency without being pushy, offer clear value, use action-oriented language\r\n\r\nCONTENT REQUIREMENTS:\r\n- Start with a powerful hook using psychological triggers\r\n- Apply a copywriting framework (AIDA, PAS, or storytelling)\r\n- Include 2-3 engagement questions throughout\r\n- Incorporate relevant emojis strategically (${input.platform === 'Instagram' ? '8-12' : input.platform === 'LinkedIn' ? '2-4' : '4-8'} emojis)\r\n- End with a compelling, specific call-to-action\r\n- Make it location-relevant for ${input.location} when appropriate\r\n- NO HASHTAGS in caption (provided separately)\r\n- Create unique, varied content - avoid generic templates\r\n\r\nVARIETY REQUIREMENTS:\r\n- Use different hook styles each time (statistics, questions, stories, bold statements)\r\n- Vary the copywriting framework (rotate between AIDA, PAS, storytelling)\r\n- Change the emotional tone (inspirational, educational, entertaining, motivational)\r\n- Alternate engagement techniques (questions, polls, challenges, tips)\r\n- Mix content angles (behind-the-scenes, customer focus, industry insights, local relevance)\r\n\r\nREAL-TIME CONTEXT INTEGRATION:\r\n${realTimeContext.weather ? `\r\n🌤️ CURRENT WEATHER: ${realTimeContext.weather.temperature}°C, ${realTimeContext.weather.condition}\r\n- Business Impact: ${realTimeContext.weather.business_impact}\r\n- Content Opportunities: ${realTimeContext.weather.content_opportunities}` : ''}\r\n\r\n${realTimeContext.trends.length > 0 ? `\r\n📈 TRENDING TOPICS:\r\n${realTimeContext.trends.map((trend: any, i: number) => `${i + 1}. ${trend.topic} (${trend.category})`).join('\\n')}` : ''}\r\n\r\n${realTimeContext.events.length > 0 ? `\r\n🎪 LOCAL EVENTS:\r\n${realTimeContext.events.map((event: any, i: number) => `${i + 1}. ${event.name} - ${event.venue}`).join('\\n')}` : ''}\r\n\r\n${realTimeContext.news.length > 0 ? `\r\n📰 RELEVANT NEWS:\r\n${realTimeContext.news.map((news: any, i: number) => `${i + 1}. ${news.topic}`).join('\\n')}` : ''}\r\n\r\nCONTEXT INTEGRATION INSTRUCTIONS:\r\n- Strategically reference weather when relevant to business (e.g., seasonal services, weather-dependent activities)\r\n- Incorporate trending topics that align with business values and audience interests\r\n- Mention local events when they create business opportunities or community engagement\r\n- Use news trends to position business as current and relevant\r\n- Only include context that adds genuine value - don't force irrelevant connections\r\n\r\nRANDOMIZATION SEED: ${Date.now() % 1000} (Use this to ensure variety in approach)\r\n\r\nIMPORTANT: Generate ONLY the actual social media caption content. Do NOT include any meta-text, explanations, or descriptions. Start directly with the engaging hook and write as if you are posting for the business.\r\n\r\nExample format:\r\n🚀 Did you know 73% of Kenyan businesses are still using outdated payment systems?\r\n\r\n[Continue with engaging content...]\r\n\r\nWhat's your biggest financial challenge? Drop a comment below! 👇\r\n\r\nNOW GENERATE THE ACTUAL CAPTION WITH SMART CONTEXT INTEGRATION:`;\r\n\r\n    const result = await model.generateContent([\r\n      revo10Prompts.CONTENT_SYSTEM_PROMPT,\r\n      enhancedCaptionPrompt\r\n    ]);\r\n\r\n    const response = await result.response;\r\n    const content = response.text();\r\n\r\n    console.log('📝 Enhanced caption generated:');\r\n    console.log(`- Length: ${content.length} characters`);\r\n    console.log(`- Platform: ${input.platform}`);\r\n    console.log(`- Tone: ${input.writingTone}`);\r\n    console.log(`- Preview: ${content.substring(0, 100)}...`);\r\n\r\n    // Generate strategic hashtags with different categories\r\n    const hashtagPrompt = `Generate strategic hashtags for ${input.businessName} (${input.businessType}) in ${input.location} for ${input.platform}.\r\n\r\nCOMPREHENSIVE BUSINESS CONTEXT:\r\n- Business Name: ${input.businessName}\r\n- Industry: ${input.businessType}\r\n- Location: ${input.location}\r\n- Target Audience: ${input.targetAudience}\r\n- Services Offered: ${input.services}\r\n- Key Features: ${input.keyFeatures}\r\n- Competitive Advantages: ${input.competitiveAdvantages}\r\n- Content Themes: ${input.contentThemes.join(', ')}\r\n- Brand Voice: ${input.writingTone}\r\n- Visual Style: ${input.visualStyle}\r\n\r\nHASHTAG STRATEGY:\r\nCreate 15 hashtags in these categories:\r\n1. Brand/Business (2-3 hashtags): Company name, business type\r\n2. Industry/Niche (3-4 hashtags): Specific to ${input.businessType}\r\n3. Location (2-3 hashtags): ${input.location} and surrounding areas\r\n4. Trending/Popular (2-3 hashtags): Current trending topics in the industry\r\n5. Community/Engagement (2-3 hashtags): Encourage interaction\r\n6. Long-tail (2-3 hashtags): Specific, less competitive phrases\r\n\r\nREQUIREMENTS:\r\n- Mix of high, medium, and low competition hashtags\r\n- Include local hashtags for ${input.location}\r\n- Relevant to ${input.platform} audience\r\n- No spaces in hashtags\r\n- Each hashtag on a new line starting with #\r\n- Focus on discoverability and engagement\r\n\r\nGenerate exactly 15 hashtags:`;\r\n\r\n    const hashtagResult = await model.generateContent(hashtagPrompt);\r\n    const hashtagResponse = await hashtagResult.response;\r\n    const hashtags = hashtagResponse.text()\r\n      .split('\\n')\r\n      .filter(tag => tag.trim().startsWith('#'))\r\n      .map(tag => tag.trim())\r\n      .slice(0, 15);\r\n\r\n    console.log('📱 Generated hashtag strategy:');\r\n    console.log(`- Total hashtags: ${hashtags.length}`);\r\n    console.log(`- Hashtags: ${hashtags.join(' ')}`);\r\n\r\n    // Analyze hashtag categories for logging\r\n    const brandHashtags = hashtags.filter(tag =>\r\n      tag.toLowerCase().includes(input.businessName.toLowerCase().replace(/\\s+/g, '')) ||\r\n      tag.toLowerCase().includes(input.businessType.toLowerCase().replace(/\\s+/g, ''))\r\n    );\r\n    const locationHashtags = hashtags.filter(tag =>\r\n      tag.toLowerCase().includes(input.location.toLowerCase().replace(/\\s+/g, ''))\r\n    );\r\n\r\n    console.log(`- Brand hashtags: ${brandHashtags.length}`);\r\n    console.log(`- Location hashtags: ${locationHashtags.length}`);\r\n\r\n    // Generate structured content components\r\n    const structuredPrompt = `Generate structured content components for ${input.businessName} (${input.businessType}) in ${input.location} for ${input.platform}:\r\n\r\nREQUIREMENTS:\r\n- Headline: 3-5 catchy words maximum (for image overlay)\r\n- Subheadline: 8-14 words maximum (optional, use only if it adds value)\r\n- Call-to-Action: 3-6 words maximum (optional, use only when contextually appropriate)\r\n\r\nGUIDELINES:\r\n- Headline should be punchy and attention-grabbing\r\n- Subheadline should provide context or value proposition\r\n- CTA should be action-oriented and relevant\r\n- Consider the business type: ${input.businessType}\r\n- Make it location-relevant for ${input.location}\r\n- Optimize for ${input.platform} audience\r\n\r\nFORMAT YOUR RESPONSE EXACTLY AS:\r\nHEADLINE: [your headline here]\r\nSUBHEADLINE: [your subheadline here or leave blank if not needed]\r\nCTA: [your call-to-action here or leave blank if not needed]`;\r\n\r\n    const structuredResult = await model.generateContent(structuredPrompt);\r\n    const structuredResponse = await structuredResult.response;\r\n    const structuredText = structuredResponse.text().trim();\r\n\r\n    // Parse the structured response\r\n    const headlineMatch = structuredText.match(/HEADLINE:\\s*(.+)/i);\r\n    const subheadlineMatch = structuredText.match(/SUBHEADLINE:\\s*(.+)/i);\r\n    const ctaMatch = structuredText.match(/CTA:\\s*(.+)/i);\r\n\r\n    const headline = headlineMatch ? headlineMatch[1].trim() : 'Your Business';\r\n    const subheadline = subheadlineMatch && subheadlineMatch[1].trim() && !subheadlineMatch[1].toLowerCase().includes('blank') ? subheadlineMatch[1].trim() : '';\r\n    const callToAction = ctaMatch && ctaMatch[1].trim() && !ctaMatch[1].toLowerCase().includes('blank') ? ctaMatch[1].trim() : '';\r\n\r\n    console.log('📝 Generated content structure:');\r\n    console.log('- Headline:', headline);\r\n    console.log('- Subheadline:', subheadline || '(none)');\r\n    console.log('- CTA:', callToAction || '(none)');\r\n\r\n    console.log('✅ Revo 1.0: Content generated successfully with Gemini 2.5 Flash Image Preview');\r\n\r\n    // Final content package summary\r\n    console.log('📦 Complete content package:');\r\n    console.log(`- Caption: ${content.length} chars`);\r\n    console.log(`- Headline: \"${headline}\"`);\r\n    console.log(`- Subheadline: \"${subheadline || 'None'}\"`);\r\n    console.log(`- CTA: \"${callToAction || 'None'}\"`);\r\n    console.log(`- Hashtags: ${hashtags.length} strategic tags`);\r\n    console.log(`- Platform: ${input.platform} optimized`);\r\n\r\n    return {\r\n      content: content.trim(),\r\n      hashtags: hashtags,\r\n      catchyWords: headline, // Use headline as catchy words for image\r\n      subheadline: subheadline,\r\n      callToAction: callToAction,\r\n      headline: headline, // Add headline as separate field\r\n      realTimeContext: realTimeContext, // Pass context to image generator\r\n      variants: [{\r\n        platform: input.platform,\r\n        aspectRatio: '1:1',\r\n        imageUrl: '' // Will be generated separately\r\n      }]\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 1.0: Content generation failed:', error);\r\n    throw new Error(`Revo 1.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate design using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Design(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  primaryColor: string;\r\n  accentColor: string;\r\n  backgroundColor: string;\r\n  imageText: string;\r\n}) {\r\n  try {\r\n    console.log('🎨 Revo 1.0: Starting design generation with Gemini 2.5 Flash Image Preview...');\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build the design generation prompt\r\n    const designPrompt = revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE\r\n      .replace('{businessName}', input.businessName)\r\n      .replace('{businessType}', input.businessType)\r\n      .replace('{platform}', input.platform)\r\n      .replace('{visualStyle}', input.visualStyle)\r\n      .replace('{primaryColor}', input.primaryColor)\r\n      .replace('{accentColor}', input.accentColor)\r\n      .replace('{backgroundColor}', input.backgroundColor)\r\n      .replace('{imageText}', input.imageText);\r\n\r\n    console.log('🎨 Revo 1.0: Generating design with enhanced AI capabilities...');\r\n\r\n    const result = await model.generateContent([\r\n      revo10Prompts.DESIGN_SYSTEM_PROMPT,\r\n      designPrompt\r\n    ]);\r\n\r\n    const response = await result.response;\r\n    const design = response.text();\r\n\r\n    console.log('✅ Revo 1.0: Design generated successfully with Gemini 2.5 Flash Image Preview');\r\n\r\n    return {\r\n      design: design.trim(),\r\n      aspectRatio: '1:1',\r\n      resolution: '2048x2048',\r\n      quality: 'enhanced'\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 1.0: Design generation failed:', error);\r\n    throw new Error(`Revo 1.0 design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate image using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Image(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  primaryColor: string;\r\n  accentColor?: string;\r\n  backgroundColor?: string;\r\n  imageText: string;\r\n  designDescription: string;\r\n  logoDataUrl?: string;\r\n  location?: string;\r\n  headline?: string;\r\n  subheadline?: string;\r\n  callToAction?: string;\r\n  realTimeContext?: any;\r\n}) {\r\n  try {\r\n    console.log('🖼️ Revo 1.0: Starting image generation with Gemini 2.5 Flash Image Preview...');\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build advanced professional design prompt\r\n    const brandInfo = input.location ? ` based in ${input.location}` : '';\r\n    const colorScheme = `Primary: ${input.primaryColor} (60% dominant), Accent: ${input.accentColor || '#1E40AF'} (30% secondary), Background: ${input.backgroundColor || '#FFFFFF'} (10% highlights)`;\r\n    const logoInstruction = input.logoDataUrl ?\r\n      'Use the provided brand logo (do NOT create new logo - integrate existing one naturally)' :\r\n      'Create professional design without logo overlay';\r\n\r\n    // Prepare structured content display with hierarchy\r\n    const contentStructure = [];\r\n    if (input.headline) contentStructure.push(`PRIMARY (Largest, most prominent): \"${input.headline}\"`);\r\n    if (input.subheadline) contentStructure.push(`SECONDARY (Medium, supporting): \"${input.subheadline}\"`);\r\n    if (input.callToAction) contentStructure.push(`TERTIARY (Smaller, action-oriented): \"${input.callToAction}\"`);\r\n\r\n    // Get advanced design features\r\n    const businessDesignDNA = getBusinessDesignDNA(input.businessType);\r\n    const platformOptimization = getPlatformOptimization(input.platform);\r\n    const shouldIncludePeople = shouldIncludePeopleInDesign(input.businessType, input.location || 'Global', input.visualStyle);\r\n    const peopleInstructions = shouldIncludePeople ? getAdvancedPeopleInstructions(input.businessType, input.location || 'Global') : '';\r\n    const culturalContext = getLocalCulturalContext(input.location || 'Global');\r\n\r\n    console.log('👥 People Integration:', shouldIncludePeople ? 'Enabled' : 'Disabled');\r\n    console.log('🌍 Cultural Context:', culturalContext.substring(0, 100) + '...');\r\n\r\n    // Generate design variation seed for unique designs\r\n    const designSeed = Date.now() % 10000;\r\n    const designVariations = getDesignVariations(designSeed);\r\n\r\n    const imagePrompt = `Create a professional-grade 2048x2048 social media design that surpasses Canva quality for ${input.businessName} (${input.businessType})${brandInfo}.\r\n\r\nBUSINESS CONTEXT:\r\n- Business: ${input.businessName}\r\n- Industry: ${input.businessType}\r\n- Platform: ${input.platform}\r\n- Visual Style: ${input.visualStyle}\r\n- Location: ${input.location || 'Global'}\r\n\r\nBRAND IDENTITY SYSTEM:\r\n- Color Scheme: ${colorScheme}\r\n- Logo Integration: ${logoInstruction}\r\n- Brand Personality: Professional, modern, trustworthy\r\n\r\nSTRUCTURED CONTENT HIERARCHY:\r\n${contentStructure.length > 0 ? contentStructure.join('\\n') : `- PRIMARY MESSAGE: ${input.imageText}`}\r\n\r\nADVANCED COMPOSITION REQUIREMENTS:\r\n- Apply Rule of Thirds: Position key elements along grid intersections\r\n- Create strong focal point with primary message as center of attention\r\n- Use sophisticated asymmetrical balance for modern appeal\r\n- Implement clear visual hierarchy: ${input.headline || 'Headline'} → ${input.subheadline || 'Supporting text'} → ${input.callToAction || 'Call-to-action'}\r\n- Strategic negative space for premium, uncluttered feel\r\n- Leading lines and visual flow to guide eye movement\r\n\r\nPLATFORM-SPECIFIC OPTIMIZATION:\r\n${platformOptimization}\r\n\r\nBUSINESS TYPE DESIGN DNA:\r\n${businessDesignDNA}\r\n\r\nTYPOGRAPHY EXCELLENCE:\r\n- Primary headline: Bold, attention-grabbing, high contrast against background\r\n- Secondary text: Supporting, readable, complementary to headline\r\n- Ensure 4.5:1 contrast ratio minimum for accessibility\r\n- Professional font pairing (maximum 2-3 font families)\r\n- Proper letter spacing, line height, and alignment\r\n- Scale typography appropriately for mobile viewing\r\n\r\nCOLOR IMPLEMENTATION STRATEGY:\r\n- Dominant color (${input.primaryColor}): 60% usage for backgrounds, main elements\r\n- Secondary color (${input.accentColor || '#1E40AF'}): 30% for supporting elements, borders\r\n- Accent color (${input.backgroundColor || '#FFFFFF'}): 10% for highlights, details\r\n- Apply color psychology appropriate for ${input.businessType} industry\r\n- Ensure sufficient contrast between all text and background elements\r\n\r\nMODERN DESIGN ELEMENTS:\r\n- Subtle gradients and depth effects for dimensionality\r\n- Clean geometric shapes with consistent border radius\r\n- Professional drop shadows and lighting effects\r\n- Premium visual texture and sophisticated finish\r\n- Consistent spacing and alignment throughout\r\n- Modern minimalist approach with purposeful elements\r\n\r\n${shouldIncludePeople ? peopleInstructions : ''}\r\n\r\nCULTURAL & LOCAL INTEGRATION:\r\n- Cultural Context: ${culturalContext}\r\n- Incorporate local aesthetic preferences and visual language\r\n- Use culturally appropriate colors, patterns, and design elements\r\n- Ensure authentic representation of local community and values\r\n- Blend traditional elements with modern design sensibilities\r\n- Show contemporary local lifestyle and business culture\r\n- Use authentic local fashion, architecture, and environmental elements\r\n\r\nQUALITY STANDARDS:\r\n- Professional agency-level design quality that surpasses Canva\r\n- Superior visual storytelling with authentic human connections\r\n- Print-ready resolution and crystal-clear clarity\r\n- Perfect text rendering with no pixelation or artifacts\r\n- Sophisticated visual appeal that commands attention and drives engagement\r\n- Commercial-grade finish suitable for professional marketing use\r\n- Design that converts viewers into customers through emotional connection\r\n- Authentic representation that builds trust and relatability\r\n- Cultural sensitivity and local relevance that resonates with target audience\r\n- Premium aesthetic that positions the brand as industry-leading\r\n- Visual hierarchy that guides the eye and communicates value proposition clearly\r\n\r\nDESIGN VARIATION & UNIQUENESS:\r\n**SPECIFIC DESIGN STYLE: ${designVariations.style}**\r\n- Layout Approach: ${designVariations.layout}\r\n- Composition Style: ${designVariations.composition}\r\n- Visual Mood: ${designVariations.mood}\r\n- Key Elements: ${designVariations.elements}\r\n- Create a completely unique design that stands out from typical social media posts\r\n- Avoid repetitive layouts, compositions, or visual treatments\r\n- Use creative angles, perspectives, and visual storytelling techniques\r\n- Ensure each design feels fresh, original, and professionally crafted\r\n- Incorporate unexpected visual elements that enhance the message\r\n- NEVER repeat the same visual concept, layout, or composition from previous generations\r\n- Generate completely different visual approaches each time\r\n\r\nREAL-TIME CONTEXT INTEGRATION:\r\n${input.realTimeContext?.weather ? `\r\n🌤️ WEATHER CONTEXT: ${input.realTimeContext.weather.temperature}°C, ${input.realTimeContext.weather.condition}\r\n- Visual Mood: Adapt colors and imagery to reflect current weather\r\n- Seasonal Elements: Include weather-appropriate visual cues when relevant` : ''}\r\n\r\n${input.realTimeContext?.trends?.length > 0 ? `\r\n📈 TRENDING VISUAL THEMES:\r\n${input.realTimeContext.trends.slice(0, 3).map((trend: any, i: number) => `${i + 1}. ${trend.topic} - Consider visual elements that align with this trend`).join('\\n')}` : ''}\r\n\r\n${input.realTimeContext?.events?.length > 0 ? `\r\n🎪 LOCAL EVENT INSPIRATION:\r\n${input.realTimeContext.events.slice(0, 2).map((event: any, i: number) => `${i + 1}. ${event.name} - Use event energy/theme for visual inspiration`).join('\\n')}` : ''}\r\n\r\nCONTEXT-AWARE DESIGN INSTRUCTIONS:\r\n- Incorporate weather mood into color temperature and visual atmosphere\r\n- Reference trending topics through subtle visual elements or color choices\r\n- Use local event energy to inform design dynamism and visual style\r\n- Ensure context integration feels natural and enhances the core message\r\n\r\nTECHNICAL SPECIFICATIONS:\r\n- Resolution: 2048x2048 pixels (high-definition)\r\n- Aspect ratio: 1:1 (perfect square)\r\n- Color space: sRGB for digital display\r\n- Text readability: Optimized for mobile viewing\r\n- File quality: Maximum clarity and sharpness\r\n- Use contrasting colors for text readability\r\n- Create visual separation between different text elements\r\n- Professional typography that matches the brand personality\r\n\r\nDESIGN REQUIREMENTS:\r\n- Create depth and visual interest with gradients or subtle patterns\r\n- IMPORTANT: If a logo is provided as an image, use that exact logo - do not create, modify, or redesign it\r\n- Position the logo appropriately within the design layout\r\n- Balance text elements with visual design elements\r\n- Ensure the overall composition is visually appealing and professional`;\r\n\r\n    console.log('🎨 Brand-aware prompt created with colors:', colorScheme);\r\n\r\n    console.log('🖼️ Revo 1.0: Generating image with enhanced AI capabilities...');\r\n    console.log('🎨 Advanced Features:');\r\n    console.log(`  👥 People Integration: ${shouldIncludePeople ? 'Enabled' : 'Disabled'}`);\r\n    console.log(`  🌍 Cultural Context: ${input.location || 'Global'}`);\r\n    console.log(`  🎭 Visual Style: ${input.visualStyle}`);\r\n    console.log(`  🏢 Business DNA: ${input.businessType} optimized`);\r\n    console.log(`  🎲 Design Variation: ${designVariations.style} (Seed: ${designSeed})`);\r\n\r\n    // Prepare the generation request with logo if available\r\n    const generationParts = [\r\n      'You are an expert graphic designer using Gemini 2.5 Flash Image Preview. Create professional, high-quality social media images with perfect text rendering and 2048x2048 resolution.',\r\n      imagePrompt\r\n    ];\r\n\r\n    // If logo is provided, include it in the generation\r\n    if (input.logoDataUrl) {\r\n      console.log('🏢 Including brand logo in image generation...');\r\n\r\n      // Extract the base64 data and mime type from the data URL\r\n      const logoMatch = input.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);\r\n      if (logoMatch) {\r\n        const [, mimeType, base64Data] = logoMatch;\r\n\r\n        generationParts.push({\r\n          inlineData: {\r\n            data: base64Data,\r\n            mimeType: mimeType\r\n          }\r\n        });\r\n\r\n        // Update the prompt to reference the provided logo\r\n        const logoPrompt = `\\n\\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;\r\n        generationParts[1] = imagePrompt + logoPrompt;\r\n      } else {\r\n        console.log('⚠️ Invalid logo data URL format, proceeding without logo');\r\n      }\r\n    }\r\n\r\n    const result = await model.generateContent(generationParts);\r\n\r\n    const response = await result.response;\r\n\r\n    // Extract image data from Gemini response\r\n    const parts = response.candidates?.[0]?.content?.parts || [];\r\n    let imageUrl = '';\r\n\r\n    for (const part of parts) {\r\n      if (part.inlineData) {\r\n        const imageData = part.inlineData.data;\r\n        const mimeType = part.inlineData.mimeType;\r\n        imageUrl = `data:${mimeType};base64,${imageData}`;\r\n        console.log('🖼️ Revo 1.0: Image data extracted successfully');\r\n        break;\r\n      }\r\n    }\r\n\r\n    if (!imageUrl) {\r\n      // Fallback: try to get text response if no image data\r\n      const textResponse = response.text();\r\n      console.log('⚠️ Revo 1.0: No image data found, got text response instead');\r\n      throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');\r\n    }\r\n\r\n    console.log('✅ Revo 1.0: Image generated successfully with Gemini 2.5 Flash Image Preview');\r\n\r\n    return {\r\n      imageUrl: imageUrl,\r\n      aspectRatio: '1:1',\r\n      resolution: '2048x2048',\r\n      quality: 'enhanced'\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 1.0: Image generation failed:', error);\r\n    throw new Error(`Revo 1.0 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Health check for Revo 1.0 service\r\n */\r\nexport async function checkRevo10Health() {\r\n  try {\r\n    const model = ai.getGenerativeModel({ model: REVO_1_0_MODEL });\r\n    const result = await model.generateContent('Hello');\r\n    const response = await result.response;\r\n\r\n    return {\r\n      healthy: true,\r\n      model: REVO_1_0_MODEL,\r\n      response: response.text().substring(0, 50) + '...',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      healthy: false,\r\n      model: REVO_1_0_MODEL,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get Revo 1.0 service information\r\n */\r\nexport function getRevo10ServiceInfo() {\r\n  return {\r\n    model: REVO_1_0_MODEL,\r\n    version: '1.0.0',\r\n    status: 'enhanced',\r\n    aiService: 'gemini-2.5-flash-image-preview',\r\n    capabilities: [\r\n      'Enhanced content generation',\r\n      'High-resolution image support (2048x2048)',\r\n      'Perfect text rendering',\r\n      'Advanced AI capabilities',\r\n      'Enhanced brand consistency'\r\n    ],\r\n    pricing: {\r\n      contentGeneration: 1.5,\r\n      designGeneration: 1.5,\r\n      tier: 'enhanced'\r\n    },\r\n    lastUpdated: '2025-01-27'\r\n  };\r\n}\r\n\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;AAEA;;;AAEA,qDAAqD;AACrD,8DAA8D;AAE9D,kDAAkD;AAClD,SAAS,qBAAqB,YAAoB;IAChD,MAAM,YAAoC;QACxC,cAAc;QACd,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,aAAa;QACb,UAAU;QACV,eAAe;QACf,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,aAAa,WAAW,GAAG,IAAI,SAAS,CAAC,UAAU;AACtE;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,gBAAwC;QAC5C,aAAa,CAAC;;;;;;mDAMiC,CAAC;QAEhD,YAAY,CAAC;;;;;;+CAM8B,CAAC;QAE5C,YAAY,CAAC;;;;;;uCAMsB,CAAC;QAEpC,WAAW,CAAC;;;;;;+BAMe,CAAC;QAE5B,WAAW,CAAC;;;;;+CAK+B,CAAC;IAC9C;IAEA,OAAO,aAAa,CAAC,SAAS,WAAW,GAAG,IAAI,aAAa,CAAC,UAAU;AAC1E;AAEA,yEAAyE;AACzE,eAAe,sBAAsB,YAAoB,EAAE,QAAgB,EAAE,QAAgB;IAC3F,QAAQ,GAAG,CAAC;IAEZ,MAAM,UAAe;QACnB,QAAQ,EAAE;QACV,SAAS;QACT,QAAQ,EAAE;QACV,MAAM,EAAE;QACR,aAAa;YACX,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YACpE,OAAO,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,OAAO;YAAO;YAC9D,QAAQ;YACR,WAAW;QACb;IACF;IAEA,IAAI;QACF,iEAAiE;QACjE,QAAQ,GAAG,CAAC;QACZ,QAAQ,MAAM,GAAG,yBAAyB,cAAc;QACxD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAEpE,mDAAmD;QACnD,QAAQ,GAAG,CAAC;QACZ,QAAQ,OAAO,GAAG,uBAAuB;QACzC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ,OAAO,CAAC,SAAS,EAAE;QAE7D,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,QAAQ,MAAM,GAAG,2BAA2B,cAAc;QAC1D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC;QAErE,QAAQ,GAAG,CAAC;QACZ,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,SAAS,yBAAyB;IAC3C;AACF;AAEA,wCAAwC;AACxC,SAAS,4BAA4B,YAAoB,EAAE,QAAgB,EAAE,WAAmB;IAC9F,MAAM,sBAAsB;QAC1B;QAAc;QAAW;QAAc;QAAa;QAAU;QAC9D;QAAU;QAAY;QAAc;QAAY;QAAe;QAC/D;QAAc;QAAa;QAAU;QAAe;KACrD;IAED,OAAO,oBAAoB,IAAI,CAAC,CAAA,OAC9B,aAAa,WAAW,GAAG,QAAQ,CAAC,SACpC,gBAAgB,eAChB,gBAAgB;AAEpB;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,mBAA2C;QAC/C,SAAS;QACT,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,YAAY;QACZ,WAAW;IACb;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,kBAAmB;QAC7D,IAAI,YAAY,QAAQ,CAAC,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO,gBAAgB,CAAC,UAAU;AACpC;AAEA,SAAS,oBAAoB,IAAY;IACvC,MAAM,aAAa;QACjB;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;KACD;IAED,OAAO,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;AAC7C;AAEA,SAAS,8BAA8B,YAAoB,EAAE,QAAgB;IAC3E,MAAM,kBAAkB,wBAAwB;IAEhD,OAAO,CAAC;;;;;oBAKU,EAAE,gBAAgB;;;;gCAIN,EAAE,aAAa;;;;;;;;;;sEAUuB,CAAC;AACvE;AAEA,0CAA0C;AAC1C,SAAS;IACP,MAAM,QAAQ,IAAI,OAAO,QAAQ;IACjC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,IAAI,OAAO;IACtC,OAAO;AACT;AAEA,SAAS;IACP,MAAM,OAAO,IAAI,OAAO,QAAQ;IAChC,IAAI,QAAQ,KAAK,OAAO,IAAI,OAAO;IACnC,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO;IACpC,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO;IACpC,OAAO;AACT;AAEA,SAAS,yBAAyB,YAAoB,EAAE,QAAgB;IACtE,MAAM,SAAS;QACb;YAAE,OAAO,GAAG,aAAa,kBAAkB,CAAC;YAAE,UAAU;YAAY,WAAW;QAAO;QACtF;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAO;QAC7E;YAAE,OAAO;YAA0B,UAAU;YAAc,WAAW;QAAS;QAC/E;YAAE,OAAO;YAAoC,UAAU;YAAY,WAAW;QAAO;QACrF;YAAE,OAAO;YAAkC,UAAU;YAAU,WAAW;QAAS;KACpF;IACD,OAAO,OAAO,KAAK,CAAC,GAAG;AACzB;AAEA,SAAS,uBAAuB,QAAgB;IAC9C,0DAA0D;IAC1D,MAAM,SAAS;IACf,MAAM,WAAW;QACf,UAAU;YAAE,WAAW;YAAwB,iBAAiB;YAAwC,uBAAuB;QAAuC;QACtK,UAAU;YAAE,WAAW;YAAqB,iBAAiB;YAAmC,uBAAuB;QAAqD;QAC5K,QAAQ;YAAE,WAAW;YAAuB,iBAAiB;YAAkC,uBAAuB;QAAsC;QAC5J,UAAU;YAAE,WAAW;YAAyB,iBAAiB;YAAsC,uBAAuB;QAAuC;IACvK;IAEA,OAAO;QACL,aAAa;QACb,WAAW,QAAQ,CAAC,OAAgC,CAAC,SAAS;QAC9D,iBAAiB,QAAQ,CAAC,OAAgC,CAAC,eAAe;QAC1E,uBAAuB,QAAQ,CAAC,OAAgC,CAAC,qBAAqB;IACxF;AACF;AAEA,SAAS,2BAA2B,YAAoB,EAAE,QAAgB;IACxE,MAAM,gBAAgB;QACpB;YAAE,MAAM,GAAG,SAAS,cAAc,CAAC;YAAE,OAAO;YAA2B,WAAW;QAAa;QAC/F;YAAE,MAAM,GAAG,aAAa,kBAAkB,CAAC;YAAE,OAAO;YAAqB,WAAW;QAAW;QAC/F;YAAE,MAAM;YAA6B,OAAO;YAAoB,WAAW;QAAY;KACxF;IACD,OAAO,cAAc,KAAK,CAAC,GAAG;AAChC;AAEA,6DAA6D;AAC7D,MAAM,SACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,IAChC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,gCAAgC;AAE9C,IAAI,CAAC,QAAQ;IACX,QAAQ,KAAK,CAAC;IACd,QAAQ,KAAK,CAAC,uBAAuB;QACnC,QAAQ;YACN,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAC5C,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAC5C,sBAAsB,CAAC,CAAC,QAAQ,GAAG,CAAC,oBAAoB;QAC1D;QACA,QAAQ;YACN,4BAA4B,CAAC,CAAC,QAAQ,GAAG,CAAC,0BAA0B;YACpE,4BAA4B,CAAC,CAAC,QAAQ,GAAG,CAAC,0BAA0B;YACpE,kCAAkC,CAAC,CAAC,QAAQ,GAAG,CAAC,gCAAgC;QAClF;IACF;AACF;AAEA,6DAA6D;AAC7D,MAAM,KAAK,IAAI,8JAAA,CAAA,qBAAkB,CAAC;AAElC,+CAA+C;AAC/C,MAAM,iBAAiB;AAKhB,eAAe,sBAAsB,KAe3C;IACC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,gCAAgC;QAChC,MAAM,kBAAkB,MAAM,sBAAsB,MAAM,YAAY,EAAE,MAAM,QAAQ,EAAE,MAAM,QAAQ;QAEtG,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,kEAAkE;QAClE,MAAM,gBAAgB,2JAAA,CAAA,gBAAa,CAAC,4BAA4B,CAC7D,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,iBAAiB,MAAM,WAAW,EAC1C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,kBAAkB,MAAM,YAAY,IAAI,WAChD,OAAO,CAAC,iBAAiB,MAAM,WAAW,IAAI,UAC9C,OAAO,CAAC,oBAAoB,MAAM,cAAc,EAChD,OAAO,CAAC,cAAc,MAAM,QAAQ,IAAI,IACxC,OAAO,CAAC,iBAAiB,MAAM,WAAW,IAAI,IAC9C,OAAO,CAAC,2BAA2B,MAAM,qBAAqB,IAAI,IAClE,OAAO,CAAC,mBAAmB,MAAM,aAAa,CAAC,IAAI,CAAC,SAAS;QAEhE,QAAQ,GAAG,CAAC;QAEZ,kEAAkE;QAClE,MAAM,wBAAwB,CAAC,2FAA2F,EAAE,MAAM,YAAY,CAAC;;;;iBAIlI,EAAE,MAAM,YAAY,CAAC;YAC1B,EAAE,MAAM,YAAY,CAAC;YACrB,EAAE,MAAM,QAAQ,CAAC;eACd,EAAE,MAAM,WAAW,CAAC;gBACnB,EAAE,MAAM,WAAW,IAAI,SAAS;iBAC/B,EAAE,MAAM,YAAY,IAAI,UAAU;mBAChC,EAAE,MAAM,cAAc,CAAC;oBACtB,EAAE,MAAM,QAAQ,IAAI,wBAAwB;gBAChD,EAAE,MAAM,WAAW,IAAI,0BAA0B;0BACvC,EAAE,MAAM,qBAAqB,IAAI,2BAA2B;kBACpE,EAAE,MAAM,aAAa,CAAC,IAAI,CAAC,SAAS,sBAAsB;YAChE,EAAE,MAAM,QAAQ,CAAC;OACtB,EAAE,MAAM,SAAS,CAAC;QACjB,EAAE,MAAM,WAAW,CAAC;;;qBAGP,EAAE,MAAM,YAAY,CAAC;UAChC,EAAE,MAAM,YAAY,CAAC;cACjB,EAAE,MAAM,QAAQ,CAAC;QACvB,EAAE,MAAM,WAAW,CAAC;;YAEhB,EAAE,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;mCAeA,EAAE,MAAM,QAAQ,CAAC,WAAW,GAAG;AAClE,EAAE,MAAM,QAAQ,KAAK,cAAc,CAAC;;;;oCAIA,CAAC,GAAG,GAAG;AAC3C,EAAE,MAAM,QAAQ,KAAK,aAAa,CAAC;;;;mCAIA,CAAC,GAAG,GAAG;AAC1C,EAAE,MAAM,QAAQ,KAAK,aAAa,CAAC;;;;yCAIM,CAAC,GAAG,GAAG;AAChD,EAAE,MAAM,QAAQ,KAAK,YAAY,CAAC;;;;2CAIS,CAAC,GAAG,GAAG;;;;;;;;;;;6CAWL,EAAE,MAAM,QAAQ,KAAK,cAAc,SAAS,MAAM,QAAQ,KAAK,aAAa,QAAQ,MAAM;;gCAEvG,EAAE,MAAM,QAAQ,CAAC;;;;;;;;;;;;AAYjD,EAAE,gBAAgB,OAAO,GAAG,CAAC;qBACR,EAAE,gBAAgB,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,gBAAgB,OAAO,CAAC,SAAS,CAAC;mBAChF,EAAE,gBAAgB,OAAO,CAAC,eAAe,CAAC;yBACpC,EAAE,gBAAgB,OAAO,CAAC,qBAAqB,EAAE,GAAG,GAAG;;AAEhF,EAAE,gBAAgB,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;;AAEvC,EAAE,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAY,IAAc,GAAG,IAAI,EAAE,EAAE,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;AAE1H,EAAE,gBAAgB,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;;AAEvC,EAAE,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAY,IAAc,GAAG,IAAI,EAAE,EAAE,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;AAEtH,EAAE,gBAAgB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;;AAErC,EAAE,gBAAgB,IAAI,CAAC,GAAG,CAAC,CAAC,MAAW,IAAc,GAAG,IAAI,EAAE,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;;;;;;;;oBAS9E,EAAE,KAAK,GAAG,KAAK,KAAK;;;;;;;;;;;+DAWuB,CAAC;QAE5D,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YACzC,2JAAA,CAAA,gBAAa,CAAC,qBAAqB;YACnC;SACD;QAED,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,UAAU,SAAS,IAAI;QAE7B,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,MAAM,CAAC,WAAW,CAAC;QACpD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM,QAAQ,EAAE;QAC3C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,WAAW,EAAE;QAC1C,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAExD,wDAAwD;QACxD,MAAM,gBAAgB,CAAC,gCAAgC,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC;;;iBAGlI,EAAE,MAAM,YAAY,CAAC;YAC1B,EAAE,MAAM,YAAY,CAAC;YACrB,EAAE,MAAM,QAAQ,CAAC;mBACV,EAAE,MAAM,cAAc,CAAC;oBACtB,EAAE,MAAM,QAAQ,CAAC;gBACrB,EAAE,MAAM,WAAW,CAAC;0BACV,EAAE,MAAM,qBAAqB,CAAC;kBACtC,EAAE,MAAM,aAAa,CAAC,IAAI,CAAC,MAAM;eACpC,EAAE,MAAM,WAAW,CAAC;gBACnB,EAAE,MAAM,WAAW,CAAC;;;;;8CAKU,EAAE,MAAM,YAAY,CAAC;4BACvC,EAAE,MAAM,QAAQ,CAAC;;;;;;;6BAOhB,EAAE,MAAM,QAAQ,CAAC;cAChC,EAAE,MAAM,QAAQ,CAAC;;;;;6BAKF,CAAC;QAE1B,MAAM,gBAAgB,MAAM,MAAM,eAAe,CAAC;QAClD,MAAM,kBAAkB,MAAM,cAAc,QAAQ;QACpD,MAAM,WAAW,gBAAgB,IAAI,GAClC,KAAK,CAAC,MACN,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,UAAU,CAAC,MACpC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,KAAK,CAAC,GAAG;QAEZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,MAAM,EAAE;QAClD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC,MAAM;QAE/C,yCAAyC;QACzC,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,MACpC,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,QAC5E,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;QAE9E,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,MACvC,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;QAG1E,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,cAAc,MAAM,EAAE;QACvD,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,iBAAiB,MAAM,EAAE;QAE7D,yCAAyC;QACzC,MAAM,mBAAmB,CAAC,2CAA2C,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC;;;;;;;;;;;8BAWnI,EAAE,MAAM,YAAY,CAAC;gCACnB,EAAE,MAAM,QAAQ,CAAC;eAClC,EAAE,MAAM,QAAQ,CAAC;;;;;4DAK4B,CAAC;QAEzD,MAAM,mBAAmB,MAAM,MAAM,eAAe,CAAC;QACrD,MAAM,qBAAqB,MAAM,iBAAiB,QAAQ;QAC1D,MAAM,iBAAiB,mBAAmB,IAAI,GAAG,IAAI;QAErD,gCAAgC;QAChC,MAAM,gBAAgB,eAAe,KAAK,CAAC;QAC3C,MAAM,mBAAmB,eAAe,KAAK,CAAC;QAC9C,MAAM,WAAW,eAAe,KAAK,CAAC;QAEtC,MAAM,WAAW,gBAAgB,aAAa,CAAC,EAAE,CAAC,IAAI,KAAK;QAC3D,MAAM,cAAc,oBAAoB,gBAAgB,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK;QAC1J,MAAM,eAAe,YAAY,QAAQ,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK;QAE3H,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,eAAe;QAC3B,QAAQ,GAAG,CAAC,kBAAkB,eAAe;QAC7C,QAAQ,GAAG,CAAC,UAAU,gBAAgB;QAEtC,QAAQ,GAAG,CAAC;QAEZ,gCAAgC;QAChC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC,MAAM,CAAC;QAChD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACvC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,eAAe,OAAO,CAAC,CAAC;QACvD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,gBAAgB,OAAO,CAAC,CAAC;QAChD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS,MAAM,CAAC,eAAe,CAAC;QAC3D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,UAAU,CAAC;QAErD,OAAO;YACL,SAAS,QAAQ,IAAI;YACrB,UAAU;YACV,aAAa;YACb,aAAa;YACb,cAAc;YACd,UAAU;YACV,iBAAiB;YACjB,UAAU;gBAAC;oBACT,UAAU,MAAM,QAAQ;oBACxB,aAAa;oBACb,UAAU,GAAG,+BAA+B;gBAC9C;aAAE;QACJ;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,qBAAqB,KAS1C;IACC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,qCAAqC;QACrC,MAAM,eAAe,2JAAA,CAAA,gBAAa,CAAC,2BAA2B,CAC3D,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,iBAAiB,MAAM,WAAW,EAC1C,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,iBAAiB,MAAM,WAAW,EAC1C,OAAO,CAAC,qBAAqB,MAAM,eAAe,EAClD,OAAO,CAAC,eAAe,MAAM,SAAS;QAEzC,QAAQ,GAAG,CAAC;QAEZ,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YACzC,2JAAA,CAAA,gBAAa,CAAC,oBAAoB;YAClC;SACD;QAED,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,SAAS,SAAS,IAAI;QAE5B,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,QAAQ,OAAO,IAAI;YACnB,aAAa;YACb,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAClH;AACF;AAKO,eAAe,oBAAoB,KAgBzC;IACC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,4CAA4C;QAC5C,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM,QAAQ,EAAE,GAAG;QACnE,MAAM,cAAc,CAAC,SAAS,EAAE,MAAM,YAAY,CAAC,yBAAyB,EAAE,MAAM,WAAW,IAAI,UAAU,8BAA8B,EAAE,MAAM,eAAe,IAAI,UAAU,iBAAiB,CAAC;QAClM,MAAM,kBAAkB,MAAM,WAAW,GACvC,4FACA;QAEF,oDAAoD;QACpD,MAAM,mBAAmB,EAAE;QAC3B,IAAI,MAAM,QAAQ,EAAE,iBAAiB,IAAI,CAAC,CAAC,oCAAoC,EAAE,MAAM,QAAQ,CAAC,CAAC,CAAC;QAClG,IAAI,MAAM,WAAW,EAAE,iBAAiB,IAAI,CAAC,CAAC,iCAAiC,EAAE,MAAM,WAAW,CAAC,CAAC,CAAC;QACrG,IAAI,MAAM,YAAY,EAAE,iBAAiB,IAAI,CAAC,CAAC,sCAAsC,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC;QAE5G,+BAA+B;QAC/B,MAAM,oBAAoB,qBAAqB,MAAM,YAAY;QACjE,MAAM,uBAAuB,wBAAwB,MAAM,QAAQ;QACnE,MAAM,sBAAsB,4BAA4B,MAAM,YAAY,EAAE,MAAM,QAAQ,IAAI,UAAU,MAAM,WAAW;QACzH,MAAM,qBAAqB,sBAAsB,8BAA8B,MAAM,YAAY,EAAE,MAAM,QAAQ,IAAI,YAAY;QACjI,MAAM,kBAAkB,wBAAwB,MAAM,QAAQ,IAAI;QAElE,QAAQ,GAAG,CAAC,0BAA0B,sBAAsB,YAAY;QACxE,QAAQ,GAAG,CAAC,wBAAwB,gBAAgB,SAAS,CAAC,GAAG,OAAO;QAExE,oDAAoD;QACpD,MAAM,aAAa,KAAK,GAAG,KAAK;QAChC,MAAM,mBAAmB,oBAAoB;QAE7C,MAAM,cAAc,CAAC,2FAA2F,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC,CAAC,EAAE,UAAU;;;YAGjK,EAAE,MAAM,YAAY,CAAC;YACrB,EAAE,MAAM,YAAY,CAAC;YACrB,EAAE,MAAM,QAAQ,CAAC;gBACb,EAAE,MAAM,WAAW,CAAC;YACxB,EAAE,MAAM,QAAQ,IAAI,SAAS;;;gBAGzB,EAAE,YAAY;oBACV,EAAE,gBAAgB;;;;AAItC,EAAE,iBAAiB,MAAM,GAAG,IAAI,iBAAiB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,MAAM,SAAS,EAAE,CAAC;;;;;;oCAMlE,EAAE,MAAM,QAAQ,IAAI,WAAW,GAAG,EAAE,MAAM,WAAW,IAAI,kBAAkB,GAAG,EAAE,MAAM,YAAY,IAAI,iBAAiB;;;;;AAK3J,EAAE,qBAAqB;;;AAGvB,EAAE,kBAAkB;;;;;;;;;;;kBAWF,EAAE,MAAM,YAAY,CAAC;mBACpB,EAAE,MAAM,WAAW,IAAI,UAAU;gBACpC,EAAE,MAAM,eAAe,IAAI,UAAU;yCACZ,EAAE,MAAM,YAAY,CAAC;;;;;;;;;;;AAW9D,EAAE,sBAAsB,qBAAqB,GAAG;;;oBAG5B,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;yBAsBb,EAAE,iBAAiB,KAAK,CAAC;mBAC/B,EAAE,iBAAiB,MAAM,CAAC;qBACxB,EAAE,iBAAiB,WAAW,CAAC;eACrC,EAAE,iBAAiB,IAAI,CAAC;gBACvB,EAAE,iBAAiB,QAAQ,CAAC;;;;;;;;;;AAU5C,EAAE,MAAM,eAAe,EAAE,UAAU,CAAC;qBACf,EAAE,MAAM,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC;;0EAErC,CAAC,GAAG,GAAG;;AAEjF,EAAE,MAAM,eAAe,EAAE,QAAQ,SAAS,IAAI,CAAC;;AAE/C,EAAE,MAAM,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAY,IAAc,GAAG,IAAI,EAAE,EAAE,EAAE,MAAM,KAAK,CAAC,sDAAsD,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;AAE9K,EAAE,MAAM,eAAe,EAAE,QAAQ,SAAS,IAAI,CAAC;;AAE/C,EAAE,MAAM,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAY,IAAc,GAAG,IAAI,EAAE,EAAE,EAAE,MAAM,IAAI,CAAC,gDAAgD,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;;;;;;;;;;;;;;;;;;;;;;uEAuBhG,CAAC;QAEpE,QAAQ,GAAG,CAAC,8CAA8C;QAE1D,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,sBAAsB,YAAY,YAAY;QACtF,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,QAAQ,IAAI,UAAU;QAClE,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,WAAW,EAAE;QACrD,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,YAAY,CAAC,UAAU,CAAC;QAChE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAEpF,wDAAwD;QACxD,MAAM,kBAAkB;YACtB;YACA;SACD;QAED,oDAAoD;QACpD,IAAI,MAAM,WAAW,EAAE;YACrB,QAAQ,GAAG,CAAC;YAEZ,0DAA0D;YAC1D,MAAM,YAAY,MAAM,WAAW,CAAC,KAAK,CAAC;YAC1C,IAAI,WAAW;gBACb,MAAM,GAAG,UAAU,WAAW,GAAG;gBAEjC,gBAAgB,IAAI,CAAC;oBACnB,YAAY;wBACV,MAAM;wBACN,UAAU;oBACZ;gBACF;gBAEA,mDAAmD;gBACnD,MAAM,aAAa,CAAC,6MAA6M,CAAC;gBAClO,eAAe,CAAC,EAAE,GAAG,cAAc;YACrC,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAE3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QAEtC,0CAA0C;QAC1C,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,EAAE;gBACnB,MAAM,YAAY,KAAK,UAAU,CAAC,IAAI;gBACtC,MAAM,WAAW,KAAK,UAAU,CAAC,QAAQ;gBACzC,WAAW,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,WAAW;gBACjD,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,sDAAsD;YACtD,MAAM,eAAe,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,UAAU;YACV,aAAa;YACb,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACjH;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAAE,OAAO;QAAe;QAC5D,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QAEtC,OAAO;YACL,SAAS;YACT,OAAO;YACP,UAAU,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG,MAAM;YAC7C,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO;YACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF;AAKO,SAAS;IACd,OAAO;QACL,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP,mBAAmB;YACnB,kBAAkB;YAClB,MAAM;QACR;QACA,aAAa;IACf;AACF", "debugId": null}}, {"offset": {"line": 2178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/content-generator.ts"], "sourcesContent": ["/**\n * Revo 1.0 Content Generator\n * Handles content generation for the stable foundation model\n */\n\nimport type {\n  IContentGenerator,\n  ContentGenerationRequest,\n  GenerationResponse\n} from '../../types/model-types';\nimport type { GeneratedPost } from '@/lib/types';\nimport { generateRevo10Content } from '@/ai/revo-1.0-service';\n\nexport class Revo10ContentGenerator implements IContentGenerator {\n  private readonly modelId = 'revo-1.0';\n\n  /**\n   * Generate content using Revo 1.0 specifications\n   */\n  async generateContent(request: ContentGenerationRequest): Promise<GenerationResponse<GeneratedPost>> {\n    const startTime = Date.now();\n\n    try {\n      console.log('📝 Revo 1.0: Starting content generation...');\n      console.log('- Platform:', request.platform);\n      console.log('- Business:', request.profile.businessName);\n      console.log('- AI Engine: Gemini 2.5 Flash Image Preview (Enhanced)');\n\n      // Validate request\n      if (!this.validateRequest(request)) {\n        throw new Error('Invalid content generation request for Revo 1.0');\n      }\n\n      // Prepare generation parameters for Revo 1.0\n      const generationParams = this.prepareGenerationParams(request);\n\n      // Generate content using Revo 1.0 service with Gemini 2.5 Flash Image Preview\n      const postDetails = await generateRevo10Content({\n        businessType: generationParams.businessType,\n        businessName: generationParams.businessName || 'Business',\n        location: generationParams.location || 'Location',\n        platform: generationParams.variants[0]?.platform || 'instagram',\n        writingTone: generationParams.writingTone || 'professional',\n        contentThemes: generationParams.contentThemes || [],\n        targetAudience: generationParams.targetAudience || 'General',\n        services: generationParams.services || '',\n        keyFeatures: generationParams.keyFeatures || '',\n        competitiveAdvantages: generationParams.competitiveAdvantages || '',\n        dayOfWeek: generationParams.dayOfWeek || 'Monday',\n        currentDate: generationParams.currentDate || new Date().toLocaleDateString(),\n        primaryColor: generationParams.primaryColor,\n        visualStyle: generationParams.visualStyle\n      });\n\n      // Generate image using the catchy words and brand profile data\n      console.log('🎨 Revo 1.0: Generating branded image for content...');\n      console.log('🏢 Brand:', generationParams.businessName);\n      console.log('🏭 Business Type:', generationParams.businessType);\n      console.log('🎨 Colors:', generationParams.primaryColor, generationParams.accentColor, generationParams.backgroundColor);\n      console.log('📍 Location:', generationParams.location);\n      console.log('🎭 Visual Style:', generationParams.visualStyle);\n      console.log('✍️ Writing Tone:', generationParams.writingTone);\n      console.log('🎯 Target Audience:', generationParams.targetAudience);\n      console.log('🔧 Services:', generationParams.services ? 'Available' : 'None');\n      console.log('⭐ Key Features:', generationParams.keyFeatures ? 'Available' : 'None');\n      console.log('🚀 Competitive Advantages:', generationParams.competitiveAdvantages ? 'Available' : 'None');\n      console.log('🖼️ Logo:', generationParams.logoDataUrl ? 'Available' : 'None');\n\n      const { generateRevo10Image } = await import('@/ai/revo-1.0-service');\n      // Prepare structured text for image\n      const imageTextComponents = [];\n      if (postDetails.catchyWords) imageTextComponents.push(postDetails.catchyWords);\n      if (postDetails.subheadline) imageTextComponents.push(postDetails.subheadline);\n      if (postDetails.callToAction) imageTextComponents.push(postDetails.callToAction);\n\n      const structuredImageText = imageTextComponents.join(' | ');\n      console.log('🎨 Image text structure:', structuredImageText);\n\n      // Get real-time context for enhanced design\n      const realTimeContext = (postDetails as any).realTimeContext || null;\n\n      const imageResult = await generateRevo10Image({\n        businessType: generationParams.businessType,\n        businessName: generationParams.businessName || 'Business',\n        platform: generationParams.variants[0]?.platform || 'instagram',\n        visualStyle: generationParams.visualStyle || 'modern',\n        primaryColor: generationParams.primaryColor || '#3B82F6',\n        accentColor: generationParams.accentColor || '#1E40AF',\n        backgroundColor: generationParams.backgroundColor || '#FFFFFF',\n        imageText: structuredImageText,\n        designDescription: `Professional ${generationParams.businessType} content with structured headline, subheadline, and CTA for ${generationParams.variants[0]?.platform || 'instagram'}`,\n        logoDataUrl: generationParams.logoDataUrl,\n        location: generationParams.location,\n        headline: postDetails.catchyWords,\n        subheadline: postDetails.subheadline,\n        callToAction: postDetails.callToAction,\n        realTimeContext: realTimeContext\n      });\n\n      // Update variants with the generated image\n      postDetails.variants = postDetails.variants.map(variant => ({\n        ...variant,\n        imageUrl: imageResult.imageUrl\n      }));\n\n      // Create the generated post\n      const generatedPost: GeneratedPost = {\n        id: new Date().toISOString(),\n        date: new Date().toISOString(),\n        content: postDetails.content,\n        hashtags: postDetails.hashtags,\n        status: 'generated',\n        variants: postDetails.variants,\n        catchyWords: postDetails.catchyWords,\n        subheadline: postDetails.subheadline,\n        callToAction: postDetails.callToAction,\n        // Revo 1.0 doesn't include advanced features\n        contentVariants: undefined,\n        hashtagAnalysis: undefined,\n        marketIntelligence: undefined,\n        localContext: undefined,\n        metadata: {\n          modelId: this.modelId,\n          modelVersion: '1.0.0',\n          generationType: 'standard',\n          processingTime: Date.now() - startTime,\n          qualityLevel: 'standard'\n        }\n      };\n\n      const processingTime = Date.now() - startTime;\n      const qualityScore = this.calculateQualityScore(generatedPost);\n\n      console.log(`✅ Revo 1.0: Content generated successfully in ${processingTime}ms`);\n      console.log(`⭐ Quality Score: ${qualityScore}/10`);\n\n      return {\n        success: true,\n        data: generatedPost,\n        metadata: {\n          modelId: this.modelId,\n          processingTime,\n          qualityScore,\n          creditsUsed: 1.5, // Revo 1.0 now uses 1.5 credits for enhanced capabilities\n          enhancementsApplied: ['enhanced-optimization', 'platform-formatting', 'gemini-2.5-flash-image']\n        }\n      };\n\n    } catch (error) {\n      const processingTime = Date.now() - startTime;\n      console.error('❌ Revo 1.0: Content generation failed:', error);\n\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n        metadata: {\n          modelId: this.modelId,\n          processingTime,\n          qualityScore: 0,\n          creditsUsed: 0,\n          enhancementsApplied: []\n        }\n      };\n    }\n  }\n\n  /**\n   * Validate content generation request for Revo 1.0\n   */\n  private validateRequest(request: ContentGenerationRequest): boolean {\n    // Check required fields\n    if (!request.profile || !request.platform) {\n      return false;\n    }\n\n    // Check if profile has minimum required information\n    if (!request.profile.businessType || !request.profile.businessName) {\n      return false;\n    }\n\n    // Revo 1.0 doesn't support artifacts\n    if (request.artifactIds && request.artifactIds.length > 0) {\n      console.warn('⚠️ Revo 1.0: Artifacts not supported, ignoring artifact IDs');\n    }\n\n    return true;\n  }\n\n  /**\n   * Prepare generation parameters optimized for Revo 1.0\n   */\n  private prepareGenerationParams(request: ContentGenerationRequest) {\n    const { profile, platform, brandConsistency } = request;\n    const today = new Date();\n\n    // Convert arrays to strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n    return {\n      businessName: profile.businessName || profile.name || 'Business', // Add business name\n      businessType: profile.businessType,\n      location: profile.location,\n      writingTone: profile.writingTone,\n      contentThemes: profile.contentThemes,\n      visualStyle: profile.visualStyle,\n      logoDataUrl: profile.logoDataUrl,\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\n      primaryColor: profile.primaryColor,\n      accentColor: profile.accentColor,\n      backgroundColor: profile.backgroundColor,\n      dayOfWeek: today.toLocaleDateString('en-US', { weekday: 'long' }),\n      currentDate: today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\n      variants: [{\n        platform: platform,\n        aspectRatio: '1:1', // Revo 1.0 only supports 1:1\n      }],\n      services: servicesString,\n      targetAudience: profile.targetAudience,\n      keyFeatures: keyFeaturesString,\n      competitiveAdvantages: competitiveAdvantagesString,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      // Revo 1.0 specific constraints (updated to match config)\n      modelConstraints: {\n        maxComplexity: 'enhanced', // Upgraded from basic\n        enhancedFeatures: true,    // Now enabled\n        realTimeContext: true,     // Now enabled\n        trendingTopics: true,      // Now enabled\n        artifactSupport: false     // Keep disabled for Revo 1.0\n      }\n    };\n  }\n\n  /**\n   * Calculate quality score for generated content\n   */\n  private calculateQualityScore(post: GeneratedPost): number {\n    let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)\n\n    // Content quality checks\n    if (post.content && post.content.length > 50) score += 1;\n    if (post.content && post.content.length > 100) score += 0.5;\n\n    // Hashtag quality\n    if (post.hashtags && post.hashtags.length >= 5) score += 1;\n    if (post.hashtags && post.hashtags.length >= 10) score += 0.5;\n\n    // Catchy words presence\n    if (post.catchyWords && post.catchyWords.trim().length > 0) score += 1;\n\n    // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)\n    if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {\n      score += 1.5; // Increased from 1 for better image quality\n    }\n\n    // Cap at 10\n    return Math.min(score, 10);\n  }\n\n  /**\n   * Health check for content generator\n   */\n  async healthCheck(): Promise<boolean> {\n    try {\n      // Check if we can access the AI service\n      const hasApiKey = !!(\n        process.env.GEMINI_API_KEY ||\n        process.env.GOOGLE_API_KEY ||\n        process.env.GOOGLE_GENAI_API_KEY\n      );\n\n      return hasApiKey;\n    } catch (error) {\n      console.error('❌ Revo 1.0 Content Generator health check failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get generator-specific information\n   */\n  getGeneratorInfo() {\n    return {\n      modelId: this.modelId,\n      type: 'content',\n      capabilities: [\n        'Enhanced content generation with Gemini 2.5 Flash Image Preview',\n        'Platform-specific formatting',\n        'Hashtag generation',\n        'Catchy words creation',\n        'Brand consistency (enhanced)',\n        'Perfect text rendering',\n        'High-resolution image support'\n      ],\n      limitations: [\n        'No real-time context',\n        'No trending topics',\n        'No artifact support',\n        'Enhanced quality optimization',\n        'Limited customization'\n      ],\n      averageProcessingTime: '20-30 seconds (enhanced for quality)',\n      qualityRange: '8-9/10 (upgraded from 6-8/10)',\n      costPerGeneration: 1.5 // Upgraded from 1 for enhanced capabilities\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAQD;;AAEO,MAAM;IACM,UAAU,WAAW;IAEtC;;GAEC,GACD,MAAM,gBAAgB,OAAiC,EAA8C;QACnG,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,eAAe,QAAQ,QAAQ;YAC3C,QAAQ,GAAG,CAAC,eAAe,QAAQ,OAAO,CAAC,YAAY;YACvD,QAAQ,GAAG,CAAC;YAEZ,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,6CAA6C;YAC7C,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC;YAEtD,8EAA8E;YAC9E,MAAM,cAAc,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE;gBAC9C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,eAAe,iBAAiB,aAAa,IAAI,EAAE;gBACnD,gBAAgB,iBAAiB,cAAc,IAAI;gBACnD,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,uBAAuB,iBAAiB,qBAAqB,IAAI;gBACjE,WAAW,iBAAiB,SAAS,IAAI;gBACzC,aAAa,iBAAiB,WAAW,IAAI,IAAI,OAAO,kBAAkB;gBAC1E,cAAc,iBAAiB,YAAY;gBAC3C,aAAa,iBAAiB,WAAW;YAC3C;YAEA,+DAA+D;YAC/D,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,aAAa,iBAAiB,YAAY;YACtD,QAAQ,GAAG,CAAC,qBAAqB,iBAAiB,YAAY;YAC9D,QAAQ,GAAG,CAAC,cAAc,iBAAiB,YAAY,EAAE,iBAAiB,WAAW,EAAE,iBAAiB,eAAe;YACvH,QAAQ,GAAG,CAAC,gBAAgB,iBAAiB,QAAQ;YACrD,QAAQ,GAAG,CAAC,oBAAoB,iBAAiB,WAAW;YAC5D,QAAQ,GAAG,CAAC,oBAAoB,iBAAiB,WAAW;YAC5D,QAAQ,GAAG,CAAC,uBAAuB,iBAAiB,cAAc;YAClE,QAAQ,GAAG,CAAC,gBAAgB,iBAAiB,QAAQ,GAAG,cAAc;YACtE,QAAQ,GAAG,CAAC,mBAAmB,iBAAiB,WAAW,GAAG,cAAc;YAC5E,QAAQ,GAAG,CAAC,8BAA8B,iBAAiB,qBAAqB,GAAG,cAAc;YACjG,QAAQ,GAAG,CAAC,aAAa,iBAAiB,WAAW,GAAG,cAAc;YAEtE,MAAM,EAAE,mBAAmB,EAAE,GAAG;YAChC,oCAAoC;YACpC,MAAM,sBAAsB,EAAE;YAC9B,IAAI,YAAY,WAAW,EAAE,oBAAoB,IAAI,CAAC,YAAY,WAAW;YAC7E,IAAI,YAAY,WAAW,EAAE,oBAAoB,IAAI,CAAC,YAAY,WAAW;YAC7E,IAAI,YAAY,YAAY,EAAE,oBAAoB,IAAI,CAAC,YAAY,YAAY;YAE/E,MAAM,sBAAsB,oBAAoB,IAAI,CAAC;YACrD,QAAQ,GAAG,CAAC,4BAA4B;YAExC,4CAA4C;YAC5C,MAAM,kBAAkB,AAAC,YAAoB,eAAe,IAAI;YAEhE,MAAM,cAAc,MAAM,oBAAoB;gBAC5C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,iBAAiB,iBAAiB,eAAe,IAAI;gBACrD,WAAW;gBACX,mBAAmB,CAAC,aAAa,EAAE,iBAAiB,YAAY,CAAC,4DAA4D,EAAE,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY,aAAa;gBACtL,aAAa,iBAAiB,WAAW;gBACzC,UAAU,iBAAiB,QAAQ;gBACnC,UAAU,YAAY,WAAW;gBACjC,aAAa,YAAY,WAAW;gBACpC,cAAc,YAAY,YAAY;gBACtC,iBAAiB;YACnB;YAEA,2CAA2C;YAC3C,YAAY,QAAQ,GAAG,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC1D,GAAG,OAAO;oBACV,UAAU,YAAY,QAAQ;gBAChC,CAAC;YAED,4BAA4B;YAC5B,MAAM,gBAA+B;gBACnC,IAAI,IAAI,OAAO,WAAW;gBAC1B,MAAM,IAAI,OAAO,WAAW;gBAC5B,SAAS,YAAY,OAAO;gBAC5B,UAAU,YAAY,QAAQ;gBAC9B,QAAQ;gBACR,UAAU,YAAY,QAAQ;gBAC9B,aAAa,YAAY,WAAW;gBACpC,aAAa,YAAY,WAAW;gBACpC,cAAc,YAAY,YAAY;gBACtC,6CAA6C;gBAC7C,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,cAAc;gBACd,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB,cAAc;oBACd,gBAAgB;oBAChB,gBAAgB,KAAK,GAAG,KAAK;oBAC7B,cAAc;gBAChB;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAEhD,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,eAAe,EAAE,CAAC;YAC/E,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,aAAa,GAAG,CAAC;YAEjD,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA;oBACA,aAAa;oBACb,qBAAqB;wBAAC;wBAAyB;wBAAuB;qBAAyB;gBACjG;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,QAAQ,KAAK,CAAC,0CAA0C;YAExD,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA,cAAc;oBACd,aAAa;oBACb,qBAAqB,EAAE;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAiC,EAAW;QAClE,wBAAwB;QACxB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACzC,OAAO;QACT;QAEA,oDAAoD;QACpD,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,EAAE;YAClE,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;YACzD,QAAQ,IAAI,CAAC;QACf;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAAwB,OAAiC,EAAE;QACjE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG;QAChD,MAAM,QAAQ,IAAI;QAElB,8CAA8C;QAC9C,MAAM,oBAAoB,MAAM,OAAO,CAAC,QAAQ,WAAW,IACvD,QAAQ,WAAW,CAAC,IAAI,CAAC,QACzB,QAAQ,WAAW,IAAI;QAE3B,MAAM,8BAA8B,MAAM,OAAO,CAAC,QAAQ,qBAAqB,IAC3E,QAAQ,qBAAqB,CAAC,IAAI,CAAC,QACnC,QAAQ,qBAAqB,IAAI;QAErC,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,QAAQ,IACjD,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UACrB,OAAO,YAAY,YAAY,QAAQ,IAAI,GACvC,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,IAAI,IAAI,GAC/C,SACJ,IAAI,CAAC,QACL,QAAQ,QAAQ,IAAI;QAExB,OAAO;YACL,cAAc,QAAQ,YAAY,IAAI,QAAQ,IAAI,IAAI;YACtD,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,eAAe,QAAQ,aAAa;YACpC,aAAa,QAAQ,WAAW;YAChC,aAAa,QAAQ,WAAW;YAChC,gBAAgB,kBAAkB,oBAAqB,QAAQ,cAAc,IAAI,EAAE,GAAI,EAAE;YACzF,cAAc,QAAQ,YAAY;YAClC,aAAa,QAAQ,WAAW;YAChC,iBAAiB,QAAQ,eAAe;YACxC,WAAW,MAAM,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YAC/D,aAAa,MAAM,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,OAAO;gBAAQ,KAAK;YAAU;YAChG,UAAU;gBAAC;oBACT,UAAU;oBACV,aAAa;gBACf;aAAE;YACF,UAAU;YACV,gBAAgB,QAAQ,cAAc;YACtC,aAAa;YACb,uBAAuB;YACvB,kBAAkB,oBAAoB;gBAAE,mBAAmB;gBAAO,mBAAmB;YAAK;YAC1F,0DAA0D;YAC1D,kBAAkB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB,MAAU,6BAA6B;YAC1D;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAmB,EAAU;QACzD,IAAI,QAAQ,GAAG,kEAAkE;QAEjF,yBAAyB;QACzB,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,SAAS;QACvD,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,SAAS;QAExD,kBAAkB;QAClB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,GAAG,SAAS;QACzD,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,IAAI,SAAS;QAE1D,wBAAwB;QACxB,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG,SAAS;QAErE,yEAAyE;QACzE,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1E,SAAS,KAAK,4CAA4C;QAC5D;QAEA,YAAY;QACZ,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,wCAAwC;YACxC,MAAM,YAAY,CAAC,CAAC,CAClB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qDAAqD;YACnE,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,uBAAuB;YACvB,cAAc;YACd,mBAAmB,IAAI,4CAA4C;QACrE;IACF;AACF", "debugId": null}}, {"offset": {"line": 2459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/design-generator.ts"], "sourcesContent": ["/**\n * Revo 1.0 Design Generator\n * Handles design generation for the stable foundation model\n */\n\nimport type {\n  IDesignGenerator,\n  DesignGenerationRequest,\n  GenerationResponse\n} from '../../types/model-types';\nimport type { PostVariant } from '@/lib/types';\n\nexport class Revo10DesignGenerator implements IDesignGenerator {\n  private readonly modelId = 'revo-1.0';\n\n  /**\n   * Generate design using Revo 1.0 specifications\n   */\n  async generateDesign(request: DesignGenerationRequest): Promise<GenerationResponse<PostVariant>> {\n    const startTime = Date.now();\n\n    try {\n      console.log('🎨 Revo 1.0: Starting design generation...');\n      console.log('- Business Type:', request.businessType);\n      console.log('- Platform:', request.platform);\n      console.log('- Visual Style:', request.visualStyle);\n      console.log('- AI Engine: Gemini 2.5 Flash Image Preview (Enhanced)');\n\n      // Validate request\n      if (!this.validateRequest(request)) {\n        throw new Error('Invalid design generation request for Revo 1.0');\n      }\n\n      // Generate design using basic Gemini 2.0 approach\n      const designResult = await this.generateBasicDesign(request);\n\n      const processingTime = Date.now() - startTime;\n      const qualityScore = this.calculateQualityScore(designResult);\n\n      console.log(`✅ Revo 1.0: Design generated successfully in ${processingTime}ms`);\n      console.log(`⭐ Quality Score: ${qualityScore}/10`);\n\n      return {\n        success: true,\n        data: designResult,\n        metadata: {\n          modelId: this.modelId,\n          processingTime,\n          qualityScore,\n          creditsUsed: 1.5, // Revo 1.0 now uses 1.5 credits for enhanced capabilities\n          enhancementsApplied: ['enhanced-styling', 'brand-colors', 'platform-optimization', 'gemini-2.5-flash-image']\n        }\n      };\n\n    } catch (error) {\n      const processingTime = Date.now() - startTime;\n      console.error('❌ Revo 1.0: Design generation failed:', error);\n\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n        metadata: {\n          modelId: this.modelId,\n          processingTime,\n          qualityScore: 0,\n          creditsUsed: 0,\n          enhancementsApplied: []\n        }\n      };\n    }\n  }\n\n  /**\n   * Generate basic design using Gemini 2.0\n   */\n  private async generateBasicDesign(request: DesignGenerationRequest): Promise<PostVariant> {\n    try {\n      // Import the basic generation flow\n      const { generateRevo10Design } = await import('@/ai/revo-1.0-service');\n\n      // Prepare image text\n      let imageText: string;\n      if (typeof request.imageText === 'string') {\n        imageText = request.imageText;\n      } else {\n        // Combine components for Revo 1.0 (simpler approach)\n        imageText = request.imageText.catchyWords;\n        if (request.imageText.subheadline) {\n          imageText += '\\n' + request.imageText.subheadline;\n        }\n      }\n\n      // Create a simplified generation request\n      const generationParams = {\n        businessType: request.businessType,\n        location: request.brandProfile.location || '',\n        writingTone: request.brandProfile.writingTone || 'professional',\n        contentThemes: request.brandProfile.contentThemes || '',\n        visualStyle: request.visualStyle,\n        logoDataUrl: request.brandProfile.logoDataUrl,\n        designExamples: request.brandConsistency?.strictConsistency ?\n          (request.brandProfile.designExamples || []) : [],\n        primaryColor: request.brandProfile.primaryColor,\n        accentColor: request.brandProfile.accentColor,\n        backgroundColor: request.brandProfile.backgroundColor,\n        dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\n        currentDate: new Date().toLocaleDateString('en-US', {\n          year: 'numeric', month: 'long', day: 'numeric'\n        }),\n        variants: [{\n          platform: request.platform,\n          aspectRatio: '1:1', // Revo 1.0 only supports 1:1\n        }],\n        services: '',\n        targetAudience: request.brandProfile.targetAudience || '',\n        keyFeatures: '',\n        competitiveAdvantages: '',\n        brandConsistency: request.brandConsistency || {\n          strictConsistency: false,\n          followBrandColors: true\n        }\n      };\n\n      // First generate design description\n      const designResult = await generateRevo10Design({\n        businessType: generationParams.businessType,\n        businessName: generationParams.businessName || 'Business',\n        platform: generationParams.variants[0]?.platform || 'instagram',\n        visualStyle: generationParams.visualStyle || 'modern',\n        primaryColor: generationParams.primaryColor || '#3B82F6',\n        accentColor: generationParams.accentColor || '#1E40AF',\n        backgroundColor: generationParams.backgroundColor || '#FFFFFF',\n        imageText: imageText || 'Your Text Here'\n      });\n\n      // Then generate the actual image using the design description\n      const { generateRevo10Image } = await import('@/ai/revo-1.0-service');\n      const imageResult = await generateRevo10Image({\n        businessType: generationParams.businessType,\n        businessName: generationParams.businessName || 'Business',\n        platform: generationParams.variants[0]?.platform || 'instagram',\n        visualStyle: generationParams.visualStyle || 'modern',\n        primaryColor: generationParams.primaryColor || '#3B82F6',\n        imageText: imageText || 'Your Text Here',\n        designDescription: designResult.design\n      });\n\n      // Return the complete result with actual image URL\n      return {\n        platform: request.platform,\n        imageUrl: imageResult.imageUrl,\n        caption: imageText,\n        hashtags: [],\n        design: designResult.design,\n        aspectRatio: imageResult.aspectRatio,\n        resolution: imageResult.resolution,\n        quality: imageResult.quality\n      };\n\n    } catch (error) {\n      console.error('❌ Revo 1.0: Basic design generation failed:', error);\n\n      // Return a fallback variant\n      return {\n        platform: request.platform,\n        imageUrl: '', // Empty URL indicates generation failure\n        caption: typeof request.imageText === 'string' ?\n          request.imageText : request.imageText.catchyWords,\n        hashtags: []\n      };\n    }\n  }\n\n  /**\n   * Validate design generation request for Revo 1.0\n   */\n  private validateRequest(request: DesignGenerationRequest): boolean {\n    // Check required fields\n    if (!request.businessType || !request.platform || !request.brandProfile) {\n      return false;\n    }\n\n    // Check image text\n    if (!request.imageText) {\n      return false;\n    }\n\n    // Revo 1.0 only supports 1:1 aspect ratio\n    // We don't enforce this here as the generator will handle it\n\n    // Warn about unsupported features\n    if (request.artifactInstructions) {\n      console.warn('⚠️ Revo 1.0: Artifact instructions not supported, ignoring');\n    }\n\n    return true;\n  }\n\n  /**\n   * Calculate quality score for generated design\n   */\n  private calculateQualityScore(variant: PostVariant): number {\n    let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)\n\n    // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)\n    if (variant.imageUrl && variant.imageUrl.length > 0) {\n      score += 2.5; // Increased from 2 for better image quality\n    }\n\n    // Caption quality\n    if (variant.caption && variant.caption.length > 10) {\n      score += 1;\n    }\n\n    // Hashtags presence\n    if (variant.hashtags && variant.hashtags.length > 0) {\n      score += 1;\n    }\n\n    // Platform optimization (basic check)\n    if (variant.platform) {\n      score += 0.5;\n    }\n\n    // Revo 1.0 now has higher quality ceiling due to Gemini 2.5 Flash Image Preview\n    return Math.min(score, 9.0);\n  }\n\n  /**\n   * Health check for design generator\n   */\n  async healthCheck(): Promise<boolean> {\n    try {\n      // Check if we can access the AI service\n      const hasApiKey = !!(\n        process.env.GEMINI_API_KEY ||\n        process.env.GOOGLE_API_KEY ||\n        process.env.GOOGLE_GENAI_API_KEY\n      );\n\n      return hasApiKey;\n    } catch (error) {\n      console.error('❌ Revo 1.0 Design Generator health check failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get generator-specific information\n   */\n  getGeneratorInfo() {\n    return {\n      modelId: this.modelId,\n      type: 'design',\n      capabilities: [\n        'Enhanced image generation with Gemini 2.5 Flash Image Preview',\n        '1:1 aspect ratio only',\n        'Brand color integration',\n        'Logo placement',\n        'Platform optimization',\n        'Text overlay (enhanced)',\n        'Perfect text rendering',\n        'High-resolution 2048x2048 output'\n      ],\n      limitations: [\n        'Single aspect ratio (1:1)',\n        'No artifact support',\n        'Enhanced styling options',\n        'Limited customization',\n        'High-resolution support'\n      ],\n      supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\n      supportedAspectRatios: ['1:1'],\n      averageProcessingTime: '25-35 seconds (enhanced for quality)',\n      qualityRange: '7-9/10 (upgraded from 5-7.5/10)',\n      costPerGeneration: 1.5, // Upgraded from 1 for enhanced capabilities\n      resolution: '2048x2048 (upgraded from 1024x1024)'\n    };\n  }\n\n  /**\n   * Get supported features for this design generator\n   */\n  getSupportedFeatures() {\n    return {\n      aspectRatios: ['1:1'],\n      textOverlay: 'enhanced', // Upgraded from basic\n      brandIntegration: 'standard',\n      logoPlacement: true,\n      colorCustomization: true,\n      templateSupport: false,\n      artifactSupport: false,\n      advancedStyling: true, // Upgraded from false\n      multipleVariants: false,\n      highResolution: true, // NEW: 2048x2048 support\n      perfectTextRendering: true // NEW: Gemini 2.5 Flash Image Preview feature\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AASM,MAAM;IACM,UAAU,WAAW;IAEtC;;GAEC,GACD,MAAM,eAAe,OAAgC,EAA4C;QAC/F,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,YAAY;YACpD,QAAQ,GAAG,CAAC,eAAe,QAAQ,QAAQ;YAC3C,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,WAAW;YAClD,QAAQ,GAAG,CAAC;YAEZ,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,kDAAkD;YAClD,MAAM,eAAe,MAAM,IAAI,CAAC,mBAAmB,CAAC;YAEpD,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAEhD,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,eAAe,EAAE,CAAC;YAC9E,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,aAAa,GAAG,CAAC;YAEjD,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA;oBACA,aAAa;oBACb,qBAAqB;wBAAC;wBAAoB;wBAAgB;wBAAyB;qBAAyB;gBAC9G;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,QAAQ,KAAK,CAAC,yCAAyC;YAEvD,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA,cAAc;oBACd,aAAa;oBACb,qBAAqB,EAAE;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,OAAgC,EAAwB;QACxF,IAAI;YACF,mCAAmC;YACnC,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,qBAAqB;YACrB,IAAI;YACJ,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;gBACzC,YAAY,QAAQ,SAAS;YAC/B,OAAO;gBACL,qDAAqD;gBACrD,YAAY,QAAQ,SAAS,CAAC,WAAW;gBACzC,IAAI,QAAQ,SAAS,CAAC,WAAW,EAAE;oBACjC,aAAa,OAAO,QAAQ,SAAS,CAAC,WAAW;gBACnD;YACF;YAEA,yCAAyC;YACzC,MAAM,mBAAmB;gBACvB,cAAc,QAAQ,YAAY;gBAClC,UAAU,QAAQ,YAAY,CAAC,QAAQ,IAAI;gBAC3C,aAAa,QAAQ,YAAY,CAAC,WAAW,IAAI;gBACjD,eAAe,QAAQ,YAAY,CAAC,aAAa,IAAI;gBACrD,aAAa,QAAQ,WAAW;gBAChC,aAAa,QAAQ,YAAY,CAAC,WAAW;gBAC7C,gBAAgB,QAAQ,gBAAgB,EAAE,oBACvC,QAAQ,YAAY,CAAC,cAAc,IAAI,EAAE,GAAI,EAAE;gBAClD,cAAc,QAAQ,YAAY,CAAC,YAAY;gBAC/C,aAAa,QAAQ,YAAY,CAAC,WAAW;gBAC7C,iBAAiB,QAAQ,YAAY,CAAC,eAAe;gBACrD,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAAE,SAAS;gBAAO;gBACpE,aAAa,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAClD,MAAM;oBAAW,OAAO;oBAAQ,KAAK;gBACvC;gBACA,UAAU;oBAAC;wBACT,UAAU,QAAQ,QAAQ;wBAC1B,aAAa;oBACf;iBAAE;gBACF,UAAU;gBACV,gBAAgB,QAAQ,YAAY,CAAC,cAAc,IAAI;gBACvD,aAAa;gBACb,uBAAuB;gBACvB,kBAAkB,QAAQ,gBAAgB,IAAI;oBAC5C,mBAAmB;oBACnB,mBAAmB;gBACrB;YACF;YAEA,oCAAoC;YACpC,MAAM,eAAe,MAAM,qBAAqB;gBAC9C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,iBAAiB,iBAAiB,eAAe,IAAI;gBACrD,WAAW,aAAa;YAC1B;YAEA,8DAA8D;YAC9D,MAAM,EAAE,mBAAmB,EAAE,GAAG;YAChC,MAAM,cAAc,MAAM,oBAAoB;gBAC5C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,WAAW,aAAa;gBACxB,mBAAmB,aAAa,MAAM;YACxC;YAEA,mDAAmD;YACnD,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,YAAY,QAAQ;gBAC9B,SAAS;gBACT,UAAU,EAAE;gBACZ,QAAQ,aAAa,MAAM;gBAC3B,aAAa,YAAY,WAAW;gBACpC,YAAY,YAAY,UAAU;gBAClC,SAAS,YAAY,OAAO;YAC9B;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAE7D,4BAA4B;YAC5B,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU;gBACV,SAAS,OAAO,QAAQ,SAAS,KAAK,WACpC,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,WAAW;gBACnD,UAAU,EAAE;YACd;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAgC,EAAW;QACjE,wBAAwB;QACxB,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,YAAY,EAAE;YACvE,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO;QACT;QAEA,0CAA0C;QAC1C,6DAA6D;QAE7D,kCAAkC;QAClC,IAAI,QAAQ,oBAAoB,EAAE;YAChC,QAAQ,IAAI,CAAC;QACf;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,OAAoB,EAAU;QAC1D,IAAI,QAAQ,GAAG,kEAAkE;QAEjF,yEAAyE;QACzE,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnD,SAAS,KAAK,4CAA4C;QAC5D;QAEA,kBAAkB;QAClB,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,IAAI;YAClD,SAAS;QACX;QAEA,oBAAoB;QACpB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnD,SAAS;QACX;QAEA,sCAAsC;QACtC,IAAI,QAAQ,QAAQ,EAAE;YACpB,SAAS;QACX;QAEA,gFAAgF;QAChF,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,wCAAwC;YACxC,MAAM,YAAY,CAAC,CAAC,CAClB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oDAAoD;YAClE,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAoB;gBAAC;gBAAa;gBAAY;gBAAW;aAAW;YACpE,uBAAuB;gBAAC;aAAM;YAC9B,uBAAuB;YACvB,cAAc;YACd,mBAAmB;YACnB,YAAY;QACd;IACF;IAEA;;GAEC,GACD,uBAAuB;QACrB,OAAO;YACL,cAAc;gBAAC;aAAM;YACrB,aAAa;YACb,kBAAkB;YAClB,eAAe;YACf,oBAAoB;YACpB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB;YAClB,gBAAgB;YAChB,sBAAsB,KAAK,8CAA8C;QAC3E;IACF;AACF", "debugId": null}}, {"offset": {"line": 2733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/index.ts"], "sourcesContent": ["/**\n * Revo 1.0 Model Implementation\n * Standard Model - Stable Foundation\n */\n\nimport type {\n  IModelImplementation,\n  IContentGenerator,\n  IDesignGenerator,\n  ContentGenerationRequest,\n  DesignGenerationRequest,\n  GenerationResponse\n} from '../../types/model-types';\nimport { getModelConfig } from '../../config/model-configs';\nimport { Revo10ContentGenerator } from './content-generator';\nimport { Revo10DesignGenerator } from './design-generator';\n\nexport class Revo10Implementation implements IModelImplementation {\n  public readonly model;\n  public readonly contentGenerator: IContentGenerator;\n  public readonly designGenerator: IDesignGenerator;\n\n  constructor() {\n    try {\n      console.log('🔧 Revo 1.0: Getting model config...');\n      this.model = getModelConfig('revo-1.0');\n      console.log('✅ Revo 1.0: Model config loaded:', this.model.name);\n      \n      console.log('🔧 Revo 1.0: Creating content generator...');\n      this.contentGenerator = new Revo10ContentGenerator();\n      console.log('✅ Revo 1.0: Content generator created');\n      \n      console.log('🔧 Revo 1.0: Creating design generator...');\n      this.designGenerator = new Revo10DesignGenerator();\n      console.log('✅ Revo 1.0: Design generator created');\n      \n      console.log('✅ Revo 1.0: Implementation fully initialized');\n    } catch (error) {\n      console.error('❌ Revo 1.0: Failed to initialize implementation:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Check if the model is available and ready to use\n   */\n  async isAvailable(): Promise<boolean> {\n    try {\n      // Check if the underlying AI service (Gemini 2.5 Flash Image Preview) is available\n      // For now, we'll assume it's available if we have the API key\n      const hasApiKey = !!(\n        process.env.GEMINI_API_KEY ||\n        process.env.GOOGLE_API_KEY ||\n        process.env.GOOGLE_GENAI_API_KEY\n      );\n\n      return hasApiKey;\n    } catch (error) {\n      console.error('❌ Revo 1.0 availability check failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Validate a generation request for this model\n   */\n  validateRequest(request: ContentGenerationRequest | DesignGenerationRequest): boolean {\n    try {\n      // Basic validation\n      if (!request || !request.modelId) {\n        return false;\n      }\n\n      // Check if this is the correct model\n      if (request.modelId !== 'revo-1.0') {\n        return false;\n      }\n\n      // Content generation validation\n      if ('profile' in request) {\n        const contentRequest = request as ContentGenerationRequest;\n        return !!(\n          contentRequest.profile &&\n          contentRequest.platform &&\n          contentRequest.profile.businessType\n        );\n      }\n\n      // Design generation validation\n      if ('businessType' in request) {\n        const designRequest = request as DesignGenerationRequest;\n        return !!(\n          designRequest.businessType &&\n          designRequest.platform &&\n          designRequest.visualStyle &&\n          designRequest.brandProfile\n        );\n      }\n\n      return false;\n    } catch (error) {\n      console.error('❌ Revo 1.0 request validation failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get model-specific information\n   */\n  getModelInfo() {\n    return {\n      id: this.model.id,\n      name: this.model.name,\n      version: this.model.version,\n      description: this.model.description,\n      status: this.model.status,\n      capabilities: this.model.capabilities,\n      pricing: this.model.pricing,\n      features: this.model.features,\n      strengths: [\n        'Reliable and stable performance',\n        'Cost-effective for basic needs',\n        'Proven track record',\n        'Fast processing times',\n        'Consistent quality',\n        'Enhanced AI capabilities with Gemini 2.5 Flash Image Preview',\n        'Perfect text rendering',\n        'High-resolution 2048x2048 output',\n        'Advanced image generation'\n      ],\n      limitations: [\n        'Limited to 1:1 aspect ratio',\n        'No artifact support',\n        'Basic brand consistency',\n        'No real-time context',\n        'No video generation'\n      ],\n      bestUseCases: [\n        'Small businesses starting out',\n        'Personal brands',\n        'Budget-conscious users',\n        'Basic social media content',\n        'Consistent daily posting'\n      ]\n    };\n  }\n\n  /**\n   * Get performance metrics for this model\n   */\n  async getPerformanceMetrics() {\n    return {\n      modelId: this.model.id,\n      averageProcessingTime: 30000, // 30 seconds (upgraded from 15s)\n      successRate: 0.97, // 97% success rate (upgraded from 95%)\n      averageQualityScore: 8.5, // Upgraded from 7.2\n      costEfficiency: 'high',\n      reliability: 'excellent',\n      userSatisfaction: 4.5, // out of 5 (upgraded from 4.1)\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  /**\n   * Health check for this specific model\n   */\n  async healthCheck(): Promise<{ healthy: boolean; details: any }> {\n    try {\n      const isAvailable = await this.isAvailable();\n      const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;\n      const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;\n\n      const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;\n\n      return {\n        healthy,\n        details: {\n          modelAvailable: isAvailable,\n          contentGenerator: contentGeneratorHealthy,\n          designGenerator: designGeneratorHealthy,\n          timestamp: new Date().toISOString()\n        }\n      };\n    } catch (error) {\n      return {\n        healthy: false,\n        details: {\n          error: error instanceof Error ? error.message : 'Unknown error',\n          timestamp: new Date().toISOString()\n        }\n      };\n    }\n  }\n}\n\n// Export generators for direct use if needed\nexport { Revo10ContentGenerator } from './content-generator';\nexport { Revo10DesignGenerator } from './design-generator';\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAUD;AACA;AACA;;;;AAEO,MAAM;IACK,MAAM;IACN,iBAAoC;IACpC,gBAAkC;IAElD,aAAc;QACZ,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE;YAC5B,QAAQ,GAAG,CAAC,oCAAoC,IAAI,CAAC,KAAK,CAAC,IAAI;YAE/D,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,gBAAgB,GAAG,IAAI,yKAAA,CAAA,yBAAsB;YAClD,QAAQ,GAAG,CAAC;YAEZ,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,eAAe,GAAG,IAAI,wKAAA,CAAA,wBAAqB;YAChD,QAAQ,GAAG,CAAC;YAEZ,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oDAAoD;YAClE,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,mFAAmF;YACnF,8DAA8D;YAC9D,MAAM,YAAY,CAAC,CAAC,CAClB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,gBAAgB,OAA2D,EAAW;QACpF,IAAI;YACF,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAAE;gBAChC,OAAO;YACT;YAEA,qCAAqC;YACrC,IAAI,QAAQ,OAAO,KAAK,YAAY;gBAClC,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAI,aAAa,SAAS;gBACxB,MAAM,iBAAiB;gBACvB,OAAO,CAAC,CAAC,CACP,eAAe,OAAO,IACtB,eAAe,QAAQ,IACvB,eAAe,OAAO,CAAC,YAAY,AACrC;YACF;YAEA,+BAA+B;YAC/B,IAAI,kBAAkB,SAAS;gBAC7B,MAAM,gBAAgB;gBACtB,OAAO,CAAC,CAAC,CACP,cAAc,YAAY,IAC1B,cAAc,QAAQ,IACtB,cAAc,WAAW,IACzB,cAAc,YAAY,AAC5B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,eAAe;QACb,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,cAAc,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB;QAC5B,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE;YACtB,uBAAuB;YACvB,aAAa;YACb,qBAAqB;YACrB,gBAAgB;YAChB,aAAa;YACb,kBAAkB;YAClB,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,cAA2D;QAC/D,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW;YAC1C,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,QAAQ;YAC/E,MAAM,yBAAyB,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,QAAQ;YAE7E,MAAM,UAAU,eAAe,2BAA2B;YAE1D,OAAO;gBACL;gBACA,SAAS;oBACP,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;oBACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF;IACF;AACF", "debugId": null}}]}