{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": "AAAA,qBAAqB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAyBsB,qBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAsHsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA2PsB,6BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAiRsB,8BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA4SsB,+BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA+asB,+BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\n\"use server\";\n\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\nimport type { Artifact } from \"@/lib/types/artifacts\";\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\n\n\n// --- AI Flow Actions ---\n\ntype AnalysisResult = {\n  success: true;\n  data: BrandAnalysisResult;\n} | {\n  success: false;\n  error: string;\n  errorType: 'blocked' | 'timeout' | 'error';\n};\n\nexport async function analyzeBrandAction(\n  websiteUrl: string,\n  designImageUris: string[],\n): Promise<AnalysisResult> {\n  try {\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\n\n    // Validate URL format\n    if (!websiteUrl || !websiteUrl.trim()) {\n      return {\n        success: false,\n        error: \"Website URL is required\",\n        errorType: 'error'\n      };\n    }\n\n    // Ensure URL has protocol\n    let validUrl = websiteUrl.trim();\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\n      validUrl = 'https://' + validUrl;\n    }\n\n    const result = await analyzeBrandFlow({\n      websiteUrl: validUrl,\n      designImageUris: designImageUris || []\n    });\n\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\n    console.log(\"🔍 Result type:\", typeof result);\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\n\n    if (!result) {\n      return {\n        success: false,\n        error: \"Analysis returned empty result\",\n        errorType: 'error'\n      };\n    }\n\n    return {\n      success: true,\n      data: result\n    };\n  } catch (error) {\n    console.error(\"❌ Error analyzing brand:\", error);\n\n    // Return structured error response instead of throwing\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else if (errorMessage.includes('timeout')) {\n      return {\n        success: false,\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\n        errorType: 'timeout'\n      };\n    } else if (errorMessage.includes('CORS')) {\n      return {\n        success: false,\n        error: \"Website blocks automated access. This is common for security reasons.\",\n        errorType: 'blocked'\n      };\n    } else {\n      return {\n        success: false,\n        error: `Analysis failed: ${errorMessage}`,\n        errorType: 'error'\n      };\n    }\n  }\n}\n\nconst getAspectRatioForPlatform = (platform: Platform): string => {\n  switch (platform) {\n    case 'Instagram':\n      return '1:1'; // Square\n    case 'Facebook':\n      return '16:9'; // Landscape - Facebook posts are landscape format\n    case 'Twitter':\n      return '16:9'; // Landscape\n    case 'LinkedIn':\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\n    default:\n      return '1:1';\n  }\n}\n\nexport async function generateContentAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    const today = new Date();\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n\n    // Apply brand consistency logic\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\n      ? (profile.designExamples || [])\n      : []; // Don't use design examples if not strict consistency\n\n    // Enhanced brand profile data extraction\n    const enhancedProfile = {\n      ...profile,\n      // Ensure brand colors are available\n      primaryColor: profile.primaryColor || '#3B82F6',\n      accentColor: profile.accentColor || '#10B981',\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\n      // Extract services information\n      servicesArray: typeof profile.services === 'string'\n        ? profile.services.split('\\n').filter(s => s.trim())\n        : Array.isArray(profile.services)\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\n          : [],\n      // Extract contact information for brand context\n      contactInfo: profile.contactInfo || {},\n      socialMedia: profile.socialMedia || {},\n    };\n\n    // Convert arrays to newline-separated strings for AI processing\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\n      ? profile.keyFeatures.join('\\n')\n      : profile.keyFeatures || '';\n\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\n      ? profile.competitiveAdvantages.join('\\n')\n      : profile.competitiveAdvantages || '';\n\n    // Convert services array to newline-separated string\n    const servicesString = Array.isArray(profile.services)\n      ? profile.services.map(service =>\n        typeof service === 'object' && service.name\n          ? `${service.name}: ${service.description || ''}`\n          : service\n      ).join('\\n')\n      : profile.services || '';\n\n\n\n    // Ensure model registry is initialized\n    if (!modelRegistry.isInitialized()) {\n      console.log('🔄 Model registry not initialized, initializing now...');\n      await modelRegistry.initialize();\n    }\n\n    console.log('🔍 Model registry initialized, getting Revo 1.0 model...');\n\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\n    const revo10Model = modelRegistry.getModel('revo-1.0');\n    if (!revo10Model) {\n      console.error('❌ Revo 1.0 model not found in registry');\n      console.log('📊 Available models:', modelRegistry.getAllModels().map(m => m.model.id));\n      throw new Error('Revo 1.0 model not available');\n    }\n\n    console.log('✅ Revo 1.0 model found:', revo10Model.model.name);\n\n    const generationRequest = {\n      modelId: 'revo-1.0',\n      profile: enhancedProfile,\n      platform: platform,\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\n      contentThemes: enhancedProfile.contentThemes || [],\n      writingTone: enhancedProfile.writingTone || 'professional',\n      targetAudience: enhancedProfile.targetAudience || 'General',\n      keyFeatures: enhancedProfile.keyFeatures || [],\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\n      services: enhancedProfile.services || [],\n      visualStyle: enhancedProfile.visualStyle || 'modern',\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\n      accentColor: enhancedProfile.accentColor || '#10B981',\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\n      logoDataUrl: enhancedProfile.logoDataUrl,\n      designExamples: effectiveDesignExamples,\n      dayOfWeek: dayOfWeek,\n      currentDate: currentDate,\n      variants: [{\n        platform: platform,\n        aspectRatio: getAspectRatioForPlatform(platform),\n      }]\n    };\n\n    console.log('📝 Calling Revo 1.0 content generator...');\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\n\n    if (!result.success) {\n      console.error('❌ Revo 1.0 content generation failed:', result.error);\n      throw new Error(result.error || 'Content generation failed');\n    }\n\n    console.log('✅ Revo 1.0 content generation successful');\n    const postDetails = result.data;\n\n    const newPost: GeneratedPost = {\n      id: new Date().toISOString(),\n      date: today.toISOString(),\n      content: postDetails.content,\n      hashtags: postDetails.hashtags,\n      status: 'generated',\n      variants: postDetails.variants,\n      catchyWords: postDetails.catchyWords,\n      subheadline: postDetails.subheadline || '',\n      callToAction: postDetails.callToAction || '',\n      // Revo 1.0 doesn't include these advanced features\n      contentVariants: undefined,\n      hashtagAnalysis: undefined,\n      marketIntelligence: undefined,\n      localContext: undefined,\n    };\n\n    return newPost;\n  } catch (error) {\n    console.error(\"Error generating content:\", error);\n    throw new Error(\"Failed to generate content. Please try again later.\");\n  }\n}\n\nexport async function generateVideoContentAction(\n  profile: BrandProfile,\n  catchyWords: string,\n  postContent: string,\n): Promise<{ videoUrl: string }> {\n  try {\n    const result = await generateVideoPostFlow({\n      businessType: profile.businessType,\n      location: profile.location,\n      visualStyle: profile.visualStyle,\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\n      postContent: postContent,\n    });\n    return { videoUrl: result.videoUrl };\n  } catch (error) {\n    console.error(\"Error generating video content:\", error);\n    // Pass the specific error message from the flow to the client\n    throw new Error((error as Error).message);\n  }\n}\n\n\nexport async function generateCreativeAssetAction(\n  prompt: string,\n  outputType: 'image' | 'video',\n  referenceAssetUrl: string | null,\n  useBrandProfile: boolean,\n  brandProfile: BrandProfile | null,\n  maskDataUrl: string | null | undefined,\n  aspectRatio: '16:9' | '9:16' | undefined\n): Promise<CreativeAsset> {\n  try {\n    const result = await generateCreativeAssetFlow({\n      prompt,\n      outputType,\n      referenceAssetUrl,\n      useBrandProfile,\n      brandProfile: useBrandProfile ? brandProfile : null,\n      maskDataUrl,\n      aspectRatio,\n    });\n    return result;\n  } catch (error) {\n    console.error(\"Error generating creative asset:\", error);\n    // Always pass the specific error message from the flow to the client.\n    throw new Error((error as Error).message);\n  }\n}\n\nexport async function generateEnhancedDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\n  brandProfile?: BrandProfile,\n  enableEnhancements: boolean = true,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactInstructions?: string,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<{\n  imageUrl: string;\n  qualityScore: number;\n  enhancementsApplied: string[];\n  processingTime: number;\n}> {\n  const startTime = Date.now();\n  const enhancementsApplied: string[] = [];\n\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for enhanced design generation');\n    }\n\n    // Handle both old string format and new object format\n    let finalImageText: string;\n    if (typeof imageText === 'string') {\n      finalImageText = imageText;\n    } else {\n      // Combine catchy words, subheadline, and call-to-action\n      const components = [imageText.catchyWords];\n      if (imageText.subheadline && imageText.subheadline.trim()) {\n        components.push(imageText.subheadline.trim());\n      }\n      if (imageText.callToAction && imageText.callToAction.trim()) {\n        components.push(imageText.callToAction.trim());\n      }\n      finalImageText = components.join('\\n');\n    }\n\n    console.log('🎨 Enhanced Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', finalImageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n    console.log('- Enhancements Enabled:', enableEnhancements);\n\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\n    let result;\n\n    try {\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\n\n      result = await generateEnhancedDesign({\n        businessType,\n        platform,\n        visualStyle,\n        imageText: finalImageText,\n        brandProfile,\n        brandConsistency,\n        artifactInstructions,\n        includePeopleInDesigns,\n        useLocalLanguage,\n      });\n\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\n\n    } catch (gemini25Error) {\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\n\n      try {\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\n\n        result = await generateEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\n      } catch (openaiError) {\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\n\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n        result = await generateGeminiHDEnhancedDesignWithFallback({\n          businessType,\n          platform,\n          visualStyle,\n          imageText: finalImageText,\n          brandProfile,\n          brandConsistency,\n          artifactInstructions,\n        });\n\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\n      }\n    }\n\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      imageUrl: result.imageUrl,\n      qualityScore: result.qualityScore,\n      enhancementsApplied: result.enhancementsApplied,\n      processingTime: result.processingTime\n    };\n\n\n  } catch (error) {\n    console.error(\"Error generating enhanced design:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n\n/**\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\n * This action forces the use of Gemini HD for maximum quality\n */\nexport async function generateGeminiHDDesignAction(\n  businessType: string,\n  platform: string,\n  visualStyle: string,\n  imageText: string,\n  brandProfile: BrandProfile,\n  brandConsistency?: {\n    strictConsistency: boolean;\n    followBrandColors: boolean;\n  },\n  artifactInstructions?: string\n): Promise<PostVariant> {\n  try {\n    if (!brandProfile) {\n      throw new Error('Brand profile is required for Gemini HD design generation');\n    }\n\n    console.log('🎨 Gemini HD Design Generation Started');\n    console.log('- Business Type:', businessType);\n    console.log('- Platform:', platform);\n    console.log('- Visual Style:', visualStyle);\n    console.log('- Image Text:', imageText);\n    console.log('- Brand Profile:', brandProfile.businessName);\n\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\n\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\n      businessType,\n      platform,\n      visualStyle,\n      imageText,\n      brandProfile,\n      brandConsistency,\n      artifactInstructions,\n    });\n\n    console.log('✅ Gemini HD enhanced design generated successfully');\n    console.log('🔗 Image URL:', result.imageUrl);\n    console.log('⭐ Quality Score:', result.qualityScore);\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\n\n    return {\n      platform,\n      imageUrl: result.imageUrl,\n      caption: imageText,\n      hashtags: [],\n    };\n  } catch (error) {\n    console.error('❌ Error in Gemini HD design generation:', error);\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Generate content with artifact references (Enhanced)\n */\nexport async function generateContentWithArtifactsAction(\n  profile: BrandProfile,\n  platform: Platform,\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\n  artifactIds: string[] = [],\n  useEnhancedDesign: boolean = true,\n  includePeopleInDesigns: boolean = true,\n  useLocalLanguage: boolean = false\n): Promise<GeneratedPost> {\n  try {\n    console.log('🎨 Generating content with artifacts...');\n    console.log('- Platform:', platform);\n    console.log('- Artifacts:', artifactIds.length);\n    console.log('- Enhanced Design:', useEnhancedDesign);\n\n    // Get active artifacts if no specific artifacts provided\n    let targetArtifacts: Artifact[] = [];\n\n    if (artifactIds.length > 0) {\n      // Use specified artifacts\n      for (const artifactId of artifactIds) {\n        const artifact = artifactsService.getArtifact(artifactId);\n        if (artifact) {\n          targetArtifacts.push(artifact);\n          await artifactsService.trackUsage(artifactId, 'quick-content');\n        }\n      }\n    } else {\n      // Use active artifacts, prioritizing exact-use\n      const activeArtifacts = artifactsService.getActiveArtifacts();\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\n        id: a.id,\n        name: a.name,\n        type: a.type,\n        usageType: a.usageType,\n        isActive: a.isActive,\n        instructions: a.instructions\n      })));\n\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\n\n      // Prioritize exact-use artifacts\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\n\n      // Track usage for active artifacts\n      for (const artifact of targetArtifacts) {\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\n      }\n    }\n\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\n\n    // Generate base content first\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\n\n    // If enhanced design is disabled, return base content\n    if (!useEnhancedDesign) {\n      console.log('🔄 Enhanced design disabled, using base content generation');\n      return basePost;\n    }\n\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\n\n    if (targetArtifacts.length === 0) {\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\n    } else {\n      console.log('🎯 Using enhanced design with artifact context');\n    }\n\n    // Separate exact-use and reference artifacts\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\n\n    // Create enhanced image text structure from post components\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\n      catchyWords: basePost.catchyWords || 'Engaging Content',\n      subheadline: basePost.subheadline,\n      callToAction: basePost.callToAction\n    };\n    let enhancedContent = basePost.content;\n\n    // Collect usage instructions from artifacts\n    const artifactInstructions = targetArtifacts\n      .filter(a => a.instructions && a.instructions.trim())\n      .map(a => `- ${a.name}: ${a.instructions}`)\n      .join('\\n');\n\n    // Collect text overlay instructions from text artifacts\n    const textOverlayInstructions = exactUseArtifacts\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\n      .join('\\n');\n\n    // Process exact-use artifacts first (higher priority)\n    if (exactUseArtifacts.length > 0) {\n      const primaryExactUse = exactUseArtifacts[0];\n\n      // Use text overlay if available\n      if (primaryExactUse.textOverlay) {\n        if (primaryExactUse.textOverlay.headline) {\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\n        }\n\n        if (primaryExactUse.textOverlay.message) {\n          enhancedContent = primaryExactUse.textOverlay.message;\n          console.log('📝 Using message from exact-use artifact');\n        }\n\n        // Use CTA from artifact if available\n        if (primaryExactUse.textOverlay.cta) {\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\n        }\n      }\n    }\n\n    // Process reference artifacts for style guidance\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\n      artifact.directives.filter(directive => directive.active)\n    );\n\n    // Apply style reference directives\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\n    let visualStyleOverride = profile.visualStyle || 'modern';\n    if (styleDirectives.length > 0) {\n      console.log('🎨 Applying style references from artifacts');\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\n      if (primaryStyleDirective) {\n        visualStyleOverride = 'artifact-inspired';\n        console.log('🎨 Using artifact-inspired visual style');\n      }\n    }\n\n    // Combine all instructions\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\n      .filter(Boolean)\n      .join('\\n');\n\n    // Generate enhanced design with artifact context\n    const enhancedResult = await generateEnhancedDesignAction(\n      profile.businessType || 'business',\n      platform.toLowerCase(),\n      visualStyleOverride,\n      enhancedImageText,\n      profile,\n      true,\n      brandConsistency,\n      allInstructions || undefined,\n      includePeopleInDesigns,\n      useLocalLanguage\n    );\n\n    // Create enhanced post with artifact metadata\n    const enhancedPost: GeneratedPost = {\n      ...basePost,\n      id: Date.now().toString(),\n      variants: [{\n        platform: platform,\n        imageUrl: enhancedResult.imageUrl\n      }],\n      content: targetArtifacts.length > 0\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\n      date: new Date().toISOString(),\n      // Add artifact metadata\n      metadata: {\n        ...basePost.metadata,\n        referencedArtifacts: targetArtifacts.map(a => ({\n          id: a.id,\n          name: a.name,\n          type: a.type,\n          category: a.category\n        })),\n        activeDirectives: activeDirectives.map(d => ({\n          id: d.id,\n          type: d.type,\n          label: d.label,\n          priority: d.priority\n        }))\n      }\n    };\n\n    console.log('✅ Enhanced content with artifacts generated successfully');\n    return enhancedPost;\n\n  } catch (error) {\n    console.error(\"Error generating content with artifacts:\", error);\n    throw new Error((error as Error).message);\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAwesB,qCAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}]}