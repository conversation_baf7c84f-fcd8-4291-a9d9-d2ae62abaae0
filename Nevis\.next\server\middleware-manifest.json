{"version": 3, "middleware": {}, "sortedMiddleware": [], "functions": {"/icon/route": {"files": ["server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/icon/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_icon_route_actions_54ebf826.js", "server/edge/chunks/_next-internal_server_app_icon_route_actions_7b43ef7d.js", "server/edge/chunks/node_modules_next_dist_esm_d6e759b5._.js", "server/edge/chunks/node_modules_next_dist_compiled_react-server-dom-turbopack_0768bf67._.js", "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_85cae31e._.js", "server/edge/chunks/node_modules_next_dist_compiled_f31f06d2._.js", "server/edge/chunks/node_modules_next_dist_42d403f2._.js", "server/edge/chunks/edge-wrapper_f538444c.js", "server/edge/chunks/[root-of-the-server]__0cce1483._.js", "server/edge/chunks/edge-wrapper_33aa1a8c.js", "server/app/icon/route/react-loadable-manifest.js"], "name": "/icon", "page": "/icon/route", "matchers": [{"regexp": "^/icon(?:/)?$", "originalSource": "/icon"}], "wasm": [{"name": "wasm_node_modules_next_dist_compiled__vercel_og_yoga_325b988e", "filePath": "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_yoga_325b988e.wasm"}, {"name": "wasm_node_modules_next_dist_compiled__vercel_og_resvg_325b988e", "filePath": "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_resvg_325b988e.wasm"}], "assets": [{"name": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf", "filePath": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OVV97pzKDpObcGG/9IMhtgJ6C6Y4TQTYg9GiRknr/vI=", "__NEXT_PREVIEW_MODE_ID": "e5d0cca4cfacb32690b6e84a25c0ae3e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "92640cbeaa41ddd737a85a7a2e7dbda422083a79b9ab1415c7973e3b1708613f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eb38c9d68e66c1767416f2961cf75e6b9d26a7075acac481916f1525c1195e65"}}}}