self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f79b801f803feb5e76e403a11c5c7baf170b29d06\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7f491863d2325193ec3f40e1aa06031f7511fddf7d\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"780ea7fbdd7a6fb866af6c2bb7b448ab0a031fc67f\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"78f16784e7e21db600c083fc17664954c78d989188\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7f2ccbac81b04d4b1a191d767822e0665844079bf0\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"707f98327e60040c1b2ee19b7969f1295904475702\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"60d74caca804adb2b547ce0cf49c6b4a219392377c\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7f20bd895b2f9b19034d2b89ca4aad1c2c7dd696c8\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"40515da45dbc1e56b768ff9d60386bd092ddf23aa4\": {\n      \"workers\": {\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"OVV97pzKDpObcGG/9IMhtgJ6C6Y4TQTYg9GiRknr/vI=\"\n}"