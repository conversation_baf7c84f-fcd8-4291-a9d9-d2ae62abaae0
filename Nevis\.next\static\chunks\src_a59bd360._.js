(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/label.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const labelVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(labelVariants(), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/label.tsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
_c1 = Label;
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Label$React.forwardRef");
__turbopack_context__.k.register(_c1, "Label");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 8,
        columnNumber: 7
    }, this);
});
_c1 = Textarea;
Textarea.displayName = 'Textarea';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Textarea$React.forwardRef");
__turbopack_context__.k.register(_c1, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:ab8590 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"78f16784e7e21db600c083fc17664954c78d989188":"generateContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentAction": (()=>generateContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("78f16784e7e21db600c083fc17664954c78d989188", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:7d8916 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"707f98327e60040c1b2ee19b7969f1295904475702":"generateVideoContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateVideoContentAction": (()=>generateVideoContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateVideoContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("707f98327e60040c1b2ee19b7969f1295904475702", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateVideoContentAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tabs": (()=>Tabs),
    "TabsContent": (()=>TabsContent),
    "TabsList": (()=>TabsList),
    "TabsTrigger": (()=>TabsTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-tabs/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Tabs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"];
const TabsList = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["List"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tabs.tsx",
        lineNumber: 14,
        columnNumber: 3
    }, this));
_c1 = TabsList;
TabsList.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["List"].displayName;
const TabsTrigger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c2 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tabs.tsx",
        lineNumber: 29,
        columnNumber: 3
    }, this));
_c3 = TabsTrigger;
TabsTrigger.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"].displayName;
const TabsContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c4 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tabs.tsx",
        lineNumber: 44,
        columnNumber: 3
    }, this));
_c5 = TabsContent;
TabsContent.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"].displayName;
;
var _c, _c1, _c2, _c3, _c4, _c5;
__turbopack_context__.k.register(_c, "TabsList$React.forwardRef");
__turbopack_context__.k.register(_c1, "TabsList");
__turbopack_context__.k.register(_c2, "TabsTrigger$React.forwardRef");
__turbopack_context__.k.register(_c3, "TabsTrigger");
__turbopack_context__.k.register(_c4, "TabsContent$React.forwardRef");
__turbopack_context__.k.register(_c5, "TabsContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/dashboard/post-card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/dashboard/post-card.tsx
__turbopack_context__.s({
    "PostCard": (()=>PostCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/facebook.js [app-client] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/instagram.js [app-client] (ecmascript) <export default as Instagram>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-client] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js [app-client] (ecmascript) <export default as MoreVertical>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pen.js [app-client] (ecmascript) <export default as Pen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/twitter.js [app-client] (ecmascript) <export default as Twitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as CalendarIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/video.js [app-client] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ImageOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/image-off.js [app-client] (ecmascript) <export default as ImageOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-client] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-client] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$to$2d$image$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html-to-image/es/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ab8590__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:ab8590 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$7d8916__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:7d8916 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Helper function to validate URLs
const isValidUrl = (url)=>{
    if (!url || typeof url !== 'string') {
        return false;
    }
    // Handle compression placeholders
    if (url === '[COMPRESSED_IMAGE]' || url === '[TRUNCATED]' || url.includes('[') && url.includes(']')) {
        return false;
    }
    try {
        // Check for data URLs (base64 images)
        if (url.startsWith('data:')) {
            return url.includes('base64,') || url.includes('charset=');
        }
        // Check for HTTP/HTTPS URLs
        const parsedUrl = new URL(url);
        return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch (error) {
        // Don't log compression placeholders as errors
        if (!url.includes('[') || !url.includes(']')) {
            console.warn('URL validation failed for:', url.substring(0, 50) + '...', error.message);
        }
        return false;
    }
};
/**
 * Utility function to detect image format from data URL
 */ function getImageFormatFromDataUrl(dataUrl) {
    if (dataUrl.startsWith('data:image/svg+xml')) {
        return {
            format: 'svg',
            extension: 'svg'
        };
    } else if (dataUrl.startsWith('data:image/png;base64,')) {
        return {
            format: 'png',
            extension: 'png'
        };
    } else if (dataUrl.startsWith('data:image/jpeg;base64,') || dataUrl.startsWith('data:image/jpg;base64,')) {
        return {
            format: 'jpeg',
            extension: 'jpg'
        };
    } else if (dataUrl.startsWith('data:image/webp;base64,')) {
        return {
            format: 'webp',
            extension: 'webp'
        };
    }
    return {
        format: 'png',
        extension: 'png'
    }; // default fallback
}
const platformIcons = {
    Facebook: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 88,
        columnNumber: 13
    }, this),
    Instagram: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 89,
        columnNumber: 14
    }, this),
    LinkedIn: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 90,
        columnNumber: 13
    }, this),
    Twitter: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__["Twitter"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 91,
        columnNumber: 12
    }, this)
};
function PostCard({ post, brandProfile, onPostUpdated }) {
    _s();
    const [isEditing, setIsEditing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isRegenerating, setIsRegenerating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isGeneratingVideo, setIsGeneratingVideo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editedContent, setEditedContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(post.content);
    const [editedHashtags, setEditedHashtags] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(post.hashtags);
    const [videoUrl, setVideoUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(post.videoUrl);
    const [showVideoDialog, setShowVideoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showImagePreview, setShowImagePreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [previewImageUrl, setPreviewImageUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Ensure variants array exists and has at least one item
    const safeVariants = post.variants && post.variants.length > 0 ? post.variants : [
        {
            platform: post.platform || 'instagram',
            imageUrl: post.imageUrl || ''
        }
    ];
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(safeVariants[0]?.platform || 'instagram');
    const downloadRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    // Check if this is a Revo 2.0 post (single platform)
    const isRevo2Post = post.id?.startsWith('revo2-') || safeVariants.length === 1;
    const formattedDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "PostCard.useMemo[formattedDate]": ()=>{
            try {
                const date = new Date(post.date);
                if (isNaN(date.getTime())) {
                    // If date is invalid, use current date
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), 'MMM d, yyyy');
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'MMM d, yyyy');
            } catch (error) {
                // Fallback to current date if any error occurs
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), 'MMM d, yyyy');
            }
        }
    }["PostCard.useMemo[formattedDate]"], [
        post.date
    ]);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    // Platform-specific dimensions - MUST match backend Revo 2.0 generation
    const getPlatformDimensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PostCard.useCallback[getPlatformDimensions]": (platform)=>{
            switch(platform.toLowerCase()){
                case 'instagram':
                    return {
                        width: 1080,
                        height: 1080,
                        aspectClass: 'aspect-square'
                    };
                case 'facebook':
                    return {
                        width: 1200,
                        height: 675,
                        aspectClass: 'aspect-[16/9]'
                    };
                case 'twitter':
                    return {
                        width: 1200,
                        height: 675,
                        aspectClass: 'aspect-[16/9]'
                    };
                case 'linkedin':
                    return {
                        width: 1200,
                        height: 675,
                        aspectClass: 'aspect-[16/9]'
                    };
                case 'tiktok':
                    return {
                        width: 1080,
                        height: 1920,
                        aspectClass: 'aspect-[9/16]'
                    };
                default:
                    return {
                        width: 1080,
                        height: 1080,
                        aspectClass: 'aspect-square'
                    };
            }
        }
    }["PostCard.useCallback[getPlatformDimensions]"], []);
    // Copy functionality
    const handleCopyCaption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PostCard.useCallback[handleCopyCaption]": async ()=>{
            try {
                await navigator.clipboard.writeText(post.content);
                toast({
                    title: "Caption Copied!",
                    description: "The caption has been copied to your clipboard."
                });
            } catch (error) {
                toast({
                    variant: "destructive",
                    title: "Copy Failed",
                    description: "Could not copy the caption. Please try again."
                });
            }
        }
    }["PostCard.useCallback[handleCopyCaption]"], [
        post.content,
        toast
    ]);
    const handleCopyHashtags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PostCard.useCallback[handleCopyHashtags]": async ()=>{
            try {
                const hashtagsText = typeof post.hashtags === 'string' ? post.hashtags : post.hashtags?.join(' ') || '';
                await navigator.clipboard.writeText(hashtagsText);
                toast({
                    title: "Hashtags Copied!",
                    description: "The hashtags have been copied to your clipboard."
                });
            } catch (error) {
                toast({
                    variant: "destructive",
                    title: "Copy Failed",
                    description: "Could not copy the hashtags. Please try again."
                });
            }
        }
    }["PostCard.useCallback[handleCopyHashtags]"], [
        post.hashtags,
        toast
    ]);
    // Image preview functionality
    const handleImagePreview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PostCard.useCallback[handleImagePreview]": (imageUrl)=>{
            setPreviewImageUrl(imageUrl);
            setShowImagePreview(true);
        }
    }["PostCard.useCallback[handleImagePreview]"], []);
    const handleDownload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PostCard.useCallback[handleDownload]": async ()=>{
            const activeVariant = safeVariants.find({
                "PostCard.useCallback[handleDownload].activeVariant": (v)=>v.platform === activeTab
            }["PostCard.useCallback[handleDownload].activeVariant"]);
            // First try to download the original HD image directly if URL is valid
            if (activeVariant?.imageUrl && isValidUrl(activeVariant.imageUrl)) {
                try {
                    // Check if it's a data URL (base64 encoded image)
                    if (activeVariant.imageUrl.startsWith('data:')) {
                        const { format, extension } = getImageFormatFromDataUrl(activeVariant.imageUrl);
                        // For social media posts, we need raster images (PNG/JPEG), not SVG
                        if (format === 'svg') {
                            console.log('🎨 Converting SVG to PNG for social media compatibility...');
                        // Fall through to the canvas conversion method below
                        // This will convert the SVG to a high-quality PNG
                        } else {
                            // Handle other data URL formats (PNG, JPEG, etc.) directly
                            const link = document.createElement('a');
                            link.href = activeVariant.imageUrl;
                            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            toast({
                                title: "Social Media Image Ready",
                                description: `High-definition ${format.toUpperCase()} image downloaded successfully.`
                            });
                            return;
                        }
                    } else {
                        // Handle regular HTTP/HTTPS URLs (not data URLs)
                        try {
                            const response = await fetch(activeVariant.imageUrl);
                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            // Determine file extension based on content type
                            const contentType = response.headers.get('content-type') || blob.type;
                            let extension = 'png'; // default
                            if (contentType.includes('jpeg') || contentType.includes('jpg')) {
                                extension = 'jpg';
                            } else if (contentType.includes('webp')) {
                                extension = 'webp';
                            }
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            window.URL.revokeObjectURL(url);
                            toast({
                                title: "Social Media Image Ready",
                                description: "High-definition image downloaded successfully."
                            });
                            return;
                        } catch (error) {
                            console.warn('Direct download failed, falling back to canvas conversion:', error);
                        // Fall through to canvas conversion
                        }
                    }
                } catch (error) {
                    console.warn('Direct HD download failed, falling back to capture method:', error);
                }
            }
            // Fallback: Capture the displayed image with maximum quality settings
            const nodeToCapture = downloadRefs.current[activeTab];
            if (!nodeToCapture) {
                toast({
                    variant: "destructive",
                    title: "Download Failed",
                    description: "Could not find the image element to download."
                });
                return;
            }
            try {
                // Check if we're converting an SVG enhanced design
                const activeVariant = safeVariants.find({
                    "PostCard.useCallback[handleDownload].activeVariant": (v)=>v.platform === activeTab
                }["PostCard.useCallback[handleDownload].activeVariant"]);
                const isSvgDataUrl = activeVariant?.imageUrl?.startsWith('data:image/svg+xml');
                const platformDimensions = getPlatformDimensions(activeTab);
                // Platform-specific optimized settings for social media posts
                const socialMediaSettings = {
                    cacheBust: true,
                    canvasWidth: platformDimensions.width,
                    canvasHeight: platformDimensions.height,
                    pixelRatio: 3,
                    quality: 1.0,
                    backgroundColor: '#ffffff',
                    style: {
                        borderRadius: '0',
                        border: 'none'
                    }
                };
                // Enhanced settings for SVG conversion
                if (isSvgDataUrl) {
                    socialMediaSettings.canvasWidth = platformDimensions.width;
                    socialMediaSettings.canvasHeight = platformDimensions.height;
                    socialMediaSettings.pixelRatio = 4; // Extra high DPI for SVG conversion
                    console.log(`🎨 Converting enhanced SVG design to ${platformDimensions.width}x${platformDimensions.height} PNG for ${activeTab}...`);
                }
                const dataUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$to$2d$image$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPng"])(nodeToCapture, socialMediaSettings);
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = `nevis-social-${post.id}-${activeTab}.png`;
                link.click();
                // Provide specific feedback based on content type
                const successMessage = isSvgDataUrl ? "Enhanced design converted to PNG for social media use." : "High-definition image ready for social media posting.";
                toast({
                    title: "Social Media Image Ready",
                    description: successMessage
                });
            } catch (err) {
                console.error(err);
                toast({
                    variant: "destructive",
                    title: "Download Failed",
                    description: `Could not download the image. Please try again. Error: ${err.message}`
                });
            }
        }
    }["PostCard.useCallback[handleDownload]"], [
        post.id,
        activeTab,
        toast
    ]);
    const handleSaveChanges = async ()=>{
        const updatedPost = {
            ...post,
            content: editedContent,
            hashtags: editedHashtags,
            status: 'edited'
        };
        await onPostUpdated(updatedPost);
        setIsEditing(false);
        toast({
            title: "Post Updated",
            description: "Your changes have been saved."
        });
    };
    const handleRegenerate = async ()=>{
        setIsRegenerating(true);
        try {
            const platform = safeVariants[0].platform;
            const newPost = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ab8590__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentAction"])(brandProfile, platform);
            onPostUpdated({
                ...newPost,
                id: post.id
            }); // Keep old id for replacement
            toast({
                title: "Post Regenerated!",
                description: "A new version of your post has been generated."
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Regeneration Failed",
                description: error.message
            });
        } finally{
            setIsRegenerating(false);
        }
    };
    const handleGenerateVideo = async ()=>{
        if (!post.catchyWords) {
            toast({
                variant: "destructive",
                title: "Cannot Generate Video",
                description: "The post is missing the required catchy words."
            });
            return;
        }
        setIsGeneratingVideo(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$7d8916__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateVideoContentAction"])(brandProfile, post.catchyWords, post.content);
            const newVideoUrl = result.videoUrl;
            setVideoUrl(newVideoUrl);
            await onPostUpdated({
                ...post,
                videoUrl: newVideoUrl
            });
            setShowVideoDialog(true);
            toast({
                title: "Video Generated!",
                description: "Your video is ready to be viewed."
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Video Generation Failed",
                description: error.message
            });
        } finally{
            setIsGeneratingVideo(false);
        }
    };
    const activeVariant = safeVariants.find((v)=>v.platform === activeTab) || safeVariants[0];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                className: "flex flex-col w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        className: "flex-row items-center justify-between gap-4 p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2 text-sm font-medium text-muted-foreground",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__["CalendarIcon"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 405,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: formattedDate
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 406,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 404,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            size: "icon",
                                            variant: "ghost",
                                            className: "h-6 w-6",
                                            disabled: isRegenerating || isGeneratingVideo,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__["MoreVertical"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 411,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 410,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 409,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        align: "end",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>setIsEditing(true),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pen$3e$__["Pen"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 416,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Edit Text"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 415,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: handleRegenerate,
                                                disabled: isRegenerating,
                                                children: [
                                                    isRegenerating ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "mr-2 h-4 w-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 421,
                                                        columnNumber: 19
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 423,
                                                        columnNumber: 19
                                                    }, this),
                                                    "Regenerate Image"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 419,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: handleGenerateVideo,
                                                disabled: isGeneratingVideo,
                                                children: [
                                                    isGeneratingVideo ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "mr-2 h-4 w-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 429,
                                                        columnNumber: 19
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 431,
                                                        columnNumber: 19
                                                    }, this),
                                                    "Generate Video"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 427,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: handleDownload,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 436,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Download Image"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 435,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 414,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 408,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                        lineNumber: 403,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "flex-grow space-y-4 p-4 pt-0",
                        children: [
                            isRevo2Post ? // Revo 2.0 single-platform layout with platform icon at top left
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-start p-3 bg-muted/30 rounded-lg",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: platformIcons[safeVariants[0]?.platform || 'instagram']
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 448,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 447,
                                        columnNumber: 15
                                    }, this),
                                    (()=>{
                                        const variant = safeVariants[0];
                                        const dimensions = getPlatformDimensions(variant?.platform || 'instagram');
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `relative ${dimensions.aspectClass} w-full overflow-hidden`,
                                            children: [
                                                (isRegenerating || isGeneratingVideo) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute inset-0 z-10 flex items-center justify-center bg-card/80",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                            className: "h-8 w-8 animate-spin text-primary"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 462,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: isRegenerating ? 'Regenerating image...' : 'Generating video...'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 463,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                    lineNumber: 461,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: (el)=>downloadRefs.current[variant?.platform || 'instagram'] = el,
                                                    className: `relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`,
                                                    children: variant?.imageUrl && isValidUrl(variant.imageUrl) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative h-full w-full cursor-pointer",
                                                        onClick: ()=>handleImagePreview(variant.imageUrl),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                alt: `Generated post image for ${variant.platform}`,
                                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('h-full w-full object-cover transition-opacity', isRegenerating || isGeneratingVideo ? 'opacity-50' : 'opacity-100'),
                                                                height: dimensions.height,
                                                                src: variant.imageUrl,
                                                                "data-ai-hint": "social media post",
                                                                width: dimensions.width,
                                                                crossOrigin: "anonymous",
                                                                unoptimized: variant.imageUrl.startsWith('data:')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 472,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "bg-white/90 rounded-full p-2",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                        className: "h-5 w-5 text-gray-700"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 485,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 484,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 483,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 468,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex h-full w-full items-center justify-center bg-muted flex-col gap-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ImageOff$3e$__["ImageOff"], {
                                                                className: "h-12 w-12 text-muted-foreground"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 491,
                                                                columnNumber: 27
                                                            }, this),
                                                            variant?.imageUrl && !isValidUrl(variant.imageUrl) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "absolute bottom-2 left-2 right-2",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs text-red-500 bg-white/90 p-2 rounded",
                                                                    children: variant.imageUrl.includes('[') && variant.imageUrl.includes(']') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "font-medium",
                                                                                children: "Image temporarily unavailable"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                                lineNumber: 497,
                                                                                columnNumber: 37
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-gray-600 mt-1",
                                                                                children: variant.imageUrl.includes('Large image data removed') ? 'Image was too large for storage. Try regenerating.' : 'Image data was optimized for storage.'
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                                lineNumber: 498,
                                                                                columnNumber: 37
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 496,
                                                                        columnNumber: 35
                                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        children: "Invalid image URL"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 506,
                                                                        columnNumber: 35
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 494,
                                                                    columnNumber: 31
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 493,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 490,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                    lineNumber: 466,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 459,
                                            columnNumber: 19
                                        }, this);
                                    })()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 445,
                                columnNumber: 13
                            }, this) : // Multi-platform tab layout for Revo 1.0/1.5
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                                value: activeTab,
                                onValueChange: (v)=>setActiveTab(v),
                                className: "w-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                                        className: "grid w-full grid-cols-4",
                                        children: safeVariants.map((variant)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                                value: variant.platform,
                                                children: platformIcons[variant.platform]
                                            }, variant.platform, false, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 523,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 521,
                                        columnNumber: 15
                                    }, this),
                                    safeVariants.map((variant)=>{
                                        const dimensions = getPlatformDimensions(variant.platform);
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                            value: variant.platform,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `relative ${dimensions.aspectClass} w-full overflow-hidden`,
                                                children: [
                                                    (isRegenerating || isGeneratingVideo) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "absolute inset-0 z-10 flex items-center justify-center bg-card/80",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                                className: "h-8 w-8 animate-spin text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 535,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "sr-only",
                                                                children: isRegenerating ? 'Regenerating image...' : 'Generating video...'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 536,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 534,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        ref: (el)=>downloadRefs.current[variant.platform] = el,
                                                        className: `relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`,
                                                        children: variant.imageUrl && isValidUrl(variant.imageUrl) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative h-full w-full cursor-pointer",
                                                            onClick: ()=>handleImagePreview(variant.imageUrl),
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    alt: `Generated post image for ${variant.platform}`,
                                                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('h-full w-full object-cover transition-opacity', isRegenerating || isGeneratingVideo ? 'opacity-50' : 'opacity-100'),
                                                                    height: dimensions.height,
                                                                    src: variant.imageUrl,
                                                                    "data-ai-hint": "social media post",
                                                                    width: dimensions.width,
                                                                    crossOrigin: "anonymous",
                                                                    unoptimized: variant.imageUrl.startsWith('data:')
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 545,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "bg-white/90 rounded-full p-2",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                            className: "h-5 w-5 text-gray-700"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                            lineNumber: 558,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 557,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 556,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 541,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex h-full w-full items-center justify-center bg-muted",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$off$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ImageOff$3e$__["ImageOff"], {
                                                                    className: "h-12 w-12 text-muted-foreground"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 564,
                                                                    columnNumber: 29
                                                                }, this),
                                                                variant.imageUrl && !isValidUrl(variant.imageUrl) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "absolute bottom-2 left-2 right-2",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-xs text-red-500 bg-white/90 p-1 rounded",
                                                                        children: "Invalid image URL"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 567,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 566,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 563,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 539,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 532,
                                                columnNumber: 21
                                            }, this)
                                        }, variant.platform, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 531,
                                            columnNumber: 19
                                        }, this);
                                    })
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 520,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-start justify-between gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-foreground line-clamp-4 flex-1",
                                            children: post.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 584,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            onClick: handleCopyCaption,
                                            className: "h-8 w-8 p-0 flex-shrink-0",
                                            title: "Copy caption",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 592,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 585,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 583,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 582,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                        lineNumber: 442,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardFooter"], {
                        className: "p-4 pt-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start justify-between gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-1 flex-1",
                                    children: [
                                        post.hashtags && (()=>{
                                            // Handle both string and array formats for hashtags
                                            const hashtagsArray = typeof post.hashtags === 'string' ? post.hashtags.split(" ") : Array.isArray(post.hashtags) ? post.hashtags : [];
                                            return hashtagsArray.map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: "secondary",
                                                    className: "font-normal",
                                                    children: tag
                                                }, index, false, {
                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                    lineNumber: 609,
                                                    columnNumber: 19
                                                }, this));
                                        })(),
                                        !post.hashtags && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "secondary",
                                            className: "font-normal",
                                            children: "#enhanced #ai #design"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 615,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 599,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "ghost",
                                    size: "sm",
                                    onClick: handleCopyHashtags,
                                    className: "h-8 w-8 p-0 flex-shrink-0",
                                    title: "Copy hashtags",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 627,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 620,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 598,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                        lineNumber: 597,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 402,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dialog"], {
                open: isEditing,
                onOpenChange: setIsEditing,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-[600px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: "Edit Post"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 637,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: "Make changes to your post content and hashtags below. Click save when you're done."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 638,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 636,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid gap-4 py-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                            htmlFor: "content",
                                            children: "Content"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 644,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                            id: "content",
                                            value: editedContent,
                                            onChange: (e)=>setEditedContent(e.target.value),
                                            className: "h-32"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 645,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 643,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                            htmlFor: "hashtags",
                                            children: "Hashtags"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 653,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                            id: "hashtags",
                                            value: editedHashtags,
                                            onChange: (e)=>setEditedHashtags(e.target.value)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 654,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 652,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 642,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    onClick: ()=>setIsEditing(false),
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 662,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    onClick: handleSaveChanges,
                                    children: "Save Changes"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 663,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 661,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                    lineNumber: 635,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 634,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dialog"], {
                open: showVideoDialog,
                onOpenChange: setShowVideoDialog,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-[600px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: "Generated Video"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 672,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: "Here is the video generated for your post. You can download it from here."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 673,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 671,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "my-4",
                            children: videoUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                controls: true,
                                autoPlay: true,
                                src: videoUrl,
                                className: "w-full rounded-md"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 679,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "No video available."
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 681,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 677,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: ()=>setShowVideoDialog(false),
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 685,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 684,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                    lineNumber: 670,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 669,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dialog"], {
                open: showImagePreview,
                onOpenChange: setShowImagePreview,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-4xl max-h-[90vh] p-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            className: "pb-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: "Image Preview"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 694,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: "Click and drag to pan, scroll to zoom"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 695,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 693,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center max-h-[70vh] overflow-hidden",
                            children: previewImageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                src: previewImageUrl,
                                alt: "Post image preview",
                                className: "max-w-full max-h-full object-contain rounded-lg"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 701,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 699,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: ()=>setShowImagePreview(false),
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 709,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 708,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                    lineNumber: 692,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 691,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(PostCard, "mEmufCmzy4Mis8KpcfEQuv88AtU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = PostCard;
var _c;
__turbopack_context__.k.register(_c, "PostCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:a7eaa4 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f2ccbac81b04d4b1a191d767822e0665844079bf0":"generateContentWithArtifactsAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentWithArtifactsAction": (()=>generateContentWithArtifactsAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateContentWithArtifactsAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f2ccbac81b04d4b1a191d767822e0665844079bf0", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentWithArtifactsAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-brand-profiles.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Hook for managing brand profiles with Firestore
__turbopack_context__.s({
    "useBrandProfiles": (()=>useBrandProfiles),
    "useCurrentBrandProfile": (()=>useCurrentBrandProfile),
    "useHasCompleteBrandProfile": (()=>useHasCompleteBrandProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/services/brand-profile-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
function useBrandProfiles() {
    _s();
    const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserId"])();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        profiles: [],
        currentProfile: null,
        loading: true,
        error: null,
        saving: false
    });
    // Load brand profiles
    const loadProfiles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBrandProfiles.useCallback[loadProfiles]": async ()=>{
            if (!userId) {
                setState({
                    "useBrandProfiles.useCallback[loadProfiles]": (prev)=>({
                            ...prev,
                            loading: false,
                            profiles: [],
                            currentProfile: null
                        })
                }["useBrandProfiles.useCallback[loadProfiles]"]);
                return;
            }
            try {
                setState({
                    "useBrandProfiles.useCallback[loadProfiles]": (prev)=>({
                            ...prev,
                            loading: true,
                            error: null
                        })
                }["useBrandProfiles.useCallback[loadProfiles]"]);
                // Try to load from Firestore, fallback to localStorage
                let profiles = [];
                try {
                    profiles = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].getUserBrandProfiles(userId);
                } catch (firebaseError) {
                    console.log('🔄 Firebase unavailable, using localStorage fallback');
                    // Fallback to localStorage
                    const stored = localStorage.getItem('completeBrandProfile');
                    if (stored) {
                        const profile = JSON.parse(stored);
                        profiles = [
                            profile
                        ];
                    }
                }
                const currentProfile = profiles.length > 0 ? profiles[0] : null;
                setState({
                    "useBrandProfiles.useCallback[loadProfiles]": (prev)=>({
                            ...prev,
                            profiles,
                            currentProfile,
                            loading: false
                        })
                }["useBrandProfiles.useCallback[loadProfiles]"]);
            } catch (error) {
                setState({
                    "useBrandProfiles.useCallback[loadProfiles]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: error instanceof Error ? error.message : 'Failed to load profiles'
                        })
                }["useBrandProfiles.useCallback[loadProfiles]"]);
            }
        }
    }["useBrandProfiles.useCallback[loadProfiles]"], [
        userId
    ]);
    // Save brand profile
    const saveProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBrandProfiles.useCallback[saveProfile]": async (profile)=>{
            if (!userId) {
                throw new Error('User must be authenticated to save profile');
            }
            try {
                setState({
                    "useBrandProfiles.useCallback[saveProfile]": (prev)=>({
                            ...prev,
                            saving: true,
                            error: null
                        })
                }["useBrandProfiles.useCallback[saveProfile]"]);
                const profileId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].saveBrandProfile(profile, userId);
                // Reload profiles to get the updated list
                await loadProfiles();
                setState({
                    "useBrandProfiles.useCallback[saveProfile]": (prev)=>({
                            ...prev,
                            saving: false
                        })
                }["useBrandProfiles.useCallback[saveProfile]"]);
                return profileId;
            } catch (error) {
                setState({
                    "useBrandProfiles.useCallback[saveProfile]": (prev)=>({
                            ...prev,
                            saving: false,
                            error: error instanceof Error ? error.message : 'Failed to save profile'
                        })
                }["useBrandProfiles.useCallback[saveProfile]"]);
                throw error;
            }
        }
    }["useBrandProfiles.useCallback[saveProfile]"], [
        userId,
        loadProfiles
    ]);
    // Update brand profile
    const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBrandProfiles.useCallback[updateProfile]": async (profileId, updates)=>{
            if (!userId) {
                throw new Error('User must be authenticated to update profile');
            }
            try {
                setState({
                    "useBrandProfiles.useCallback[updateProfile]": (prev)=>({
                            ...prev,
                            saving: true,
                            error: null
                        })
                }["useBrandProfiles.useCallback[updateProfile]"]);
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].updateBrandProfile(profileId, updates);
                // Update local state optimistically
                setState({
                    "useBrandProfiles.useCallback[updateProfile]": (prev)=>({
                            ...prev,
                            profiles: prev.profiles.map({
                                "useBrandProfiles.useCallback[updateProfile]": (p)=>p.id === profileId ? {
                                        ...p,
                                        ...updates
                                    } : p
                            }["useBrandProfiles.useCallback[updateProfile]"]),
                            currentProfile: prev.currentProfile?.id === profileId ? {
                                ...prev.currentProfile,
                                ...updates
                            } : prev.currentProfile,
                            saving: false
                        })
                }["useBrandProfiles.useCallback[updateProfile]"]);
            } catch (error) {
                setState({
                    "useBrandProfiles.useCallback[updateProfile]": (prev)=>({
                            ...prev,
                            saving: false,
                            error: error instanceof Error ? error.message : 'Failed to update profile'
                        })
                }["useBrandProfiles.useCallback[updateProfile]"]);
                throw error;
            }
        }
    }["useBrandProfiles.useCallback[updateProfile]"], [
        userId
    ]);
    // Delete brand profile
    const deleteProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBrandProfiles.useCallback[deleteProfile]": async (profileId)=>{
            if (!userId) {
                throw new Error('User must be authenticated to delete profile');
            }
            try {
                setState({
                    "useBrandProfiles.useCallback[deleteProfile]": (prev)=>({
                            ...prev,
                            error: null
                        })
                }["useBrandProfiles.useCallback[deleteProfile]"]);
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].delete(profileId);
                // Update local state
                setState({
                    "useBrandProfiles.useCallback[deleteProfile]": (prev)=>({
                            ...prev,
                            profiles: prev.profiles.filter({
                                "useBrandProfiles.useCallback[deleteProfile]": (p)=>p.id !== profileId
                            }["useBrandProfiles.useCallback[deleteProfile]"]),
                            currentProfile: prev.currentProfile?.id === profileId ? null : prev.currentProfile
                        })
                }["useBrandProfiles.useCallback[deleteProfile]"]);
            } catch (error) {
                setState({
                    "useBrandProfiles.useCallback[deleteProfile]": (prev)=>({
                            ...prev,
                            error: error instanceof Error ? error.message : 'Failed to delete profile'
                        })
                }["useBrandProfiles.useCallback[deleteProfile]"]);
                throw error;
            }
        }
    }["useBrandProfiles.useCallback[deleteProfile]"], [
        userId
    ]);
    // Set current profile
    const setCurrentProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBrandProfiles.useCallback[setCurrentProfile]": (profile)=>{
            console.log('🎯 setCurrentProfile called with:', profile?.businessName || 'null');
            setState({
                "useBrandProfiles.useCallback[setCurrentProfile]": (prev)=>{
                    console.log('📊 Previous current profile:', prev.currentProfile?.businessName || 'none');
                    return {
                        ...prev,
                        currentProfile: profile
                    };
                }
            }["useBrandProfiles.useCallback[setCurrentProfile]"]);
        }
    }["useBrandProfiles.useCallback[setCurrentProfile]"], []);
    // Get profile by ID
    const getProfileById = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useBrandProfiles.useCallback[getProfileById]": async (profileId)=>{
            try {
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].getBrandProfileById(profileId);
            } catch (error) {
                console.error('Failed to get profile by ID:', error);
                return null;
            }
        }
    }["useBrandProfiles.useCallback[getProfileById]"], []);
    // Load profiles when userId changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBrandProfiles.useEffect": ()=>{
            loadProfiles();
        }
    }["useBrandProfiles.useEffect"], [
        loadProfiles
    ]);
    // Set up real-time listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBrandProfiles.useEffect": ()=>{
            if (!userId) return;
            const unsubscribe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].onUserDocumentsChange(userId, {
                "useBrandProfiles.useEffect.unsubscribe": (profiles)=>{
                    console.log('🔄 Real-time profiles update received:', profiles.length, 'profiles');
                    setState({
                        "useBrandProfiles.useEffect.unsubscribe": (prev)=>{
                            // Preserve the current profile if it still exists in the updated profiles
                            let preservedCurrentProfile = prev.currentProfile;
                            if (prev.currentProfile) {
                                // Check if current profile still exists in the updated list
                                const stillExists = profiles.find({
                                    "useBrandProfiles.useEffect.unsubscribe.stillExists": (p)=>p.id === prev.currentProfile?.id
                                }["useBrandProfiles.useEffect.unsubscribe.stillExists"]);
                                if (!stillExists) {
                                    console.log('⚠️ Current profile no longer exists, clearing selection');
                                    preservedCurrentProfile = null;
                                } else {
                                    // Update with the latest version of the current profile
                                    const updatedProfile = profiles.find({
                                        "useBrandProfiles.useEffect.unsubscribe.updatedProfile": (p)=>p.id === prev.currentProfile?.id
                                    }["useBrandProfiles.useEffect.unsubscribe.updatedProfile"]);
                                    if (updatedProfile) {
                                        console.log('✅ Current profile updated with latest data:', updatedProfile.businessName);
                                        preservedCurrentProfile = updatedProfile;
                                    }
                                }
                            }
                            // Only auto-select first profile if there's no current profile at all AND this is the initial load
                            const finalCurrentProfile = preservedCurrentProfile || (!prev.currentProfile && profiles.length > 0 ? profiles[0] : null);
                            if (finalCurrentProfile && !prev.currentProfile) {
                                console.log('🎯 Auto-selecting first profile on initial load:', finalCurrentProfile.businessName);
                            }
                            return {
                                ...prev,
                                profiles,
                                currentProfile: finalCurrentProfile
                            };
                        }
                    }["useBrandProfiles.useEffect.unsubscribe"]);
                }
            }["useBrandProfiles.useEffect.unsubscribe"], {
                orderBy: 'updatedAt',
                orderDirection: 'desc'
            });
            return unsubscribe;
        }
    }["useBrandProfiles.useEffect"], [
        userId
    ]);
    return {
        ...state,
        saveProfile,
        updateProfile,
        deleteProfile,
        setCurrentProfile,
        getProfileById,
        reload: loadProfiles
    };
}
_s(useBrandProfiles, "lfmhJw6Jt36v4r4UAd3vU9/l6t4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserId"]
    ];
});
function useCurrentBrandProfile() {
    _s1();
    const { currentProfile, loading, error } = useBrandProfiles();
    return {
        profile: currentProfile,
        loading,
        error
    };
}
_s1(useCurrentBrandProfile, "rBKfgreml90gw2rhzAjEPJiwfGk=", false, function() {
    return [
        useBrandProfiles
    ];
});
function useHasCompleteBrandProfile() {
    _s2();
    const { currentProfile, loading } = useBrandProfiles();
    if (loading || !currentProfile) return false;
    // Check if profile has required fields
    const requiredFields = [
        'businessName',
        'businessType',
        'location',
        'description',
        'services'
    ];
    return requiredFields.every((field)=>{
        const value = currentProfile[field];
        return value && (typeof value === 'string' ? value.trim().length > 0 : Array.isArray(value) ? value.length > 0 : true);
    });
}
_s2(useHasCompleteBrandProfile, "7EgxA7LzCNaOEDtgL7AHe70K6wg=", false, function() {
    return [
        useBrandProfiles
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-generated-posts.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Hook for managing generated posts with Firestore
__turbopack_context__.s({
    "useGeneratedPosts": (()=>useGeneratedPosts),
    "useGeneratedPostsForBrand": (()=>useGeneratedPostsForBrand),
    "usePostStatistics": (()=>usePostStatistics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/services/generated-post-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$brand$2d$profiles$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-brand-profiles.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
;
function useGeneratedPosts(limit = 10) {
    _s();
    const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserId"])();
    const { profile: currentProfile } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$brand$2d$profiles$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentBrandProfile"])();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        posts: [],
        loading: true,
        error: null,
        saving: false
    });
    // Load generated posts
    const loadPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[loadPosts]": async ()=>{
            if (!userId) {
                setState({
                    "useGeneratedPosts.useCallback[loadPosts]": (prev)=>({
                            ...prev,
                            loading: false,
                            posts: []
                        })
                }["useGeneratedPosts.useCallback[loadPosts]"]);
                return;
            }
            try {
                setState({
                    "useGeneratedPosts.useCallback[loadPosts]": (prev)=>({
                            ...prev,
                            loading: true,
                            error: null
                        })
                }["useGeneratedPosts.useCallback[loadPosts]"]);
                const posts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getUserGeneratedPosts(userId, {
                    limit
                });
                setState({
                    "useGeneratedPosts.useCallback[loadPosts]": (prev)=>({
                            ...prev,
                            posts,
                            loading: false
                        })
                }["useGeneratedPosts.useCallback[loadPosts]"]);
            } catch (error) {
                setState({
                    "useGeneratedPosts.useCallback[loadPosts]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: error instanceof Error ? error.message : 'Failed to load posts'
                        })
                }["useGeneratedPosts.useCallback[loadPosts]"]);
            }
        }
    }["useGeneratedPosts.useCallback[loadPosts]"], [
        userId,
        limit
    ]);
    // Save generated post
    const savePost = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[savePost]": async (post)=>{
            if (!userId || !currentProfile) {
                throw new Error('User must be authenticated and have a brand profile to save posts');
            }
            try {
                setState({
                    "useGeneratedPosts.useCallback[savePost]": (prev)=>({
                            ...prev,
                            saving: true,
                            error: null
                        })
                }["useGeneratedPosts.useCallback[savePost]"]);
                const postId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].saveGeneratedPost(post, userId, currentProfile.id);
                // Add to local state optimistically
                setState({
                    "useGeneratedPosts.useCallback[savePost]": (prev)=>({
                            ...prev,
                            posts: [
                                {
                                    ...post,
                                    id: postId
                                },
                                ...prev.posts
                            ].slice(0, limit),
                            saving: false
                        })
                }["useGeneratedPosts.useCallback[savePost]"]);
                return postId;
            } catch (error) {
                setState({
                    "useGeneratedPosts.useCallback[savePost]": (prev)=>({
                            ...prev,
                            saving: false,
                            error: error instanceof Error ? error.message : 'Failed to save post'
                        })
                }["useGeneratedPosts.useCallback[savePost]"]);
                throw error;
            }
        }
    }["useGeneratedPosts.useCallback[savePost]"], [
        userId,
        currentProfile,
        limit
    ]);
    // Update post analytics
    const updatePostAnalytics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[updatePostAnalytics]": async (postId, analytics)=>{
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].updatePostAnalytics(postId, analytics);
                // Update local state
                setState({
                    "useGeneratedPosts.useCallback[updatePostAnalytics]": (prev)=>({
                            ...prev,
                            posts: prev.posts.map({
                                "useGeneratedPosts.useCallback[updatePostAnalytics]": (post)=>post.id === postId ? {
                                        ...post,
                                        ...analytics
                                    } : post
                            }["useGeneratedPosts.useCallback[updatePostAnalytics]"])
                        })
                }["useGeneratedPosts.useCallback[updatePostAnalytics]"]);
            } catch (error) {
                console.error('Failed to update post analytics:', error);
                throw error;
            }
        }
    }["useGeneratedPosts.useCallback[updatePostAnalytics]"], []);
    // Update post status
    const updatePostStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[updatePostStatus]": async (postId, status)=>{
            try {
                const firestoreStatus = status === 'posted' ? 'published' : 'draft';
                const publishedAt = status === 'posted' ? new Date() : undefined;
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].updatePostStatus(postId, firestoreStatus, undefined, publishedAt);
                // Update local state
                setState({
                    "useGeneratedPosts.useCallback[updatePostStatus]": (prev)=>({
                            ...prev,
                            posts: prev.posts.map({
                                "useGeneratedPosts.useCallback[updatePostStatus]": (post)=>post.id === postId ? {
                                        ...post,
                                        status
                                    } : post
                            }["useGeneratedPosts.useCallback[updatePostStatus]"])
                        })
                }["useGeneratedPosts.useCallback[updatePostStatus]"]);
            } catch (error) {
                console.error('Failed to update post status:', error);
                throw error;
            }
        }
    }["useGeneratedPosts.useCallback[updatePostStatus]"], []);
    // Delete post
    const deletePost = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[deletePost]": async (postId)=>{
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].delete(postId);
                // Update local state
                setState({
                    "useGeneratedPosts.useCallback[deletePost]": (prev)=>({
                            ...prev,
                            posts: prev.posts.filter({
                                "useGeneratedPosts.useCallback[deletePost]": (post)=>post.id !== postId
                            }["useGeneratedPosts.useCallback[deletePost]"])
                        })
                }["useGeneratedPosts.useCallback[deletePost]"]);
            } catch (error) {
                console.error('Failed to delete post:', error);
                throw error;
            }
        }
    }["useGeneratedPosts.useCallback[deletePost]"], []);
    // Get posts by platform
    const getPostsByPlatform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[getPostsByPlatform]": async (platform)=>{
            if (!userId) return [];
            try {
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getUserGeneratedPosts(userId, {
                    platform,
                    limit
                });
            } catch (error) {
                console.error('Failed to get posts by platform:', error);
                return [];
            }
        }
    }["useGeneratedPosts.useCallback[getPostsByPlatform]"], [
        userId,
        limit
    ]);
    // Get posts by status
    const getPostsByStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPosts.useCallback[getPostsByStatus]": async (status)=>{
            if (!userId) return [];
            try {
                const firestoreStatus = status === 'posted' ? 'published' : 'draft';
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getPostsByStatus(userId, firestoreStatus);
            } catch (error) {
                console.error('Failed to get posts by status:', error);
                return [];
            }
        }
    }["useGeneratedPosts.useCallback[getPostsByStatus]"], [
        userId
    ]);
    // Load posts when dependencies change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useGeneratedPosts.useEffect": ()=>{
            loadPosts();
        }
    }["useGeneratedPosts.useEffect"], [
        loadPosts
    ]);
    // Set up real-time listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useGeneratedPosts.useEffect": ()=>{
            if (!userId) return;
            const unsubscribe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].onUserDocumentsChange(userId, {
                "useGeneratedPosts.useEffect.unsubscribe": (posts)=>{
                    setState({
                        "useGeneratedPosts.useEffect.unsubscribe": (prev)=>({
                                ...prev,
                                posts: posts.slice(0, limit)
                            })
                    }["useGeneratedPosts.useEffect.unsubscribe"]);
                }
            }["useGeneratedPosts.useEffect.unsubscribe"], {
                limit,
                orderBy: 'createdAt',
                orderDirection: 'desc'
            });
            return unsubscribe;
        }
    }["useGeneratedPosts.useEffect"], [
        userId,
        limit
    ]);
    return {
        ...state,
        savePost,
        updatePostAnalytics,
        updatePostStatus,
        deletePost,
        getPostsByPlatform,
        getPostsByStatus,
        reload: loadPosts
    };
}
_s(useGeneratedPosts, "rPWMh6BMBHOLJELxY7VzJ3//Pu4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserId"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$brand$2d$profiles$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentBrandProfile"]
    ];
});
function useGeneratedPostsForBrand(brandProfileId, limit = 10) {
    _s1();
    const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserId"])();
    const [posts, setPosts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const loadPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGeneratedPostsForBrand.useCallback[loadPosts]": async ()=>{
            if (!userId || !brandProfileId) {
                setPosts([]);
                setLoading(false);
                return;
            }
            try {
                setLoading(true);
                setError(null);
                const brandPosts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getRecentPostsForBrand(userId, brandProfileId, limit);
                setPosts(brandPosts);
                setLoading(false);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Failed to load posts');
                setLoading(false);
            }
        }
    }["useGeneratedPostsForBrand.useCallback[loadPosts]"], [
        userId,
        brandProfileId,
        limit
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useGeneratedPostsForBrand.useEffect": ()=>{
            loadPosts();
        }
    }["useGeneratedPostsForBrand.useEffect"], [
        loadPosts
    ]);
    return {
        posts,
        loading,
        error,
        reload: loadPosts
    };
}
_s1(useGeneratedPostsForBrand, "cNF57F16W+Jn4ubabJebAtfVLqM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserId"]
    ];
});
function usePostStatistics() {
    _s2();
    const { posts } = useGeneratedPosts(100); // Get more posts for statistics
    const statistics = {
        total: posts.length,
        byPlatform: posts.reduce((acc, post)=>{
            acc[post.platform] = (acc[post.platform] || 0) + 1;
            return acc;
        }, {}),
        byStatus: posts.reduce((acc, post)=>{
            acc[post.status] = (acc[post.status] || 0) + 1;
            return acc;
        }, {}),
        averageQuality: posts.length > 0 ? posts.reduce((sum, post)=>sum + (post.qualityScore || 0), 0) / posts.length : 0,
        averageEngagement: posts.length > 0 ? posts.reduce((sum, post)=>sum + (post.engagementPrediction || 0), 0) / posts.length : 0
    };
    return statistics;
}
_s2(usePostStatistics, "Mqu0HXqqY3qJNS7WiSux4MxBHc4=", false, function() {
    return [
        useGeneratedPosts
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/switch.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Switch": (()=>Switch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-switch/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Switch = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input", className),
        ...props,
        ref: ref,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Thumb"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")
        }, void 0, false, {
            fileName: "[project]/src/components/ui/switch.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/switch.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Switch;
Switch.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Switch$React.forwardRef");
__turbopack_context__.k.register(_c1, "Switch");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/dashboard/content-calendar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/dashboard/content-calendar.tsx
__turbopack_context__.s({
    "ContentCalendar": (()=>ContentCalendar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/facebook.js [app-client] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/instagram.js [app-client] (ecmascript) <export default as Instagram>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-client] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/twitter.js [app-client] (ecmascript) <export default as Twitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-client] (ecmascript) <export default as Palette>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript) <export default as Sparkles>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$post$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/post-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ab8590__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:ab8590 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$a7eaa4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:a7eaa4 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-generated-posts.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/switch.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
const platforms = [
    {
        name: 'Instagram',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"]
    },
    {
        name: 'Facebook',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"]
    },
    {
        name: 'Twitter',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__["Twitter"]
    },
    {
        name: 'LinkedIn',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"]
    }
];
function ContentCalendar({ brandProfile, posts, onPostGenerated, onPostUpdated }) {
    _s();
    const [isGenerating, setIsGenerating] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFirebaseAuth"])();
    const { savePost, saving } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGeneratedPosts"])();
    // Brand consistency preferences - default to consistent if design examples exist
    const [brandConsistency, setBrandConsistency] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState({
        strictConsistency: !!(brandProfile.designExamples && brandProfile.designExamples.length > 0),
        followBrandColors: true
    });
    // Revo model selection
    const [selectedRevoModel, setSelectedRevoModel] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState('revo-1.5');
    // Artifact selection for content generation
    const [selectedArtifacts, setSelectedArtifacts] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState([]);
    // Include people in designs toggle
    const [includePeopleInDesigns, setIncludePeopleInDesigns] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(true);
    // Use local language toggle
    const [useLocalLanguage, setUseLocalLanguage] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(false);
    // Save preferences to localStorage
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "ContentCalendar.useEffect": ()=>{
            const savedPreferences = localStorage.getItem('brandConsistencyPreferences');
            if (savedPreferences) {
                setBrandConsistency(JSON.parse(savedPreferences));
            }
            const savedRevoModel = localStorage.getItem('selectedRevoModel');
            if (savedRevoModel) {
                setSelectedRevoModel(savedRevoModel);
            }
            const savedIncludePeople = localStorage.getItem('includePeopleInDesigns');
            if (savedIncludePeople !== null) {
                setIncludePeopleInDesigns(JSON.parse(savedIncludePeople));
            }
            const savedUseLocalLanguage = localStorage.getItem('useLocalLanguage');
            if (savedUseLocalLanguage !== null) {
                setUseLocalLanguage(JSON.parse(savedUseLocalLanguage));
            }
        }
    }["ContentCalendar.useEffect"], []);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "ContentCalendar.useEffect": ()=>{
            localStorage.setItem('brandConsistencyPreferences', JSON.stringify(brandConsistency));
        }
    }["ContentCalendar.useEffect"], [
        brandConsistency
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "ContentCalendar.useEffect": ()=>{
            localStorage.setItem('selectedRevoModel', selectedRevoModel);
        }
    }["ContentCalendar.useEffect"], [
        selectedRevoModel
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "ContentCalendar.useEffect": ()=>{
            localStorage.setItem('includePeopleInDesigns', JSON.stringify(includePeopleInDesigns));
        }
    }["ContentCalendar.useEffect"], [
        includePeopleInDesigns
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "ContentCalendar.useEffect": ()=>{
            localStorage.setItem('useLocalLanguage', JSON.stringify(useLocalLanguage));
        }
    }["ContentCalendar.useEffect"], [
        useLocalLanguage
    ]);
    const handleGenerateClick = async (platform)=>{
        setIsGenerating(platform);
        try {
            console.log('🚀 Starting content generation for platform:', platform);
            console.log('👤 User authenticated:', !!user);
            console.log('🏢 Brand profile:', brandProfile?.businessName);
            let newPost;
            // Check if artifacts are enabled (simple toggle approach)
            const artifactsEnabled = selectedArtifacts.length > 0;
            const useEnhancedGeneration = artifactsEnabled || selectedRevoModel === 'revo-1.5' || selectedRevoModel === 'revo-2.0';
            if (selectedRevoModel === 'revo-2.0') {
                console.log(`🚀 Using Revo 2.0 (Gemini 2.5 Flash Image) generation via server action`);
                // Use server action to avoid client-side imports
                const response = await fetch('/api/generate-revo-2.0', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        businessType: brandProfile.businessType || 'Business',
                        platform: platform.toLowerCase(),
                        visualStyle: brandProfile.visualStyle || 'modern',
                        imageText: `${brandProfile.businessName || brandProfile.businessType} - Premium Content`,
                        brandProfile,
                        aspectRatio: '1:1',
                        includePeopleInDesigns,
                        useLocalLanguage
                    })
                });
                if (!response.ok) {
                    throw new Error(`Revo 2.0 generation failed: ${response.statusText}`);
                }
                const revo20Result = await response.json();
                newPost = {
                    id: `revo-2.0-${Date.now()}`,
                    content: revo20Result.caption || `🚀 Generated with Revo 2.0 (Gemini 2.5 Flash Image)\n\n${brandProfile.businessName || brandProfile.businessType} - Premium Content`,
                    hashtags: revo20Result.hashtags || [
                        '#NextGen',
                        '#AI',
                        '#Innovation'
                    ],
                    imageUrl: revo20Result.imageUrl,
                    platform: platform,
                    date: new Date().toISOString(),
                    analytics: {
                        views: 0,
                        likes: 0,
                        shares: 0,
                        comments: 0,
                        engagementPrediction: 85,
                        brandAlignmentScore: 95,
                        qualityScore: revo20Result.qualityScore || 10
                    },
                    metadata: {
                        aiModel: revo20Result.model || 'Revo 2.0',
                        generationPrompt: 'Revo 2.0 Native Generation',
                        processingTime: revo20Result.processingTime || 0,
                        enhancementsApplied: revo20Result.enhancementsApplied || []
                    }
                };
            } else if (useEnhancedGeneration) {
                console.log(`✨ Using enhanced generation with ${selectedRevoModel} model`);
                // Use artifact-enhanced generation - will automatically use active artifacts from artifacts page
                newPost = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$a7eaa4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentWithArtifactsAction"])(brandProfile, platform, brandConsistency, [], selectedRevoModel === 'revo-1.5', includePeopleInDesigns, useLocalLanguage);
            } else {
                console.log(`📝 Using standard content generation with ${selectedRevoModel} model`);
                // Use standard content generation
                newPost = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ab8590__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentAction"])(brandProfile, platform, brandConsistency);
            }
            console.log('📄 Generated post:', newPost.content.substring(0, 100) + '...');
            // Save to Firestore database first
            try {
                console.log('💾 Saving post to Firestore database...');
                const postId = await savePost(newPost);
                console.log('✅ Post saved to Firestore with ID:', postId);
                // Update the post with the Firestore ID
                const savedPost = {
                    ...newPost,
                    id: postId
                };
                onPostGenerated(savedPost);
            } catch (saveError) {
                console.error('❌ Failed to save to Firestore, falling back to localStorage:', saveError);
                // Fallback to localStorage if Firestore fails
                onPostGenerated(newPost);
            }
            // Dynamic toast message based on generation type
            let title = "Content Generated!";
            let description = `A new ${platform} post has been saved to your database.`;
            if (selectedArtifacts.length > 0) {
                title = "Content Generated with References! 📎";
                description = `A new ${platform} post using ${selectedArtifacts.length} reference${selectedArtifacts.length !== 1 ? 's' : ''} has been saved.`;
            } else if (selectedRevoModel === 'revo-1.5') {
                title = "Enhanced Content Generated! ✨";
                description = `A new enhanced ${platform} post with ${selectedRevoModel} has been saved.`;
            } else {
                title = "Content Generated! 🚀";
                description = `A new ${platform} post with ${selectedRevoModel} has been saved.`;
            }
            toast({
                title,
                description
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Generation Failed",
                description: error.message
            });
        } finally{
            setIsGenerating(null);
        }
    };
    // Ensure this component is always full-bleed inside the app shell and does not cause horizontal overflow.
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full max-w-[100vw] box-border overflow-x-hidden",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full px-6 py-10 lg:py-16 lg:px-12",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full box-border space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-50 border border-gray-200 rounded-lg p-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                className: "h-4 w-4 text-blue-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 235,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-sm",
                                                children: "Brand Consistency"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 236,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 234,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"], {
                                                        className: "h-3 w-3 text-gray-500"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 240,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "Strict"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 241,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: brandConsistency.strictConsistency,
                                                        onCheckedChange: (checked)=>setBrandConsistency((prev)=>({
                                                                    ...prev,
                                                                    strictConsistency: checked
                                                                }))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 242,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 239,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"], {
                                                        className: "h-3 w-3 text-gray-500"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 250,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "Colors"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 251,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: brandConsistency.followBrandColors,
                                                        onCheckedChange: (checked)=>setBrandConsistency((prev)=>({
                                                                    ...prev,
                                                                    followBrandColors: checked
                                                                }))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 252,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 249,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "👥 People"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 260,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: includePeopleInDesigns,
                                                        onCheckedChange: setIncludePeopleInDesigns
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 261,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 259,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "🌍 Local"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 267,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: useLocalLanguage,
                                                        onCheckedChange: setUseLocalLanguage
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 268,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 266,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {
                                                orientation: "vertical",
                                                className: "h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 273,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "AI Model:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                        value: selectedRevoModel,
                                                        onChange: (e)=>setSelectedRevoModel(e.target.value),
                                                        className: "appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                value: "revo-1.0",
                                                                children: "Revo 1.0"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                                lineNumber: 281,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                value: "revo-1.5",
                                                                children: "Revo 1.5"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                                lineNumber: 282,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                value: "revo-2.0",
                                                                children: "Revo 2.0"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                                lineNumber: 283,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 276,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 274,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 238,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 233,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-500 mt-2",
                                children: selectedRevoModel === 'revo-2.0' ? `🚀 ${selectedRevoModel}: Next-Gen AI with native image generation, character consistency & intelligent editing` : selectedRevoModel === 'revo-1.5' ? `✨ ${selectedRevoModel}: Enhanced AI with professional design principles + ${brandConsistency.strictConsistency ? "strict consistency" : "brand colors"}` : selectedRevoModel === 'revo-1.0' ? `🚀 ${selectedRevoModel}: Standard reliable AI + ${brandConsistency.strictConsistency ? "strict consistency" : "brand colors"}` : `🌟 ${selectedRevoModel}: Next-generation AI (coming soon)`
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 288,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 232,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                className: "p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                        className: "text-sm font-medium",
                                                        children: "Use Artifacts"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 306,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-xs text-muted-foreground",
                                                        children: "Enable to use your uploaded reference materials and exact-use content"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 307,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 305,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: selectedArtifacts.length > 0,
                                                        onCheckedChange: (checked)=>{
                                                            if (checked) {
                                                                // Enable artifacts - this will use active artifacts from the artifacts page
                                                                setSelectedArtifacts([
                                                                    'active'
                                                                ]);
                                                            } else {
                                                                // Disable artifacts
                                                                setSelectedArtifacts([]);
                                                            }
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 312,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>window.open('/artifacts', '_blank'),
                                                        className: "text-xs",
                                                        children: "Manage"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 324,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 311,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 304,
                                        columnNumber: 17
                                    }, this),
                                    selectedArtifacts.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-blue-700",
                                            children: "✓ Artifacts enabled - Content will use your reference materials and exact-use items from the Artifacts page"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                            lineNumber: 336,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 335,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 303,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                            lineNumber: 302,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 301,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold tracking-tight font-headline",
                                        children: "Content Calendar"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 349,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-muted-foreground",
                                        children: "Here's your generated content. Click a post to edit or regenerate."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 350,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 348,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            disabled: !!isGenerating,
                                            children: isGenerating ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "mr-2 h-4 w-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 359,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Generating for ",
                                                    isGenerating,
                                                    "..."
                                                ]
                                            }, void 0, true) : "✨ Generate New Post"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                            lineNumber: 356,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 355,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        children: platforms.map((p)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>handleGenerateClick(p.name),
                                                disabled: !!isGenerating,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(p.icon, {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 370,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: p.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 371,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, p.name, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 369,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 367,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 354,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 347,
                        columnNumber: 11
                    }, this),
                    posts.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full max-w-none",
                        children: posts.map((post)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$post$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PostCard"], {
                                post: post,
                                brandProfile: brandProfile,
                                onPostUpdated: onPostUpdated
                            }, post.id, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 381,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 379,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/30 bg-card p-12 text-center w-full",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold",
                                children: "Your calendar is empty"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 391,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground mt-2",
                                children: 'Click the "Generate" button to create your first social media post!'
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 392,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 390,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                lineNumber: 230,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
            lineNumber: 229,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
        lineNumber: 228,
        columnNumber: 5
    }, this);
}
_s(ContentCalendar, "NkQnmMORF4mkC/4vuI/6N6wqGzA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFirebaseAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGeneratedPosts"]
    ];
});
_c = ContentCalendar;
var _c;
__turbopack_context__.k.register(_c, "ContentCalendar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/unified-brand-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BrandContent": (()=>BrandContent),
    "BrandSwitchingStatus": (()=>BrandSwitchingStatus),
    "ConditionalBrandContent": (()=>ConditionalBrandContent),
    "UnifiedBrandLayout": (()=>UnifiedBrandLayout),
    "useBrandAware": (()=>useBrandAware),
    "useBrandScopedData": (()=>useBrandScopedData),
    "withBrandAware": (()=>withBrandAware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
'use client';
;
;
// Inner component that uses the unified brand context
function UnifiedBrandLayoutContent({ children }) {
    _s();
    const { currentBrand, loading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Listen for brand changes and log them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "UnifiedBrandLayoutContent.useBrandChangeListener": (brand)=>{
            console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');
            // Mark as initialized once we have a brand or finished loading
            if (!isInitialized && (!loading || brand)) {
                setIsInitialized(true);
            }
        }
    }["UnifiedBrandLayoutContent.useBrandChangeListener"]);
    // Show loading state while initializing
    if (!isInitialized && loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 31,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Loading brand profiles..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 32,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    }
    // Show error state if there's an error
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-600 text-2xl",
                            children: "⚠️"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                            lineNumber: 44,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-red-900 mb-2",
                        children: "Error Loading Brands"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",
                        children: "Retry"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 42,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "unified-brand-layout",
        children: [
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: "🔥 Unified Brand System"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "Brand: ",
                            currentBrand?.businessName || currentBrand?.name || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "ID: ",
                            currentBrand?.id || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
_s(UnifiedBrandLayoutContent, "fCqT7M8oQD2mKm1cC+OOL+jMWzQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c = UnifiedBrandLayoutContent;
function UnifiedBrandLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnifiedBrandProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(UnifiedBrandLayoutContent, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 79,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
_c1 = UnifiedBrandLayout;
function useBrandAware() {
    _s1();
    const { currentBrand, selectBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    return {
        currentBrand,
        selectBrand,
        loading,
        isReady: !loading && currentBrand !== null,
        brandId: currentBrand?.id || null,
        brandName: currentBrand?.businessName || currentBrand?.name || null
    };
}
_s1(useBrandAware, "w47bVP/loHCLTWfG0SMBg7xbNQg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
function withBrandAware(Component) {
    var _s = __turbopack_context__.k.signature();
    return _s(function BrandAwareComponent(props) {
        _s();
        const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props,
            brand: currentBrand
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 107,
            columnNumber: 12
        }, this);
    }, "MN56VmfrkrwEAMHnmgJmh3z4nas=", false, function() {
        return [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
        ];
    });
}
function BrandContent({ children, fallback }) {
    _s2();
    const { currentBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center p-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 123,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 122,
            columnNumber: 7
        }, this);
    }
    if (!currentBrand) {
        return fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-gray-400 text-2xl",
                        children: "🏢"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 131,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-lg font-semibold text-gray-900 mb-2",
                    children: "No Brand Selected"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Please select a brand to continue."
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 135,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 130,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children(currentBrand)
    }, void 0, false);
}
_s2(BrandContent, "SDXm9VlEhbnzCURAKLqu5+JG9ko=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
_c2 = BrandContent;
function ConditionalBrandContent({ brandId, brandName, children, fallback }) {
    _s3();
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const shouldRender = (!brandId || currentBrand?.id === brandId) && (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);
    if (shouldRender) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    return fallback || null;
}
_s3(ConditionalBrandContent, "MN56VmfrkrwEAMHnmgJmh3z4nas=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
_c3 = ConditionalBrandContent;
function useBrandScopedData(feature, defaultValue, loader) {
    _s4();
    const { currentBrand, getBrandStorage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBrandScopedData.useEffect": ()=>{
            if (!currentBrand?.id) {
                setData(defaultValue);
                return;
            }
            const storage = getBrandStorage(feature);
            if (!storage) {
                setData(defaultValue);
                return;
            }
            setLoading(true);
            try {
                if (loader) {
                    // Use custom loader
                    const result = loader(currentBrand.id);
                    if (result instanceof Promise) {
                        result.then({
                            "useBrandScopedData.useEffect": (loadedData)=>{
                                setData(loadedData);
                                setLoading(false);
                            }
                        }["useBrandScopedData.useEffect"]).catch({
                            "useBrandScopedData.useEffect": (error)=>{
                                console.error(`Failed to load ${feature} data:`, error);
                                setData(defaultValue);
                                setLoading(false);
                            }
                        }["useBrandScopedData.useEffect"]);
                    } else {
                        setData(result);
                        setLoading(false);
                    }
                } else {
                    // Use storage
                    const storedData = storage.getItem();
                    setData(storedData || defaultValue);
                    setLoading(false);
                }
            } catch (error) {
                console.error(`Failed to load ${feature} data:`, error);
                setData(defaultValue);
                setLoading(false);
            }
        }
    }["useBrandScopedData.useEffect"], [
        currentBrand?.id,
        feature,
        defaultValue,
        loader,
        getBrandStorage
    ]);
    // Save data function
    const saveData = (newData)=>{
        setData(newData);
        if (currentBrand?.id) {
            const storage = getBrandStorage(feature);
            if (storage) {
                storage.setItem(newData);
            }
        }
    };
    return [
        data,
        saveData,
        loading
    ];
}
_s4(useBrandScopedData, "ZdiOY6elsL57Z0m55fnPycGGj2E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
function BrandSwitchingStatus() {
    _s5();
    const { loading, currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [switching, setSwitching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "BrandSwitchingStatus.useBrandChangeListener": (brand)=>{
            setSwitching(true);
            const timer = setTimeout({
                "BrandSwitchingStatus.useBrandChangeListener.timer": ()=>setSwitching(false)
            }["BrandSwitchingStatus.useBrandChangeListener.timer"], 1000);
            return ({
                "BrandSwitchingStatus.useBrandChangeListener": ()=>clearTimeout(timer)
            })["BrandSwitchingStatus.useBrandChangeListener"];
        }
    }["BrandSwitchingStatus.useBrandChangeListener"]);
    if (!switching && !loading) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm",
                    children: switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 257,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 255,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 254,
        columnNumber: 5
    }, this);
}
_s5(BrandSwitchingStatus, "sajUo/soq/Xgt0zh0dhNkAG60WA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c4 = BrandSwitchingStatus;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "UnifiedBrandLayoutContent");
__turbopack_context__.k.register(_c1, "UnifiedBrandLayout");
__turbopack_context__.k.register(_c2, "BrandContent");
__turbopack_context__.k.register(_c3, "ConditionalBrandContent");
__turbopack_context__.k.register(_c4, "BrandSwitchingStatus");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils/enable-firebase-storage.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Enable Firebase Storage Utility
 * Helper to re-enable Firebase Storage after rules are deployed
 */ __turbopack_context__.s({
    "CODE_TO_UNCOMMENT": (()=>CODE_TO_UNCOMMENT),
    "FIREBASE_STORAGE_INSTRUCTIONS": (()=>FIREBASE_STORAGE_INSTRUCTIONS),
    "enableFirebaseStorage": (()=>enableFirebaseStorage),
    "testFirebaseStorageRules": (()=>testFirebaseStorageRules)
});
const FIREBASE_STORAGE_INSTRUCTIONS = `
🔥 FIREBASE STORAGE RULES DEPLOYMENT INSTRUCTIONS

1. Go to Firebase Console: https://console.firebase.google.com/
2. Select your project: localbuzz-mpkuv
3. Go to Storage → Rules
4. Replace the current rules with:

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload and manage their own generated content
    match /generated-content/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can upload and manage their own artifacts
    match /artifacts/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can upload and manage their own brand assets
    match /brand-assets/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}

5. Click "Publish"
6. Wait 2-3 minutes for rules to propagate
7. Come back and run: enableFirebaseStorage()
`;
const CODE_TO_UNCOMMENT = `
After deploying Firebase Storage rules, go to:
src/app/quick-content/page.tsx

Find this section around line 209:
// TEMPORARY: Skip Firebase Storage upload until rules are deployed

Replace the entire processPostImages function with the commented code below it.

Or simply run: enableFirebaseStorage() in the browser console.
`;
function enableFirebaseStorage() {
    console.log('🔥 Firebase Storage Enable Instructions:');
    console.log(FIREBASE_STORAGE_INSTRUCTIONS);
    console.log('📝 Code Update Instructions:');
    console.log(CODE_TO_UNCOMMENT);
    return {
        instructions: FIREBASE_STORAGE_INSTRUCTIONS,
        codeInstructions: CODE_TO_UNCOMMENT,
        status: 'Instructions displayed - manual code update required'
    };
}
async function testFirebaseStorageRules() {
    try {
        // This would need to be implemented with actual Firebase Storage test
        console.log('🧪 Testing Firebase Storage rules...');
        console.log('⚠️ Manual test required - try generating content after deploying rules');
        return {
            success: false,
            message: 'Manual test required - generate content to test'
        };
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
// Make functions available globally for easy access
if ("TURBOPACK compile-time truthy", 1) {
    window.enableFirebaseStorage = enableFirebaseStorage;
    window.testFirebaseStorageRules = testFirebaseStorageRules;
    // Auto-display instructions on load
    console.log('🔥 Firebase Storage is currently disabled.');
    console.log('📋 To enable permanent image storage, run: enableFirebaseStorage()');
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/quick-content/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/app/content-calendar/page.tsx
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$content$2d$calendar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/content-calendar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$close$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftClose$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/panel-left-close.js [app-client] (ecmascript) <export default as PanelLeftClose>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftOpen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/panel-left-open.js [app-client] (ecmascript) <export default as PanelLeftOpen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/unified-brand-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/brand-scoped-storage.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-generated-posts.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$enable$2d$firebase$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/enable-firebase-storage.ts [app-client] (ecmascript)"); // Load Firebase Storage utilities
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// No limit on posts - store all generated content
// Brand-scoped storage cleanup utility
const cleanupBrandScopedStorage = (brandStorage)=>{
    try {
        const posts = brandStorage.getItem() || [];
        // Fix invalid dates in existing posts
        const fixedPosts = posts.map((post)=>{
            if (!post.date || isNaN(new Date(post.date).getTime())) {
                return {
                    ...post,
                    date: new Date().toISOString()
                };
            }
            return post;
        });
        if (fixedPosts.length > 5) {
            // Keep only the 5 most recent posts
            const recentPosts = fixedPosts.slice(0, 5);
            brandStorage.setItem(recentPosts);
            return recentPosts;
        } else {
            // Save the fixed posts back
            brandStorage.setItem(fixedPosts);
            return fixedPosts;
        }
    } catch (error) {
        console.warn('Brand-scoped storage cleanup failed:', error);
    }
    return null;
};
function QuickContentPage() {
    _s();
    const { currentBrand, brands, loading: brandLoading, selectBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const postsStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandStorage"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_FEATURES"].QUICK_CONTENT);
    const [generatedPosts, setGeneratedPosts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const { open: sidebarOpen, toggleSidebar } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSidebar"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFirebaseAuth"])();
    const { savePost, saving } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGeneratedPosts"])();
    // Inline brand restoration function
    const forceBrandRestore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "QuickContentPage.useCallback[forceBrandRestore]": ()=>{
            try {
                // Try to restore from full brand data first
                const savedBrandData = localStorage.getItem('currentBrandData');
                if (savedBrandData) {
                    const parsedData = JSON.parse(savedBrandData);
                    console.log('🔄 Attempting to restore brand from full data:', parsedData.businessName || parsedData.name);
                    // Find matching brand in current brands list
                    const matchingBrand = brands.find({
                        "QuickContentPage.useCallback[forceBrandRestore].matchingBrand": (b)=>b.id === parsedData.id
                    }["QuickContentPage.useCallback[forceBrandRestore].matchingBrand"]);
                    if (matchingBrand) {
                        console.log('✅ Found matching brand in brands list, using fresh data');
                        selectBrand(matchingBrand);
                        return true;
                    }
                }
                // Fallback to brand ID restoration
                const savedBrandId = localStorage.getItem('selectedBrandId');
                if (savedBrandId && brands.length > 0) {
                    const savedBrand = brands.find({
                        "QuickContentPage.useCallback[forceBrandRestore].savedBrand": (b)=>b.id === savedBrandId
                    }["QuickContentPage.useCallback[forceBrandRestore].savedBrand"]);
                    if (savedBrand) {
                        console.log('🔄 Restored brand from ID:', savedBrand.businessName || savedBrand.name);
                        selectBrand(savedBrand);
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Failed to restore brand from storage:', error);
                return false;
            }
        }
    }["QuickContentPage.useCallback[forceBrandRestore]"], [
        brands,
        selectBrand
    ]);
    // Load posts when brand changes using unified brand system
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "QuickContentPage.useBrandChangeListener.useCallback": (brand)=>{
            const brandName = brand?.businessName || brand?.name || 'none';
            console.log('🔄 Quick Content: brand changed to:', brandName);
            if (!brand) {
                setGeneratedPosts([]);
                setIsLoading(false);
                return;
            }
            setIsLoading(true);
            try {
                if (postsStorage) {
                    const posts = postsStorage.getItem() || [];
                    // Check if any posts have invalid dates
                    const hasInvalidDates = posts.some({
                        "QuickContentPage.useBrandChangeListener.useCallback.hasInvalidDates": (post)=>!post.date || isNaN(new Date(post.date).getTime())
                    }["QuickContentPage.useBrandChangeListener.useCallback.hasInvalidDates"]);
                    if (hasInvalidDates) {
                        console.warn('Found posts with invalid dates, clearing brand storage...');
                        postsStorage.removeItem();
                        setGeneratedPosts([]);
                    } else {
                        setGeneratedPosts(posts);
                    }
                    console.log(`✅ Loaded ${posts.length} posts for brand ${brandName}`);
                } else {
                    setGeneratedPosts([]);
                }
            } catch (error) {
                console.error('Failed to load posts for brand:', brandName, error);
                toast({
                    variant: "destructive",
                    title: "Failed to load data",
                    description: "Could not read your posts data. It might be corrupted."
                });
            } finally{
                setIsLoading(false);
            }
        }
    }["QuickContentPage.useBrandChangeListener.useCallback"], [
        postsStorage,
        toast
    ]));
    // Enhanced brand selection logic with persistence recovery
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "QuickContentPage.useEffect": ()=>{
            console.log('🔍 Enhanced brand selection check:', {
                brandLoading,
                brandsCount: brands.length,
                currentBrand: currentBrand?.businessName || currentBrand?.name || 'null',
                postsStorageAvailable: !!postsStorage
            });
            if (!brandLoading) {
                // Add a small delay to ensure brands have time to load
                const timer = setTimeout({
                    "QuickContentPage.useEffect.timer": ()=>{
                        if (brands.length === 0) {
                            // No brands exist, redirect to brand setup
                            console.log('🔄 Quick Content: No brands found, redirecting to brand setup');
                            try {
                                router.prefetch('/brand-profile');
                            } catch  {}
                            router.push('/brand-profile');
                        } else if (brands.length > 0 && !currentBrand) {
                            // Try to restore from persistence first
                            console.log('🔧 Attempting brand restoration from persistence...');
                            const restored = forceBrandRestore();
                            if (!restored) {
                                // If restoration failed, auto-select the first brand
                                console.log('🎯 Auto-selecting first available brand:', brands[0].businessName || brands[0].name);
                                selectBrand(brands[0]);
                            }
                        }
                    }
                }["QuickContentPage.useEffect.timer"], 1000); // 1 second delay
                return ({
                    "QuickContentPage.useEffect": ()=>clearTimeout(timer)
                })["QuickContentPage.useEffect"];
            }
        }
    }["QuickContentPage.useEffect"], [
        currentBrand,
        brands.length,
        brandLoading,
        router,
        selectBrand,
        forceBrandRestore
    ]);
    // Process generated post with Firebase Storage upload and database fallback
    const processPostImages = async (post)=>{
        try {
            // Check if user is authenticated for Firebase Storage
            if (!user) {
                console.log('⚠️ User not authenticated, saving to database only');
                toast({
                    title: "Content Saved",
                    description: "Content saved to database. Sign in to save images permanently in the cloud.",
                    variant: "default"
                });
                return post; // Return original post with data URLs
            }
            console.log('🔄 Processing post images for permanent storage...');
            // TEMPORARY: Skip Firebase Storage upload until rules are deployed
            console.log('⚠️ Skipping Firebase Storage upload - rules not deployed yet');
            // Save to database with data URLs (temporary solution)
            toast({
                title: "Content Saved to Database",
                description: "Content saved successfully. Deploy Firebase Storage rules for permanent image URLs.",
                variant: "default"
            });
            return post; // Return original post with data URLs
        /* UNCOMMENT THIS AFTER DEPLOYING FIREBASE STORAGE RULES:
      try {
        // Try Firebase Storage first
        const processedPost = await processGeneratedPost(post, user.uid);

        console.log('✅ Post images processed successfully');

        // Show success message
        toast({
          title: "Images Saved to Cloud",
          description: "Images have been permanently saved to Firebase Storage.",
          variant: "default",
        });

        return processedPost;
      } catch (storageError) {
        console.warn('⚠️ Firebase Storage failed, falling back to database storage:', storageError);

        // Fallback: Save to database with data URLs (temporary)
        toast({
          title: "Content Saved to Database",
          description: "Images stored temporarily. Please update Firebase Storage rules for permanent cloud storage.",
          variant: "default",
        });

        return post; // Return original post with data URLs
      }
      */ } catch (error) {
            console.warn('⚠️ Failed to process post, using original post:', error);
            toast({
                title: "Content Saved Locally",
                description: "Content generated successfully but stored locally only.",
                variant: "default"
            });
            return post; // Return original post if all processing fails
        }
    };
    const handlePostGenerated = async (post)=>{
        console.log('📝 Processing generated post...');
        // Process images with Firebase Storage upload
        let processedPost = await processPostImages(post);
        // Add the processed post to the beginning of the array (no limit)
        const newPosts = [
            processedPost,
            ...generatedPosts
        ];
        setGeneratedPosts(newPosts);
        if (!postsStorage) {
            console.warn('No posts storage available for current brand - keeping in memory only');
            toast({
                title: "Storage Unavailable",
                description: "Post generated but couldn't be saved. Please select a brand.",
                variant: "destructive"
            });
            return;
        }
        try {
            // Save to localStorage first (immediate)
            postsStorage.setItem(newPosts);
            console.log(`💾 Saved ${newPosts.length} posts to localStorage for brand ${currentBrand?.businessName || currentBrand?.name}`);
            // Also save to Firestore database (permanent backup)
            if (user) {
                try {
                    console.log('💾 Saving post to Firestore database...');
                    const postId = await savePost(processedPost);
                    console.log('✅ Post saved to Firestore with ID:', postId);
                    // Update the post with the Firestore ID
                    const savedPost = {
                        ...processedPost,
                        id: postId
                    };
                    const updatedPosts = [
                        savedPost,
                        ...generatedPosts
                    ];
                    setGeneratedPosts(updatedPosts);
                    postsStorage.setItem(updatedPosts);
                    toast({
                        title: "Content Saved Successfully",
                        description: "Your content has been saved to both local storage and the database.",
                        variant: "default"
                    });
                } catch (firestoreError) {
                    console.error('❌ Failed to save to Firestore, but localStorage succeeded:', firestoreError);
                    toast({
                        title: "Content Saved Locally",
                        description: "Content saved locally. Database save failed but content is secure.",
                        variant: "default"
                    });
                }
            } else {
                toast({
                    title: "Content Saved Locally",
                    description: "Content saved locally. Sign in to save to database permanently.",
                    variant: "default"
                });
            }
        } catch (error) {
            console.error('Storage error in handlePostGenerated:', error);
            // Show user-friendly error message
            toast({
                title: "Storage Issue",
                description: "Post generated successfully but couldn't be saved. Storage may be full.",
                variant: "destructive"
            });
            // Keep the post in memory even if storage fails
            console.log('Post kept in memory despite storage failure');
        }
    };
    // Debug function to clear all posts for current brand
    const clearAllPosts = ()=>{
        if (!postsStorage) {
            console.warn('No posts storage available for current brand');
            return;
        }
        try {
            postsStorage.removeItem();
            setGeneratedPosts([]);
            toast({
                title: "Posts Cleared",
                description: `All stored posts have been cleared for ${currentBrand?.businessName || currentBrand?.name}.`
            });
            console.log(`🗑️ Cleared all posts for brand ${currentBrand?.businessName || currentBrand?.name}`);
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Clear Failed",
                description: "Could not clear stored posts."
            });
        }
    };
    const handlePostUpdated = async (updatedPost)=>{
        if (!postsStorage) {
            console.warn('No posts storage available for current brand');
            return;
        }
        try {
            const updatedPosts = generatedPosts.map((post)=>post.id === updatedPost.id ? updatedPost : post);
            setGeneratedPosts(updatedPosts);
            // Check storage size before saving
            const postsData = JSON.stringify(updatedPosts);
            const maxSize = 5 * 1024 * 1024; // 5MB limit
            if (postsData.length > maxSize) {
                // If too large, keep fewer posts
                const reducedPosts = updatedPosts.slice(0, Math.max(1, Math.floor(MAX_POSTS_TO_STORE / 2)));
                postsStorage.setItem(reducedPosts);
                setGeneratedPosts(reducedPosts);
                toast({
                    title: "Storage Optimized",
                    description: "Reduced stored posts to prevent storage overflow. Some older posts were removed."
                });
            } else {
                postsStorage.setItem(updatedPosts);
            }
            console.log(`💾 Updated post for brand ${currentBrand?.businessName || currentBrand?.name}`);
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Failed to update post",
                description: "Unable to save post updates. Your browser storage may be full."
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SidebarInset"], {
        fullWidth: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex h-14 items-center justify-between gap-4 border-b bg-card px-4 lg:h-[60px] lg:px-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                onClick: toggleSidebar,
                                className: "h-8 w-8",
                                title: sidebarOpen ? "Hide sidebar for full-screen mode" : "Show sidebar",
                                children: sidebarOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$close$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftClose$3e$__["PanelLeftClose"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 410,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftOpen$3e$__["PanelLeftOpen"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 412,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 402,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-muted-foreground",
                                children: sidebarOpen ? "Sidebar visible" : "Full-screen mode"
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 415,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/quick-content/page.tsx",
                        lineNumber: 401,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "secondary",
                                    size: "icon",
                                    className: "rounded-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                                    src: "https://placehold.co/40x40.png",
                                                    alt: "User",
                                                    "data-ai-hint": "user avatar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                                    lineNumber: 424,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {}, void 0, false, {
                                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                                        lineNumber: 425,
                                                        columnNumber: 33
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                                    lineNumber: 425,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/quick-content/page.tsx",
                                            lineNumber: 423,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Toggle user menu"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/quick-content/page.tsx",
                                            lineNumber: 427,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 422,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 421,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                align: "end",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                                        children: "My Account"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 431,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 432,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                        onClick: ()=>{
                                            if (postsStorage) {
                                                const cleaned = cleanupBrandScopedStorage(postsStorage);
                                                if (cleaned) {
                                                    setGeneratedPosts(cleaned);
                                                    toast({
                                                        title: "Storage Cleaned",
                                                        description: `Removed older posts for ${currentBrand?.businessName || currentBrand?.name}.`
                                                    });
                                                } else {
                                                    toast({
                                                        title: "Storage Clean",
                                                        description: "Storage is already optimized."
                                                    });
                                                }
                                            } else {
                                                toast({
                                                    variant: "destructive",
                                                    title: "No Brand Selected",
                                                    description: "Please select a brand first."
                                                });
                                            }
                                        },
                                        children: "Clear Old Posts"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 433,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 430,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/quick-content/page.tsx",
                        lineNumber: 420,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 400,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 overflow-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "min-h-full bg-gradient-to-br from-blue-50 to-indigo-100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container mx-auto px-4 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-7xl mx-auto",
                            children: isLoading || brandLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex w-full min-h-[300px] items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full max-w-3xl text-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "Loading Quick Content..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 470,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 469,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 468,
                                columnNumber: 17
                            }, this) : !currentBrand ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col items-center justify-center min-h-[400px] space-y-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-xl font-semibold",
                                        children: "Select a Brand"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 475,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-muted-foreground text-center",
                                        children: "Please select a brand to start generating content."
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 476,
                                        columnNumber: 19
                                    }, this),
                                    brands.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-wrap gap-2",
                                        children: brands.map((brand)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                onClick: ()=>selectBrand(brand),
                                                variant: "outline",
                                                children: brand.businessName || brand.name
                                            }, brand.id, false, {
                                                fileName: "[project]/src/app/quick-content/page.tsx",
                                                lineNumber: 482,
                                                columnNumber: 25
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 480,
                                        columnNumber: 21
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        onMouseEnter: ()=>router.prefetch('/brand-profile'),
                                        onFocus: ()=>router.prefetch('/brand-profile'),
                                        onClick: ()=>router.push('/brand-profile'),
                                        children: "Create Brand Profile"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 492,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 474,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: currentBrand && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$content$2d$calendar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentCalendar"], {
                                    brandProfile: {
                                        businessName: currentBrand.businessName,
                                        businessType: currentBrand.businessType || '',
                                        location: currentBrand.location || '',
                                        logoDataUrl: currentBrand.logoDataUrl || '',
                                        visualStyle: currentBrand.visualStyle || '',
                                        writingTone: currentBrand.writingTone || '',
                                        contentThemes: currentBrand.contentThemes || '',
                                        websiteUrl: currentBrand.websiteUrl || '',
                                        description: currentBrand.description || '',
                                        // Convert services array to newline-separated string to match BrandProfile.services
                                        services: Array.isArray(currentBrand.services) ? currentBrand.services.map((s)=>s.name).join('\n') : currentBrand.services || '',
                                        targetAudience: currentBrand.targetAudience || '',
                                        keyFeatures: currentBrand.keyFeatures || '',
                                        competitiveAdvantages: currentBrand.competitiveAdvantages || '',
                                        contactInfo: {
                                            phone: currentBrand.contactPhone || '',
                                            email: currentBrand.contactEmail || '',
                                            address: currentBrand.contactAddress || ''
                                        },
                                        socialMedia: {
                                            facebook: currentBrand.facebookUrl || '',
                                            instagram: currentBrand.instagramUrl || '',
                                            twitter: currentBrand.twitterUrl || '',
                                            linkedin: currentBrand.linkedinUrl || ''
                                        },
                                        primaryColor: currentBrand.primaryColor || undefined,
                                        accentColor: currentBrand.accentColor || undefined,
                                        backgroundColor: currentBrand.backgroundColor || undefined,
                                        designExamples: currentBrand.designExamples || []
                                    },
                                    posts: generatedPosts,
                                    onPostGenerated: handlePostGenerated,
                                    onPostUpdated: handlePostUpdated
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 514,
                                    columnNumber: 21
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 498,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/quick-content/page.tsx",
                            lineNumber: 466,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/quick-content/page.tsx",
                        lineNumber: 465,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/quick-content/page.tsx",
                    lineNumber: 464,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 463,
                columnNumber: 7
            }, this)
        ]
    }, currentBrand?.id || 'no-brand', true, {
        fileName: "[project]/src/app/quick-content/page.tsx",
        lineNumber: 399,
        columnNumber: 5
    }, this);
}
_s(QuickContentPage, "XqC3kjQzjISx6SZtGwvouTPd4gU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandStorage"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSidebar"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFirebaseAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGeneratedPosts"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c = QuickContentPage;
function QuickContentPageWithUnifiedBrand() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnifiedBrandLayout"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QuickContentPage, {}, void 0, false, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 566,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrandSwitchingStatus"], {}, void 0, false, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 567,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/quick-content/page.tsx",
        lineNumber: 565,
        columnNumber: 5
    }, this);
}
_c1 = QuickContentPageWithUnifiedBrand;
const __TURBOPACK__default__export__ = QuickContentPageWithUnifiedBrand;
var _c, _c1;
__turbopack_context__.k.register(_c, "QuickContentPage");
__turbopack_context__.k.register(_c1, "QuickContentPageWithUnifiedBrand");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_a59bd360._.js.map