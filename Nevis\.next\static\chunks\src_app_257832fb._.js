(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/actions.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/app/actions.ts
__turbopack_context__.s({});
"use turbopack no side effects";
;
;
;
;
;
;
;
}}),
"[project]/src/app/actions.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/app/data:40ce01 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60d74caca804adb2b547ce0cf49c6b4a219392377c":"analyzeBrandAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "analyzeBrandAction": (()=>analyzeBrandAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var analyzeBrandAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("60d74caca804adb2b547ce0cf49c6b4a219392377c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "analyzeBrandAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:ab8590 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"78f16784e7e21db600c083fc17664954c78d989188":"generateContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentAction": (()=>generateContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("78f16784e7e21db600c083fc17664954c78d989188", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:7d8916 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"707f98327e60040c1b2ee19b7969f1295904475702":"generateVideoContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateVideoContentAction": (()=>generateVideoContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateVideoContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("707f98327e60040c1b2ee19b7969f1295904475702", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateVideoContentAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:cb3929 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f79b801f803feb5e76e403a11c5c7baf170b29d06":"generateCreativeAssetAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f79b801f803feb5e76e403a11c5c7baf170b29d06", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:f5aeb7 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f491863d2325193ec3f40e1aa06031f7511fddf7d":"generateEnhancedDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateEnhancedDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f491863d2325193ec3f40e1aa06031f7511fddf7d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateEnhancedDesignAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:aa45b0 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f20bd895b2f9b19034d2b89ca4aad1c2c7dd696c8":"generateGeminiHDDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateGeminiHDDesignAction": (()=>generateGeminiHDDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateGeminiHDDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f20bd895b2f9b19034d2b89ca4aad1c2c7dd696c8", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateGeminiHDDesignAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:a7eaa4 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f2ccbac81b04d4b1a191d767822e0665844079bf0":"generateContentWithArtifactsAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentWithArtifactsAction": (()=>generateContentWithArtifactsAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateContentWithArtifactsAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f2ccbac81b04d4b1a191d767822e0665844079bf0", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentWithArtifactsAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/actions.ts [app-client] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeBrandAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$40ce01__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["analyzeBrandAction"]),
    "generateContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ab8590__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentAction"]),
    "generateContentWithArtifactsAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$a7eaa4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentWithArtifactsAction"]),
    "generateCreativeAssetAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$cb3929__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"]),
    "generateEnhancedDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$f5aeb7__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateEnhancedDesignAction"]),
    "generateGeminiHDDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$aa45b0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateGeminiHDDesignAction"]),
    "generateVideoContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$7d8916__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateVideoContentAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$40ce01__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:40ce01 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ab8590__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:ab8590 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$7d8916__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:7d8916 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$cb3929__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:cb3929 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$f5aeb7__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:f5aeb7 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$aa45b0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:aa45b0 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$a7eaa4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:a7eaa4 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/app/actions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeBrandAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["analyzeBrandAction"]),
    "generateContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateContentAction"]),
    "generateContentWithArtifactsAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateContentWithArtifactsAction"]),
    "generateCreativeAssetAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateCreativeAssetAction"]),
    "generateEnhancedDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateEnhancedDesignAction"]),
    "generateGeminiHDDesignAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateGeminiHDDesignAction"]),
    "generateVideoContentAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateVideoContentAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-client] (ecmascript) <exports>");
}}),
}]);

//# sourceMappingURL=src_app_257832fb._.js.map